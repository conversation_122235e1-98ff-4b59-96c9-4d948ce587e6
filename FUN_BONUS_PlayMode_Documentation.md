# FUN_BONUS Play Mode - Skywind Seamless Integration Guide

## Overview

The `FUN_BONUS` play mode is a new addition to Skywind's play mode system, designed to restrict gameplay to artificial currencies with the `funBonus` property only. This mode ensures no real money transactions while maintaining the same game mechanics as other play modes.

## Play Mode Types

Skywind supports the following play modes:

| Play Mode | Description | Currency Type | Real Money | Jackpot Support | History Support |
|-----------|-------------|---------------|------------|-----------------|-----------------|
| `REAL` | Standard real money gameplay | Real currencies | Yes | Yes | Yes |
| `PLAY_MONEY` | Demo/practice mode | Social currencies | No | No | No |
| `BNS` | Bonus coins gameplay | BNS currency | No | No | Yes |
| `FUN` | Fun mode gameplay | FUN currency | No | No | Yes |
| `FUN_BONUS` | **NEW** Fun bonus gameplay | Currencies with `funBonus: true` | No | No | Yes |

## FUN_BONUS Play Mode Details

### Key Features

- **Currency Restriction**: Only artificial currencies with `funBonus: true` property are allowed
- **Automatic Mode Detection**: Automatically switches to FUN_BONUS mode for currencies with `funBonus` property
- **Service Layer Integration**: Uses the same play service architecture with funBonus currency validation
- **Real Money Prevention**: Strict validation prevents any real money currency usage
- **Full Backward Compatibility**: Maintains compatibility with existing `PlayMode.REAL` implementations

### Implementation Architecture

#### 1. Currency-Level FunBonus Property

Currencies must be configured with the `funBonus: true` property in the currency exchange system:

```javascript
// Example currency configuration
{
  "name": "Fun USD",
  "iso": "FUNUSD", 
  "funBonus": true
}
```

#### 2. Automatic Mode Detection

The system automatically detects and switches to FUN_BONUS mode:

```typescript
public static getModeByCurrency(currency: string): GameMode {
    if (currency === BNS_CURRENCY) {
        return "bns";
    }
    if (Currencies.value(currency)?.funBonus) {
        return "fun_bonus";  // Automatic detection
    }
    return "real";
}
```

#### 3. Service Factory Pattern

The play service factory creates appropriate service instances:

```typescript
switch (playmode) {
    case PlayMode.BNS:
        playService = new BonusCoinsPlayService(operatorDetails, playService);
        break;
    case PlayMode.PLAY_MONEY:
        playService = new PlayMoneyMerchantPlayService(operatorDetails, playService);
        break;
    case PlayMode.FUN_BONUS:
        playService = new FunBonusMerchantPlayService(operatorDetails, playService);
        break;
}
```

#### 4. Currency Validation

Strict validation ensures only funBonus currencies are accepted:

```typescript
if (Currencies.value(currency)?.funBonus) {
    playMode = PlayMode.FUN_BONUS;
    startGameTokenData.playmode = playMode;
    if (urlParams.playmode) {
        urlParams.playmode = playMode;
    }
}
```

## API Integration

### Game URL Request

When requesting a game URL, the system will automatically detect funBonus currencies:

```http
GET /games/{gameCode}?playmode=fun_bonus
```

### Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| `playmode` | string | Play mode: "real", "play_money", "bns", "fun", "fun_bonus" | No |
| `currency` | string | Currency code (must have funBonus: true for FUN_BONUS mode) | Yes |

### Response

The game URL will include the appropriate play mode:

```json
{
  "url": "https://game.example.com/game?playmode=fun_bonus&token=...",
  "playMode": "fun_bonus"
}
```

## Currency Configuration

### Adding FunBonus Currencies

To add a new funBonus currency to the sw-currency-exchange system:

1. Define the currency with `funBonus: true` property
2. Configure exchange rates if needed
3. Deploy the currency configuration

Example:
```json
{
  "FUNUSD": {
    "name": "Fun USD",
    "iso": "FUNUSD",
    "funBonus": true,
    "symbol": "$",
    "precision": 2
  }
}
```

## Supported Features

### ✅ Supported Features
- Game mechanics (same as REAL mode)
- Player sessions
- Game history and round history
- Currency exchange (between funBonus currencies)
- Split payments (unlike BNS mode)
- Revert operations (unlike BNS mode)

### ❌ Unsupported Features
- Real money transactions
- Jackpot functionality
- Mixed currency gameplay (real + funBonus)

## Error Handling

### Common Errors

| Error | Description | Solution |
|-------|-------------|----------|
| Currency validation failed | Non-funBonus currency used in FUN_BONUS mode | Use only currencies with `funBonus: true` |
| Real money prevention | Attempt to use real currency | Switch to appropriate play mode |
| Service instantiation failed | FunBonusMerchantPlayService creation failed | Check operator configuration |

## Migration Guide

### From Existing Play Modes

No migration is required for existing implementations. The FUN_BONUS mode is additive and maintains full backward compatibility.

### Testing FUN_BONUS Mode

1. Configure a test currency with `funBonus: true`
2. Create a game session with the funBonus currency
3. Verify automatic mode detection
4. Test game functionality
5. Confirm no real money transactions are possible

## Best Practices

1. **Currency Configuration**: Always set `funBonus: true` for currencies intended for FUN_BONUS mode
2. **Validation**: Implement proper currency validation on the client side
3. **Error Handling**: Handle mode detection and currency validation errors gracefully
4. **Testing**: Thoroughly test the automatic mode detection functionality
5. **Documentation**: Update integration documentation when adding new funBonus currencies

## Support and Troubleshooting

For technical support regarding FUN_BONUS play mode implementation:

1. Check currency configuration in sw-currency-exchange
2. Verify play service factory configuration
3. Review game URL generation logs
4. Test with known working funBonus currencies
5. Contact Skywind technical support if issues persist

---

*This documentation should be integrated into the Skywind Seamless Integration Guide EU V.5.53 PDF in the appropriate play modes section.*
