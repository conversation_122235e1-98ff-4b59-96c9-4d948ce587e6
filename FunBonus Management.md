# FunBonus Management

```
Microgame S.p.A.
```
### TABLE OF CONTENTS

1.1 Fun Bonus Management ................................................................................................................... 3


## 1.1 FUN BONUS MANAGEMENT

```
The fun bonuses assigned by the operator can only be used in special game sessions. In these sessions,
the awarding of jackpots is allowed, powered exclusively by fun bonus, "real Jackpot" and "fun jackpot"
must have separate contributions. Or launching a fun bonus session, the game version must be without
jackpot.
```
```
Fun bonuses cannot be used in combination with real money and real bonus in the initial stake. This is in
charge to the operator.
```
```
Bets made using fun bonus, not allowing cash winnings, do not contribute to the calculation of the taxes.
Winning using Fun bonus bet, will always be fun bonus.
```
```
Fun Bonuses have a condition of conversion, that is the “wagering rule”. Fun Bonuses will be converted in
real bonuses if and only if the wagering rule has been satisfied.
```
```
Example:
```
```
The operator awards 10€ fun bonus with wagering 35. It means that the player must bet 10€X35 =350€
during fun bonus sessions, to convert Fun Bonus balance in Real bonus.
```
```
All fun bonus promotions and wagering is managed by the operator.
```
- In addition to the currently supported currency parameters in the game launch url, a new one must
    be made available by the game provider to communicate it that game is launched in a fun bonus
    session. For example, Currency=BNS
- Availability to have some addition parameters in the response of credit transaction to show a
    message inside the client:

```
"messageTitle": "Fun bonus",
```
```
"messageBody": "You won 100 real bonus from promo Example",
```
```
"messageBodyHtml":
```

```
"PHA+WW91IHdvbiAxMDAgcmVhbCBib251cyBmcm9tIHByb21vIEV4YW1wbGU8L3A+"
```
In this case a popup must be showed to the player using values of “messageTitle” and “messageBody” (or in
alternative of “messageBodyHtml”)

```
This message is triggered when the conversion threshold has been reached (internal by operator Platform)
```
```
The player should not be allowed to close the message and return to play in the game screen. The message
box must not have any buttons inside.
```

