{"swagger": "2.0", "info": {"description": "Skywind - API for Report", "version": "5.54", "title": "Skywind - API for Report"}, "basePath": "/v1", "produces": ["application/json"], "securityDefinitions": {"apiKey": {"description": "Basic JWT authorization", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-access-token", "in": "header"}, "Permissions": {"description": "Dummy OAuth2 authorization for permissions through scopes", "type": "oauth2", "authorizationUrl": "http://localhost:3000/oauth/dialog", "tokenUrl": "http://localhost:3000/oauth/token", "flow": "accessCode", "scopes": {"entity:game": "Manage game", "entity:game:history": "Get game history report", "report": "Get Entity brand report", "report:currency": "Get Entity brand currency report based on game rounds", "report:wallet-currency": "Get Entity brand currency report based on wallet data", "report:players": "Get Entity players report", "report:games": "Get Entity games daily report", "report:ggr": "Get Entity ggr total report", "report:jackpot": "Get Entity brand jackpot report", "report:jackpot:contributions": "Get Entity contributions to jackpots", "report:jackpot:contributions:logs": "Get Entity jackpot contribution logs", "report:jackpot:contributions:players": "Get Entity player's contribution to jackpots", "report:jackpot:contributions:wins": "Get Entity jackpot wins logs", "entity:external-game-provider:history": "Get external (game provider) history report", "keyentity:game": "Manage game", "keyentity:game:history": "Get game history report", "keyentity:report": "Get keyEntity brand report", "keyentity:report:currency": "Get keyEntity brand currency report", "keyentity:report:wallet-currency": "Get Entity brand currency report based on wallet data", "keyentity:report:players": "Get keyEntity players report", "keyentity:report:games": "Get keyEntity games daily report", "keyentity:report:ggr": "Get keyEntity ggr total report", "keyentity:report:jackpot": "Get keyEntity brand jackpot report", "keyentity:report:jackpot:contributions": "Get keyEntity contributions to jackpots", "keyentity:report:jackpot:contributions:logs": "Get keyEntity jackpot contribution logs", "keyentity:report:jackpot:contributions:players": "Get keyEntity player's contribution to jackpots", "keyentity:report:jackpot:contributions:wins": "Get keyEntity jackpot wins logs", "keyentity:external-game-provider:history": "Get external (game provider) history report"}}}, "paths": {"/entities/{path}/report/currency": {"get": {"security": [{"apiKey": []}, {"Permissions": ["report", "report:currency"]}], "tags": ["Reports"], "summary": "Gets currencies report by path", "description": "This method will return data for a limited time only (default = 3 months). The limit works by 'ts' field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/from"}, {"$ref": "#/parameters/to"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}], "responses": {"200": {"description": "Currencies report", "schema": {"$ref": "#/definitions/CurrencyReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/report/currency": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report", "keyentity:report:currency"]}], "tags": ["Reports"], "summary": "Gets currencies report for key entity", "description": "This method will return data for a limited time only (default = 3 months). The limit works by 'ts' field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/from"}, {"$ref": "#/parameters/to"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}], "responses": {"200": {"description": "Currencies report", "schema": {"$ref": "#/definitions/CurrencyReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/report/wallet/currency": {"get": {"security": [{"apiKey": []}, {"Permissions": ["report", "report:wallet-currency"]}], "tags": ["Reports"], "summary": "Gets currencies report by path", "description": "This method will return data for a limited time only (default = 3 months). The limit works by 'ts' field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/from"}, {"$ref": "#/parameters/to"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}], "responses": {"200": {"description": "Currencies report", "schema": {"$ref": "#/definitions/CurrencyReportNoGamesQty"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/report/wallet/currency": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report", "keyentity:report:wallet-currency"]}], "tags": ["Reports"], "summary": "Gets currencies report for key entity", "description": "This method will return data for a limited time only (default = 3 months). The limit works by 'ts' field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/from"}, {"$ref": "#/parameters/to"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}], "responses": {"200": {"description": "Currencies report", "schema": {"$ref": "#/definitions/CurrencyReportNoGamesQty"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/report/players": {"get": {"security": [{"apiKey": []}, {"Permissions": ["report", "report:players"]}], "tags": ["Reports"], "summary": "Gets players report for key entity by path", "description": "This method will return data for a limited time only (default = 3 months). The limit works by 'paymentDate', 'paymentDateHour' fields. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeContains"}, {"$ref": "#/parameters/playerCodeNotContains"}, {"$ref": "#/parameters/playerCodeIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeContains"}, {"$ref": "#/parameters/gameCodeNotContains"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/playedGames__gt"}, {"$ref": "#/parameters/playedGames__lt"}, {"$ref": "#/parameters/playedGames__gte"}, {"$ref": "#/parameters/playedGames__lte"}, {"$ref": "#/parameters/playedGames"}, {"$ref": "#/parameters/totalBets__gt"}, {"$ref": "#/parameters/totalBets__lt"}, {"$ref": "#/parameters/totalBets__gte"}, {"$ref": "#/parameters/totalBets__lte"}, {"$ref": "#/parameters/totalBets"}, {"$ref": "#/parameters/totalWins__gt"}, {"$ref": "#/parameters/totalWins__lt"}, {"$ref": "#/parameters/totalWins__gte"}, {"$ref": "#/parameters/totalWins__lte"}, {"$ref": "#/parameters/totalWins"}, {"$ref": "#/parameters/paymentDate"}, {"$ref": "#/parameters/paymentDate__gt"}, {"$ref": "#/parameters/paymentDate__gte"}, {"$ref": "#/parameters/paymentDate__lt"}, {"$ref": "#/parameters/paymentDate__lte"}, {"$ref": "#/parameters/paymentDateHour"}, {"$ref": "#/parameters/paymentDateHour__gt"}, {"$ref": "#/parameters/paymentDateHour__lt"}, {"$ref": "#/parameters/paymentDateHour__gte"}, {"$ref": "#/parameters/paymentDateHour__lte"}], "responses": {"200": {"description": "Players report", "schema": {"$ref": "#/definitions/PlayersReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/report/players": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report", "keyentity:report:players"]}], "tags": ["Reports"], "summary": "Gets players report for key entity", "description": "This method will return data for a limited time only (default = 3 months). The limit works by 'paymentDate', 'paymentDateHour' fields. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeContains"}, {"$ref": "#/parameters/playerCodeNotContains"}, {"$ref": "#/parameters/playerCodeIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeContains"}, {"$ref": "#/parameters/gameCodeNotContains"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/playedGames__gt"}, {"$ref": "#/parameters/playedGames__lt"}, {"$ref": "#/parameters/playedGames__gte"}, {"$ref": "#/parameters/playedGames__lte"}, {"$ref": "#/parameters/playedGames"}, {"$ref": "#/parameters/totalBets__gt"}, {"$ref": "#/parameters/totalBets__lt"}, {"$ref": "#/parameters/totalBets__gte"}, {"$ref": "#/parameters/totalBets__lte"}, {"$ref": "#/parameters/totalBets"}, {"$ref": "#/parameters/totalWins__gt"}, {"$ref": "#/parameters/totalWins__lt"}, {"$ref": "#/parameters/totalWins__gte"}, {"$ref": "#/parameters/totalWins__lte"}, {"$ref": "#/parameters/totalWins"}, {"$ref": "#/parameters/paymentDate"}, {"$ref": "#/parameters/paymentDate__gt"}, {"$ref": "#/parameters/paymentDate__gte"}, {"$ref": "#/parameters/paymentDate__lt"}, {"$ref": "#/parameters/paymentDate__lte"}, {"$ref": "#/parameters/paymentDateHour"}, {"$ref": "#/parameters/paymentDateHour__gt"}, {"$ref": "#/parameters/paymentDateHour__lt"}, {"$ref": "#/parameters/paymentDateHour__gte"}, {"$ref": "#/parameters/paymentDateHour__lte"}], "responses": {"200": {"description": "Players report", "schema": {"$ref": "#/definitions/PlayersReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/report/games/daily": {"get": {"security": [{"apiKey": []}, {"Permissions": ["report", "report:games"]}], "tags": ["Reports"], "summary": "Gets games daily report for key entity by path", "description": "Provides daily basis games report with aggregate amount of Players and Rounds with GGR. This method will return data for a limited time only (default = 3 months). The limit works by 'ts' field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeContains"}, {"$ref": "#/parameters/gameCodeNotContains"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/ts__lt"}], "responses": {"200": {"description": "Players report", "schema": {"$ref": "#/definitions/PlayersDailyReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/report/games/daily": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report", "keyentity:report:games"]}], "tags": ["Reports"], "summary": "Gets games daily report", "description": "Provides daily basis games report with aggregate amount of Players and Rounds with GGR. This method will return data for a limited time only (default = 3 months). The limit works by 'ts' field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeContains"}, {"$ref": "#/parameters/gameCodeNotContains"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/ts__lt"}], "responses": {"200": {"description": "Players report", "schema": {"$ref": "#/definitions/PlayersDailyReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/report/ggr": {"get": {"security": [{"apiKey": []}, {"Permissions": ["report", "report:ggr"]}], "tags": ["Reports"], "summary": "Gets ggr report by currencies by path", "description": "Provides common Gross Gaming Revenue report by currencies per Brand", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/currencyIn"}], "responses": {"200": {"description": "GGR report", "schema": {"$ref": "#/definitions/ReportGGR"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/report/ggr": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report", "keyentity:report:ggr"]}], "tags": ["Reports"], "summary": "Gets ggr report by currencies under the key entity", "description": "Provides common Gross Gaming Revenue report by currenciesunder the key entity", "parameters": [{"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/currencyIn"}], "responses": {"200": {"description": "GGR report", "schema": {"$ref": "#/definitions/ReportGGR"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/history/game": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:history"]}], "tags": ["History"], "summary": "Gets game history for the brand by path", "description": "This method will return data for a limited time only (default = 3 months). This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/roundIdStrictEquality"}, {"$ref": "#/parameters/roundIdIn"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeContains"}, {"$ref": "#/parameters/playerCodeNotContains"}, {"$ref": "#/parameters/playerCodeIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeContains"}, {"$ref": "#/parameters/gameCodeNotContains"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/firstTs"}, {"$ref": "#/parameters/firstTs__gt"}, {"$ref": "#/parameters/firstTs__gte"}, {"$ref": "#/parameters/firstTs__lt"}, {"$ref": "#/parameters/firstTs__lte"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/queryFinished"}, {"$ref": "#/parameters/bet"}, {"$ref": "#/parameters/bet__lt"}, {"$ref": "#/parameters/bet__lte"}, {"$ref": "#/parameters/bet__gt"}, {"$ref": "#/parameters/bet__gte"}, {"$ref": "#/parameters/win"}, {"$ref": "#/parameters/win__lt"}, {"$ref": "#/parameters/win__lte"}, {"$ref": "#/parameters/win__gt"}, {"$ref": "#/parameters/win__gte"}, {"$ref": "#/parameters/revenue"}, {"$ref": "#/parameters/revenue__lt"}, {"$ref": "#/parameters/revenue__lte"}, {"$ref": "#/parameters/revenue__gt"}, {"$ref": "#/parameters/revenue__gte"}, {"$ref": "#/parameters/device"}, {"$ref": "#/parameters/balanceBefore"}, {"$ref": "#/parameters/balanceBefore__lt"}, {"$ref": "#/parameters/balanceBefore__lte"}, {"$ref": "#/parameters/balanceBefore__gt"}, {"$ref": "#/parameters/balanceBefore__gte"}, {"$ref": "#/parameters/balanceAfter"}, {"$ref": "#/parameters/balanceAfter__lt"}, {"$ref": "#/parameters/balanceAfter__lte"}, {"$ref": "#/parameters/balanceAfter__gt"}, {"$ref": "#/parameters/balanceAfter__gte"}, {"$ref": "#/parameters/isTest"}], "responses": {"200": {"description": "Game history\n####Searchable fields:\n- roundId: Number,\n- playerCode: String,\n- gameCode: String,\n- currency: String,\n- firstTs: Number,\n- ts: Number,\n- finished: Boolean,\n- bet: Number,\n- win: Number,\n- revenue: Number;\n- balanceBefore: Number;\n- balanceAfter: Number;\n", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistory"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n- 907: Your request took too long time, please change your request\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/history/game/{roundId}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:history"]}], "tags": ["History"], "summary": "Gets round information for the brand by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/isPayment"}, {"name": "spinNumber", "in": "query", "description": "Spin number", "required": false, "type": "integer"}], "responses": {"200": {"description": "Spins of the round", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistorySpin"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/history/game/{roundId}/details/{spinNumber}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:history"]}], "tags": ["History"], "summary": "Get game event details by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roundId"}, {"name": "spinNumber", "in": "path", "description": "Spin number", "required": true, "type": "integer"}], "responses": {"200": {"description": "Spins of the round", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistoryDetails"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 683: Game history details not found\n"}}}}, "/entities/{path}/history/game/{roundId}/details/{spinNumber}/image": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:history"]}], "tags": ["History"], "summary": "Get game event details visualisation link by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/spinNumber"}, {"$ref": "#/parameters/languageQuery"}], "responses": {"200": {"description": "Link to the spin details visualisation", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistoryVisualization"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 683: Game history details not found\n"}}}}, "/history/game": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:history"]}], "tags": ["History"], "summary": "Gets game history for the key entity", "description": "This method will return data for a limited time only (default = 3 months). This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/roundIdStrictEquality"}, {"$ref": "#/parameters/roundIdIn"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeContains"}, {"$ref": "#/parameters/playerCodeNotContains"}, {"$ref": "#/parameters/playerCodeIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeContains"}, {"$ref": "#/parameters/gameCodeNotContains"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/firstTs"}, {"$ref": "#/parameters/firstTs__gt"}, {"$ref": "#/parameters/firstTs__gte"}, {"$ref": "#/parameters/firstTs__lt"}, {"$ref": "#/parameters/firstTs__lte"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/queryFinished"}, {"$ref": "#/parameters/bet"}, {"$ref": "#/parameters/bet__lt"}, {"$ref": "#/parameters/bet__lte"}, {"$ref": "#/parameters/bet__gt"}, {"$ref": "#/parameters/bet__gte"}, {"$ref": "#/parameters/win"}, {"$ref": "#/parameters/win__lt"}, {"$ref": "#/parameters/win__lte"}, {"$ref": "#/parameters/win__gt"}, {"$ref": "#/parameters/win__gte"}, {"$ref": "#/parameters/revenue"}, {"$ref": "#/parameters/revenue__lt"}, {"$ref": "#/parameters/revenue__lte"}, {"$ref": "#/parameters/revenue__gt"}, {"$ref": "#/parameters/revenue__gte"}, {"$ref": "#/parameters/device"}, {"$ref": "#/parameters/balanceBefore"}, {"$ref": "#/parameters/balanceBefore__lt"}, {"$ref": "#/parameters/balanceBefore__lte"}, {"$ref": "#/parameters/balanceBefore__gt"}, {"$ref": "#/parameters/balanceBefore__gte"}, {"$ref": "#/parameters/balanceAfter"}, {"$ref": "#/parameters/balanceAfter__lt"}, {"$ref": "#/parameters/balanceAfter__lte"}, {"$ref": "#/parameters/balanceAfter__gt"}, {"$ref": "#/parameters/balanceAfter__gte"}, {"$ref": "#/parameters/isTest"}], "responses": {"200": {"description": "Game history\n####Searchable fields:\n- roundId: Number,\n- playerCode: String,\n- gameCode: String,\n- currency: Array,\n- from: Number,\n- to: Number,\n- finished: Boolean,\n- bet: Number,\n- win: Number,\n- revenue: Number;\n", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistory"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 907: Your request took too long time, please change your request\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/history/game/{roundId}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:history"]}], "tags": ["History"], "summary": "Gets round information for the key entity", "parameters": [{"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/isPayment"}, {"name": "spinNumber", "in": "query", "description": "Spin number", "required": false, "type": "integer"}], "responses": {"200": {"description": "Spins of the round", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistorySpin"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/history/game/{roundId}/details/{spinNumber}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:history"]}], "tags": ["History"], "summary": "Get game event details", "parameters": [{"$ref": "#/parameters/roundId"}, {"name": "spinNumber", "in": "path", "description": "Spin number", "required": true, "type": "integer"}], "responses": {"200": {"description": "Spins of the round", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistoryDetails"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 683: Game history details not found\n"}}}}, "/history/game/{roundId}/details/{spinNumber}/image": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:history"]}], "tags": ["History"], "summary": "Get game event details visualisation link", "parameters": [{"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/spinNumber"}, {"$ref": "#/parameters/languageQuery"}], "responses": {"200": {"description": "Link to the spin details visualisation", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistoryVisualization"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 683: Game history details not found\n"}}}}, "/history/external": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:external-game-provider:history"]}], "tags": ["History"], "summary": "Get external win/bet history", "description": "This method will return data for a limited time only (default = 3 months). The limit works by 'insertedAt' field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/currencyInQuery"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/roundIdInQuery"}, {"$ref": "#/parameters/extTrxId"}, {"$ref": "#/parameters/gameProviderCode"}, {"$ref": "#/parameters/insertedAt"}, {"$ref": "#/parameters/insertedAt__gt"}, {"$ref": "#/parameters/insertedAt__lt"}, {"$ref": "#/parameters/playerCodeInQuery"}, {"$ref": "#/parameters/gameCodeInQuery"}, {"$ref": "#/parameters/isTest"}], "responses": {"200": {"description": "Return external history", "schema": {"type": "array", "items": {"$ref": "#/definitions/ExtWinBetHistoryEntry"}}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> is expired\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/entities/{path}/history/spins": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:history"]}], "tags": ["History"], "summary": "Gets game spins history for the brand by path", "description": "The method returns game history items for the brand by its path. Default output is limited to 30 entries per page. Max output is 1000 entries per page. This method will return data for a limited time only (default = 3 months). The limit works by \"insertedAt\", \"ts\" fields. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/insertedAt__gt"}, {"$ref": "#/parameters/insertedAt__lt"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/spinTypeStrictEquality"}, {"$ref": "#/parameters/spinTypeIn"}], "responses": {"200": {"description": "Game history\n####Searchable fields:\n- brandId: Number,\n- roundId: Number,\n- playerCode: String,\n- gameCode: String,\n- currency: String,\n- firstTs: Number,\n- ts: Number,\n- finished: Boolean,\n- bet: Number,\n- win: Number,\n- revenue: Number,\n- isTest: Boolean,\n- recoveryType: String\n", "schema": {"type": "array", "items": {"$ref": "#/definitions/OperationsHistory"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/history/spins": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:history"]}], "tags": ["History"], "summary": "Gets game spins history for the brand", "description": "The method returns game history items for the brand by its path. Default output is limited to 30 entries per page. Max output is 1000 entries per page. This method will return data for a limited time only (default = 3 months). The limit works by \"insertedAt\", \"ts\" fields. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/insertedAt__gt"}, {"$ref": "#/parameters/insertedAt__lt"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/spinTypeStrictEquality"}, {"$ref": "#/parameters/spinTypeIn"}], "responses": {"200": {"description": "Game history\n####Searchable fields:\n- brandId: Number,\n- roundId: Number,\n- playerCode: String,\n- gameCode: String,\n- currency: String,\n- firstTs: Number,\n- ts: Number,\n- finished: Boolean,\n- bet: Number,\n- win: Number,\n- revenue: Number,\n- isTest: Boolean,\n- recoveryType: String\n", "schema": {"type": "array", "items": {"$ref": "#/definitions/OperationsHistory"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/history/external": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:external-game-provider:history"]}], "tags": ["History"], "summary": "Get external win/bet history by path", "description": "This method will return data for a limited time only (default = 3 months). The limit works by 'insertedAt' field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/currencyInQuery"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/roundIdInQuery"}, {"$ref": "#/parameters/extTrxId"}, {"$ref": "#/parameters/gameProviderCode"}, {"$ref": "#/parameters/insertedAt"}, {"$ref": "#/parameters/insertedAt__gt"}, {"$ref": "#/parameters/insertedAt__lt"}, {"$ref": "#/parameters/playerCodeInQuery"}, {"$ref": "#/parameters/gameCodeInQuery"}, {"$ref": "#/parameters/isTest"}], "responses": {"200": {"description": "Return history", "schema": {"type": "array", "items": {"$ref": "#/definitions/ExtWinBetHistoryEntry"}}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> is expired\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/history/external/{roundId}/details": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:external-game-provider:history"]}], "tags": ["History"], "summary": "Get external win/bet history details", "parameters": [{"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/gameProviderCodeRequired"}, {"$ref": "#/parameters/extTrxIdRequired"}], "responses": {"200": {"description": "Return history", "schema": {"$ref": "#/definitions/ExtHistoryDetailsResponse"}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> is expired\n"}, "404": {"description": "- 2000: History details url is not present\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/entities/{path}/history/external/{roundId}/details": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:external-game-provider:history"]}], "tags": ["History"], "summary": "Get external win/bet history details by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/gameProviderCodeRequired"}, {"$ref": "#/parameters/extTrxIdRequired"}], "responses": {"200": {"description": "Return history", "schema": {"$ref": "#/definitions/ExtHistoryDetailsResponse"}}, "400": {"description": "- 11: <PERSON><PERSON> is missing\n- 12: <PERSON><PERSON> is not valid\n- 13: <PERSON><PERSON> is expired\n"}, "404": {"description": "- 2000: History details url is not present\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/report/jackpot/contributions": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report", "keyentity:report:jackpot", "keyentity:report:jackpot:contributions"]}], "tags": ["Reports Jackpot"], "summary": "Gets contributions to jackpots for key entity", "description": "Method returns list contributions to jackpot by games. All amounts converted to euros. Sortable fields are \"dateHour\", \"gameCode\", \"seedAmount\", \"progressiveAmount\", \"totalBetAmount\", \"jpWinAmount\", \"totalBetCount\", \"jpWinCount\", \"firstActivity\", \"lastActivity\". This method will return data for a limited time only (default = 3 months). The limit works by \"dateHour\" field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/dateHour"}, {"$ref": "#/parameters/dateHour__gt"}, {"$ref": "#/parameters/dateHour__lt"}, {"$ref": "#/parameters/dateHour__gte"}, {"$ref": "#/parameters/dateHour__lte"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeIn"}], "responses": {"200": {"description": "Game history", "schema": {"type": "array", "items": {"$ref": "#/definitions/JpContribution"}}}, "400": {"description": "- 40: Validation error\n - 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 907: Your request took too long time, please change your request\n"}}}}, "/entities/{path}/report/jackpot/contributions": {"get": {"security": [{"apiKey": []}, {"Permissions": ["report", "report:jackpot", "report:jackpot:contributions"]}], "tags": ["Reports Jackpot"], "summary": "Gets contributions to jackpots by path", "description": "Method returns list contributions to jackpot by games. All amounts converted to euros. Sortable fields are \"dateHour\", \"gameCode\", \"seedAmount\", \"progressiveAmount\", \"totalBetAmount\", \"jpWinAmount\", \"totalBetCount\", \"jpWinCount\", \"firstActivity\", \"lastActivity\". This method will return data for a limited time only (default = 3 months). The limit works by \"dateHour\" field. This restriction does not work if you have 'report-without-limit' permission. This method will return data for a limited time only (default = 3 months). The limit works by \"dateHour\" field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/dateHour"}, {"$ref": "#/parameters/dateHour__gt"}, {"$ref": "#/parameters/dateHour__lt"}, {"$ref": "#/parameters/dateHour__gte"}, {"$ref": "#/parameters/dateHour__lte"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeIn"}], "responses": {"200": {"description": "Game history", "schema": {"type": "array", "items": {"$ref": "#/definitions/JpContribution"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 907: Your request took too long time, please change your request\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/report/jackpot/contributions/players": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report", "keyentity:report:jackpot", "keyentity:report:jackpot:contributions", "keyentity:report:jackpot:contributions:players"]}], "tags": ["Reports Jackpot"], "summary": "Gets players' contribution to jackpots for key entity", "description": "Method returns list of players with their contributions to games jackpots. All amounts in euros and player's currency. Sortable fields are \"playerCode\", \"gameCode\", \"seedAmount\", \"progressiveAmount\", \"totalBetAmount\", \"jpWinAmount\", \"seedAmountJpCurrency\", \"progressiveAmountJpCurrency\", \"totalBetAmountJpCurrency\", \"jpWinAmountJpCurrency\", \"totalBetCount\", \"jpWinCount\", \"firstActivity\", \"lastActivity\". This method will return data for a limited time only (default = 3 months). The limit works by \"dateHour\" field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/dateHour"}, {"$ref": "#/parameters/dateHour__gt"}, {"$ref": "#/parameters/dateHour__lt"}, {"$ref": "#/parameters/dateHour__gte"}, {"$ref": "#/parameters/dateHour__lte"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}], "responses": {"200": {"description": "Game history", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerJpContribution"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 907: Your request took too long time, please change your request\n"}}}}, "/entities/{path}/report/jackpot/contributions/players": {"get": {"security": [{"apiKey": []}, {"Permissions": ["report", "report:jackpot", "report:jackpot:contributions", "report:jackpot:contributions:players"]}], "tags": ["Reports Jackpot"], "summary": "Gets players' contribution to jackpots by path", "description": "Method returns list of players with their contributions to games jackpots. All amounts in euros and player's currency. Sortable fields are \"dateHour\", \"playerCode\", \"gameCode\", \"seedAmount\", \"progressiveAmount\", \"totalBetAmount\", \"jpWinAmount\", \"seedAmountJpCurrency\", \"progressiveAmountJpCurrency\", \"totalBetAmountJpCurrency\", \"jpWinAmountJpCurrency\", \"totalBetCount\", \"jpWinCount\", \"firstActivity\", \"lastActivity\". This method will return data for a limited time only (default = 3 months). The limit works by \"dateHour\" field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/dateHour"}, {"$ref": "#/parameters/dateHour__gt"}, {"$ref": "#/parameters/dateHour__lt"}, {"$ref": "#/parameters/dateHour__gte"}, {"$ref": "#/parameters/dateHour__lte"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}], "responses": {"200": {"description": "Game history", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerJpContribution"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 907: Your request took too long time, please change your request\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/report/jackpot/contributions/logs": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report", "keyentity:report:jackpot", "keyentity:report:jackpot:contributions", "keyentity:report:jackpot:contributions:logs"]}], "tags": ["Reports Jackpot"], "summary": "Gets jackpot contribution logs for key entity by path", "description": "Method returns list of jackpot contribution logs sorted by trxDate. This method will return data for a limited time only (default = 3 months). The limit works by \"trxDate\" field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/trxDate__gt"}, {"$ref": "#/parameters/trxDate__lt"}], "responses": {"200": {"description": "JP contribution log", "schema": {"type": "array", "items": {"$ref": "#/definitions/JpContributionLog"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 907: Your request took too long time, please change your request\n"}}}}, "/entities/{path}/report/jackpot/contributions/logs": {"get": {"security": [{"apiKey": []}, {"Permissions": ["report", "report:jackpot", "report:jackpot:contributions", "report:jackpot:contributions:logs"]}], "tags": ["Reports Jackpot"], "summary": "Gets jackpot contribution logs by path", "description": "Method returns list of jackpot contribution logs sorted by trxDate. This method will return data for a limited time only (default = 3 months). The limit works by \"trxDate\" field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/trxDate__gt"}, {"$ref": "#/parameters/trxDate__lt"}], "responses": {"200": {"description": "JP contribution log", "schema": {"type": "array", "items": {"$ref": "#/definitions/JpContributionLog"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 907: Your request took too long time, please change your request\n"}}}}, "/report/jackpot/contributions/wins": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report", "keyentity:report:jackpot", "keyentity:report:jackpot:contributions", "keyentity:report:jackpot:contributions:wins"]}], "tags": ["Reports Jackpot"], "summary": "Gets jackpot wins logs for key entity", "description": "Method returns list of jackpot wins logs sorted by trxDate. This method will return data for a limited time only (default = 3 months). The limit works by \"trxDate\" field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/trxDate__gt"}, {"$ref": "#/parameters/trxDate__lt"}], "responses": {"200": {"description": "JP win logs", "schema": {"type": "array", "items": {"$ref": "#/definitions/JpWinLog"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/report/jackpot/contributions/wins": {"get": {"security": [{"apiKey": []}, {"Permissions": ["report", "report:jackpot", "report:jackpot:contributions", "report:jackpot:contributions:wins"]}], "tags": ["Reports Jackpot"], "summary": "Gets jackpot wins logs by path", "description": "Method returns list of jackpot wins logs sorted by trxDate. This method will return data for a limited time only (default = 3 months). The limit works by \"trxDate\" field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/trxDate__gt"}, {"$ref": "#/parameters/trxDate__lt"}], "responses": {"200": {"description": "JP win logs", "schema": {"type": "array", "items": {"$ref": "#/definitions/JpWinLog"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/health": {"get": {"tags": ["Health"], "summary": "Checks server health", "responses": {"200": {"description": "Health check OK"}}}}, "/version": {"get": {"tags": ["Version"], "summary": "Checks service version", "responses": {"200": {"description": "Returns verion, revision and time of build", "schema": {"type": "string", "example": "1.1.1 6ca78adf 01.01.1970 00:00:00"}}}}}}, "definitions": {"Entity": {"type": "object", "required": ["type", "name", "status", "key", "defaultCurrency", "defaultCountry", "defaultLanguage"], "properties": {"type": {"type": "string", "description": "type of item", "example": "entity"}, "name": {"type": "string", "description": "name", "example": "ENTITY1"}, "description": {"type": "string", "description": "description of item", "example": "Entity Description"}, "title": {"type": "string", "description": "Title of item", "example": "Main entity"}, "status": {"type": "string", "description": "status of item (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "key": {"type": "string", "description": "key for this item", "example": "A-KEY-FOR-THIS-ENTITY"}, "defaultCurrency": {"type": "string", "description": "default currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "defaultCountry": {"type": "string", "description": "default country code [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "example": "US"}, "defaultLanguage": {"type": "string", "description": "default language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "example": "en"}, "countries": {"type": "array", "description": "list of available countries codes [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "items": {"type": "string"}, "example": ["US", "CN"]}, "currencies": {"type": "array", "description": "list of available currencies codes [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "items": {"type": "string"}, "example": ["USD", "CNY"]}, "child": {"type": "array", "items": {"$ref": "#/definitions/Entity"}}, "languages": {"type": "array", "description": "list of available languages codes [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "items": {"type": "string"}, "example": ["en", "zh"]}, "path": {"type": "string", "description": "path of this item", "example": "MASTER:TOP"}, "isMerchant": {"type": "boolean", "description": "indicates if brand is merchant", "example": false}}}, "Error": {"type": "object", "properties": {"code": {"type": "integer", "description": "error code", "example": 60}, "message": {"type": "string", "description": "error message", "example": "Entity already exis"}}}, "CurrencyReport": {"type": "array", "items": {"type": "object", "required": ["currency", "playedGames"], "properties": {"currency": {"type": "string", "description": "Currency code", "example": "USD"}, "playedGames": {"type": "integer", "description": "Number of games played", "example": 112503}, "bets": {"type": "number", "description": "Sum of all bets", "example": 256432.12}, "winnings": {"type": "number", "description": "Sum of all winnings", "example": 548123.34}, "ggr": {"type": "number", "description": "Gross Gambling Revenue (bets-winnings)", "example": 804555.46}, "betsUsd": {"type": "number", "description": "Sum of all bets in USD", "example": 256432.12}, "winningsUsd": {"type": "number", "description": "Sum of all winnings in USD", "example": 548123.34}, "ggrUsd": {"type": "number", "description": "Gross Gambling Revenue in USD", "example": 804555.46}}}}, "CurrencyReportNoGamesQty": {"type": "array", "items": {"type": "object", "required": ["currency", "playedGames"], "properties": {"currency": {"type": "string", "description": "Currency code", "example": "USD"}, "bets": {"type": "number", "description": "Sum of all bets", "example": 256432.12}, "winnings": {"type": "number", "description": "Sum of all winnings", "example": 548123.34}, "ggr": {"type": "number", "description": "Gross Gambling Revenue (bets-winnings)", "example": 804555.46}, "betsUsd": {"type": "number", "description": "Sum of all bets in USD", "example": 256432.12}, "winningsUsd": {"type": "number", "description": "Sum of all winnings in USD", "example": 548123.34}, "ggrUsd": {"type": "number", "description": "Gross Gambling Revenue in USD", "example": 804555.46}}}}, "PlayersReport": {"type": "array", "items": {"type": "object", "properties": {"playerCode": {"type": "string", "description": "Player code", "example": "PL0001"}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}, "playedGames": {"type": "number", "description": "Count of played games", "example": 27182}, "totalBets": {"type": "number", "description": "Sum of the bets", "example": 1216851546}, "totalWins": {"type": "number", "description": "<PERSON><PERSON> of the wins", "example": 2851546}, "totalJpWins": {"type": "number", "description": "<PERSON><PERSON> of the jackpot wins", "example": 15000}, "totalFreebetWins": {"type": "number", "description": "<PERSON><PERSON> of the freebet wins", "example": 2300}, "GGR": {"type": "number", "description": "Resulting GGR", "example": 1546}, "RTP": {"type": "number", "description": "Resulting RTP", "example": 0.93123741241}, "debits": {"type": "number", "description": "Sum of the debits", "example": 1500}, "credits": {"type": "number", "description": "Sum of the credits", "example": 10000}}}}, "PlayersDailyReport": {"type": "array", "items": {"type": "object", "properties": {"day": {"type": "string", "description": "day", "example": "2017/08/15"}, "gameCode": {"type": "string", "description": "Game code", "example": "Pl00x0304ExtBr07"}, "rounds": {"type": "number", "description": "Count of played rounds", "example": 105865}, "players": {"type": "number", "description": "Count of unique players", "example": 1395}, "bet": {"type": "number", "description": "Sum of the bets", "example": 120096}, "win": {"type": "number", "description": "<PERSON><PERSON> of the wins", "example": 273146}, "revenue": {"type": "number", "description": "Resulting GGR", "example": 1546}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}}}}, "ReportGGR": {"type": "array", "items": {"type": "object", "properties": {"brandId": {"type": "string", "description": "Brand public id", "example": "U98S67D4"}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}, "bet": {"type": "number", "description": "Sum of the bets", "example": 6723543}, "win": {"type": "number", "description": "<PERSON><PERSON> of the wins", "example": 4545677}, "revenue": {"type": "number", "description": "Resulting Gross Gaming Revenue", "example": 2177866}, "jackpotWin": {"type": "number", "description": "Total jackpot wins", "example": 2177866}, "freeBetWin": {"type": "number", "description": "Total free bet wins", "example": 2177866}}}}, "GameHistory": {"type": "object", "properties": {"roundId": {"type": "string", "description": "Round public id", "example": "feE3Sb39"}, "brandId": {"type": "string", "description": "Brand public id", "example": "feE3Sb39"}, "playerCode": {"type": "string", "description": "Player code", "example": "PLAYER1"}, "gameCode": {"type": "string", "description": "Game code", "example": "sw_mrmnky"}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}, "bet": {"type": "number", "description": "Total bet", "example": 0.1}, "win": {"type": "number", "description": "Total winning", "example": 2}, "revenue": {"type": "number", "description": "Revenue", "example": -1.9}, "firstTs": {"type": "string", "description": "time of first action in round", "example": "2017-07-14T07:07:01.080Z"}, "ts": {"type": "string", "description": "time of last action in round", "example": "2017-07-14T07:07:11.930Z"}, "finished": {"type": "boolean", "description": "Whether the round has ended", "example": true}, "isTest": {"type": "boolean", "description": "Whether the round has been created only for testing", "example": false}, "balanceBefore": {"type": "number", "description": "Player's balance before round", "example": 25000}, "balanceAfter": {"type": "number", "description": "Player's balance after round", "example": 24950}, "device": {"type": "string", "description": "Player's device", "example": "web"}, "credit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 10}, "debit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 5}, "recoveryType": {"type": "string", "enum": ["revert", "force-finish", "finalize"], "description": "Recovery type for rounds that were resolved manually", "example": "force-finish"}, "extraData": {"type": "object", "properties": {"extRoundId": {"type": "string", "description": "PT round id", "example": "extRound1"}}}}}, "GameHistorySpin": {"type": "object", "required": ["roundId", "spinNumber", "view", "type", "currency", "bet", "win", "endOfRound"], "properties": {"roundId": {"type": "integer", "description": "Round public id", "example": "feE3Sb39"}, "spinNumber": {"type": "integer", "description": "Spin number", "example": 200000134}, "type": {"type": "string", "description": "History item type", "example": "slot"}, "view": {"type": "string", "description": "Result view", "example": [[2, 12, 7, 4, 3], [5, 12, 1, 13, 4], [1, 2, 5, 2, 1], [3, 6, 4, 6, 7]]}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}, "bet": {"type": "number", "description": "Total bet", "example": 0.1}, "win": {"type": "number", "description": "Total winning", "example": 1.5}, "endOfRound": {"type": "boolean", "description": "Whether this spin was at the end of the round", "example": true}, "ts": {"type": "string", "description": "Time of spin (ISO 8601 timestamp)", "example": "2017-02-16T16:37:13.613Z"}, "test": {"type": "boolean", "description": "Whether the round has been created only for testing", "example": false}, "credit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 10}, "debit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 5}}}, "GameHistoryDetails": {"type": "object", "required": ["roundId", "spinNumber", "gameId", "gameVersion"], "properties": {"roundId": {"type": "integer", "description": "Round public id ", "example": "feE3Sb39"}, "spinNumber": {"type": "integer", "description": "Spin number", "example": 200000134}, "gameId": {"type": "string", "description": "Game module id", "example": "sw_gol"}, "gameVersion": {"type": "string", "description": "Game module version", "example": "0.1.1"}, "details": {"type": "string", "description": "Game event details", "example": "{data: \"Some spin data\"}"}, "initSettings": {"type": "string", "description": "Game init settings", "example": "{data: \"Some game init information\"}"}, "credit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 10}, "debit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 5}}}, "GameHistoryVisualization": {"type": "object", "required": ["imageUrl"], "properties": {"imageUrl": {"type": "string", "description": "Game event details visualisation link", "example": "http://example.com/gamehistory/0.0.1/history.html?data=bhESknjk578.eyJlSWQiJZCI6Njk1NzCwiiZXhwIjoxNTMwNzgzNNJKNCDCjYzLCSSvdXAifQ&url=site.com&language=en"}, "ttl": {"type": "number", "description": "Token is expires in this time (in seconds)", "example": 3600}}}, "PlayerJpContribution": {"type": "object", "properties": {"dateHour": {"type": "string", "description": "Aggregated period", "example": "2018-01-03T17:00:00.000Z"}, "brandId": {"type": "string", "description": "brand id", "example": "hzO8Gsb"}, "playerCode": {"type": "string", "description": "Player code", "example": "PL10032MOD01T2GROUP03"}, "gameCode": {"type": "string", "description": "Game code", "example": "pt-allad-reel-jp"}, "currency": {"type": "string", "description": "Currency code", "example": "CNY"}, "jackpotId": {"type": "string", "description": "Jackpot id", "example": "OMQ-JP-TWO"}, "pool": {"type": "string", "description": "Jackpot poolname in jackpotId context", "example": "Middle-amount"}, "seedAmount": {"type": "number", "description": "Amount, contributed to the seed part of the JP (player's currency)", "example": 12.5}, "progressiveAmount": {"type": "number", "description": "Amount, contributed to the progressive part of the JP (player's currency)", "example": 6.5}, "totalBetAmount": {"type": "number", "description": "Full bet amount of game stakes used for the Jackpot replenishment (player's currency)", "example": 6.5}, "jpWinAmount": {"type": "number", "description": "Jackpot winning amount (player's currency)", "example": 15146.6}, "jpCurrency": {"type": "string", "description": "JP currency code", "example": "EUR"}, "seedAmountJpCurrency": {"type": "number", "description": "Amount, contributed to the seed part of the JP (JP currency)", "example": 12.5}, "progressiveAmountJpCurrency": {"type": "number", "description": "Amount, contributed to the progressive part of the JP (JP currency)", "example": 6.5}, "totalBetAmountJpCurrency": {"type": "number", "description": "Full bet amount of game stakes used for the Jackpot replenishment (JP currency)", "example": 6.5}, "jpWinAmountJpCurrency": {"type": "number", "description": "Jackpot winning amount (JP currency)", "example": 15146.6}, "totalBetCount": {"type": "integer", "description": "Total count of bets used for the Jackpot replenishment", "example": 120}, "jpWinCount": {"type": "integer", "description": "Total count of Jackpot wins", "example": 2}, "firstActivity": {"type": "string", "description": "First activity (contribution or win) within aggregated period", "example": "2018-01-03T17:10:01.020Z"}, "lastActivity": {"type": "string", "description": "Last activity (contribution or win) within aggregated period", "example": "2018-01-03T17:10:29.854Z"}, "seedWin": {"type": "number", "description": "Amount, contributed to the seed part of the JP", "example": -560}, "progressiveWin": {"type": "number", "description": "Amount, contributed to the progressive part of the JP", "example": 10000}}}, "JpContribution": {"type": "object", "properties": {"dateHour": {"type": "string", "description": "Aggregated period", "example": "2018-01-03T17:00:00.000Z"}, "brandId": {"type": "string", "description": "Brand id", "example": "jk2j3ncD8"}, "gameCode": {"type": "string", "description": "Game code", "example": "pt-allad-reel-jp"}, "currency": {"type": "string", "description": "JP currency code", "example": "USD"}, "seedAmount": {"type": "number", "description": "Amount, contributed to the seed part of the JP (JP currency)", "example": 12.5}, "progressiveAmount": {"type": "number", "description": "Amount, contributed to the progressive part of the JP (JP currency)", "example": 6.5}, "totalBetAmount": {"type": "number", "description": "Full bet amount of game stakes used for the Jackpot replenishment (JP currency)", "example": 6.5}, "jpWinAmount": {"type": "number", "description": "Jackpot winning amount", "example": 15146.6}, "totalBetCount": {"type": "integer", "description": "Total count of bets used for the Jackpot replenishment", "example": 120}, "jpWinCount": {"type": "integer", "description": "Total count of Jackpot wins", "example": 2}, "firstActivity": {"type": "string", "description": "First activity (contribution or win) within aggregated period", "example": "2018-01-03T17:10:01.020Z"}, "lastActivity": {"type": "string", "description": "Last activity (contribution or win) within aggregated period", "example": "2018-01-03T17:10:29.854Z"}, "seedWin": {"type": "number", "description": "Amount, contributed to the seed part of the JP", "example": -560}, "progressiveWin": {"type": "number", "description": "Amount, contributed to the progressive part of the JP", "example": 10000}}}, "JpContributionLog": {"type": "object", "properties": {"gameCode": {"type": "string", "description": "Game code", "example": "pt-allad-reel-jp"}, "roundId": {"type": "string", "description": "Round ID", "example": "ni9Gav0"}, "seedAmount": {"type": "number", "description": "Amount, contributed to the seed part of the JP", "example": 12.5}, "progressiveAmount": {"type": "number", "description": "Amount, contributed to the progressive part of the JP", "example": 6.5}, "contributionAmount": {"type": "number", "description": "Full bet amount of game stakes used for the Jackpot replenishment (in EUR)", "example": 6.5}, "currency": {"type": "string", "description": "currency", "example": "USD"}, "currencyRate": {"type": "number", "description": "currency rate", "example": 0.92123}, "insertedAt": {"type": "string", "description": "Inserted at", "example": "2018-01-03T17:00:00.000Z"}, "playerCode": {"type": "string", "description": "player code", "example": "test"}, "pool": {"type": "string", "description": "pool of contribute", "example": "mega"}, "trxDate": {"type": "string", "description": "transaction date", "example": "2018-01-03T17:00:00.000Z"}, "jackpotId": {"type": "string", "description": "jackpot id name", "example": "FIRE-reel"}}}, "JpWinInfoExternal": {"type": "object", "properties": {"externalId": {"type": "string", "description": "External Jackpot Id", "example": "FIRE-reel_hash1"}, "externalStartDate": {"type": "string", "description": "External Jackpot Start Date", "example": "2023-10-17T17:00:00.000Z"}}}, "JpWinInfo": {"type": "object", "properties": {"external": {"$ref": "#/definitions/JpWinInfoExternal"}}}, "JpWinLog": {"type": "object", "properties": {"playerCurrency": {"type": "string", "description": "Player Currency", "example": "EUR"}, "roundId": {"type": "string", "description": "Round ID", "example": "nj9u7sj"}, "initialSeedAmount": {"type": "number", "description": "Initial seed", "example": 100}, "externalId": {"type": "string", "description": "External ID", "example": "enOQKQAJew8AAALDenOQKSmDW10"}, "eventId": {"type": "number", "description": "Spin number", "example": 1}, "gameCode": {"type": "string", "description": "Game code", "example": "pt-allad-reel-jp"}, "seedAmount": {"type": "number", "description": "Amount, contributed to the seed part of the JP", "example": 12.5}, "progressiveAmount": {"type": "number", "description": "Amount, contributed to the progressive part of the JP", "example": 6.5}, "totalSeedAmount": {"type": "number", "description": "Total Amount, contributed to the seed part of the JP", "example": 12.5}, "totalProgressiveAmount": {"type": "number", "description": "Total Amount, contributed to the progressive part of the JP", "example": 6.5}, "winAmount": {"type": "number", "description": "Full win amount of game stakes used for the Jackpot replenishment (in EUR)", "example": 6.5}, "winAmountCurrency": {"type": "number", "description": "Full win amount of game stakes used for the Jackpot replenishment (in player currency)", "example": 6.5}, "currency": {"type": "string", "description": "currency", "example": "USD"}, "currencyRate": {"type": "number", "description": "currency rate", "example": 0.92123}, "playerCode": {"type": "string", "description": "player code", "example": "test"}, "pool": {"type": "string", "description": "pool of contribute", "example": "mega"}, "trxDate": {"type": "string", "description": "transaction date", "example": "2018-01-03T17:00:00.000Z"}, "jackpotId": {"type": "string", "description": "jackpot id name", "example": "FIRE-reel"}, "info": {"$ref": "#/definitions/JpWinInfo"}}}, "CurrencyCode": {"type": "string", "description": "currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "ExtWinBetHistoryEntry": {"type": "object", "description": "Win Bet history entry", "properties": {"extTrxId": {"type": "string", "description": "extTrxId generated by ext game provider", "example": "99167"}, "bet": {"type": "number", "description": "Bet amount. Null if win has not happened yet", "example": 10}, "win": {"type": "number", "description": "Win amount", "example": 20}, "currency": {"$ref": "#/definitions/CurrencyCode"}, "brandId": {"type": "string", "description": "Encoded brand id", "example": "Qa1weSD"}, "playerCode": {"type": "string", "description": "Player code", "example": "PL0001"}, "roundId": {"type": "number", "description": "Game id( round id)", "example": 167}, "gameCode": {"type": "string", "description": "Game code", "example": "pt_gm1"}, "gameProviderCode": {"type": "string", "description": "Game provider code", "example": "sw_gos"}, "balanceBefore": {"type": "number", "description": "Balance before bet", "example": 1000}, "balanceAfter": {"type": "number", "description": "Balance after win", "example": 1010}, "isTest": {"type": "boolean", "description": "True for fun games", "example": false}, "insertedAt": {"type": "string", "description": "end date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "example": "2017-12-10 16:00:00 UTC"}, "finished": {"type": "boolean", "description": "The state of the round: finished or unfinished", "example": true}, "eventId": {"type": "integer", "description": "The number of the spin in a sequence", "example": 0}}}, "ExtHistoryDetailsResponse": {"type": "object", "description": "Ext Bet Win history entry", "properties": {"result": {"type": "string", "description": "String from gameprovider with round details", "example": "<html>History</html>"}, "type": {"type": "string", "description": "Type of the result string. html or base64image", "example": "html"}}}, "OperationsHistory": {"type": "object", "required": ["roundId", "spinNumber", "type", "currency", "bet", "win", "endOfRound"], "properties": {"roundId": {"type": "string", "description": "Encripted round identifier", "example": "Rk8VenwB"}, "spinNumber": {"type": "integer", "description": "Spin number", "example": 200000134}, "type": {"type": "string", "description": "History item type", "example": "slot"}, "gameId": {"type": "string", "description": "Game code", "example": "sw_sland"}, "playerCode": {"type": "string", "description": "Player code", "example": "sw_sland"}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}, "bet": {"type": "number", "description": "Total bet", "example": 0.1}, "win": {"type": "number", "description": "Total winning", "example": 1.5}, "endOfRound": {"type": "boolean", "description": "Whether this spin was at the end of the round", "example": true}, "ts": {"type": "string", "description": "Time of spin (ISO 8601 timestamp)", "example": "2017-02-16T16:37:13.613Z"}, "test": {"type": "boolean", "description": "Whether the round has been created only for testing", "example": false}, "credit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 10}, "debit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 5}, "balanceBefore": {"type": "number", "description": "Balance before spin action", "example": 10}, "balanceAfter": {"type": "number", "description": "Balance acter spin action", "example": 5}, "totalJpContribution": {"type": "number", "description": "Sum of all jackpot contributions i terms of the spin", "example": 10}, "totalJpWin": {"type": "number", "description": "Sum of all jackpot wins i terms of the spin", "example": 5}, "insertedAt": {"type": "string", "description": "ISO 8601 timestamp when spin history was unloaded to database.", "example": "2017-02-16T16:37:13.613Z"}}}}, "parameters": {"path": {"name": "path", "in": "path", "description": "Business entity path", "required": true, "type": "string"}, "roundId": {"name": "roundId", "in": "path", "description": "Round public id", "required": true, "type": "string"}, "offset": {"name": "offset", "in": "query", "description": "Result list offset", "required": false, "type": "integer", "default": 0}, "limit": {"name": "limit", "in": "query", "description": "Result list limit", "required": false, "type": "integer", "default": 20}, "sortBy": {"name": "sortBy", "in": "query", "description": "Sorting key", "required": false, "type": "string"}, "sortOrder": {"name": "sortOrder", "in": "query", "description": "Sorting order", "required": false, "type": "string", "enum": ["ASC", "DESC"]}, "isPayment": {"name": "isPayment", "in": "query", "description": "True if returned entry should be payment", "required": false, "type": "boolean"}, "from": {"name": "from", "in": "query", "description": "Period start date in UNIX-time (seconds that have elapsed since 00:00:00 1 January 1970 (UTC)). This parameter is outdated, please use ts__gte with other date format", "required": false, "type": "integer"}, "to": {"name": "to", "in": "query", "description": "Period end date in UNIX-time (seconds that have elapsed since 00:00:00 1 January 1970 (UTC)). This parameter is outdated, please use ts__lte with other date format", "required": false, "type": "integer"}, "currencyStrictEquality": {"in": "query", "name": "currency", "description": "currency equal to value", "required": false, "type": "string"}, "currencyIn": {"in": "query", "name": "currency__in", "description": "currencies separated by commas", "required": false, "type": "string"}, "playerCodeStrictEquality": {"in": "query", "name": "playerCode", "description": "player code equal to value", "required": false, "type": "string"}, "jackpotIdStrictEquality": {"in": "query", "name": "jackpotId", "description": "jackpotId equal to value", "required": false, "type": "string"}, "playerCodeContains": {"in": "query", "name": "playerCode__contains", "description": "player code contains string", "required": false, "type": "string"}, "playerCodeNotContains": {"in": "query", "name": "playerCode__contains!", "description": "player code doesn't contain string", "required": false, "type": "string"}, "playerCodeIn": {"in": "query", "name": "playerCode__in", "description": "list of player codes separated by commas", "required": false, "type": "string"}, "gameCodeStrictEquality": {"in": "query", "name": "gameCode", "description": "game code equal to value", "required": false, "type": "string"}, "gameCodeContains": {"in": "query", "name": "gameCode__contains", "description": "game code contains string", "required": false, "type": "string"}, "gameCodeNotContains": {"in": "query", "name": "gameCode__contains!", "description": "game code doesn't contain string", "required": false, "type": "string"}, "gameCodeIn": {"in": "query", "name": "gameCode__in", "description": "list of game codes separated by commas", "required": false, "type": "string"}, "roundIdStrictEquality": {"in": "query", "name": "roundId", "description": "roundId equal to value", "required": false, "type": "string"}, "roundIdIn": {"in": "query", "name": "roundId__in", "description": "list of roundIds separated by commas", "required": false, "type": "string"}, "dateHour": {"in": "query", "name": "dateHour", "description": "JP time truncate to hour in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "trxDate__gt": {"in": "query", "name": "trxDate__gt", "description": "Trx date truncate to datetime greater than in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": true, "type": "string"}, "trxDate__lt": {"in": "query", "name": "trxDate__lt", "description": "Trx date truncate to datetime lowest than in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "dateHour__gt": {"in": "query", "name": "dateHour__gt", "description": "JP time truncate to hour greater than in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "dateHour__lt": {"in": "query", "name": "dateHour__lt", "description": "JP time truncate to hour lower than in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "dateHour__gte": {"in": "query", "name": "dateHour__gte", "description": "JP time truncate to hour greater than or equal in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "dateHour__lte": {"in": "query", "name": "dateHour__lte", "description": "JP time truncate to hour lower than or equal in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "isTest": {"in": "query", "name": "isTest", "type": "boolean", "required": false, "description": "is test entry", "enum": [true, false]}, "queryFinished": {"name": "finished", "in": "query", "description": "Get finished/unfinished rounds. true - finished, false - unfinished, undefined - all", "required": false, "type": "boolean"}, "queryFormat": {"name": "format", "in": "query", "description": "Desired format", "required": false, "type": "string", "enum": ["csv"]}, "bet": {"in": "query", "name": "bet", "description": "sharp value for bet", "required": false, "type": "number"}, "bet__gt": {"in": "query", "name": "bet__gt", "description": "bet is greater-than", "required": false, "type": "number"}, "bet__lt": {"in": "query", "name": "bet__lt", "description": "bet is less-than", "required": false, "type": "number"}, "bet__gte": {"in": "query", "name": "bet__gte", "description": "bet is greater-than-or-equal", "required": false, "type": "number"}, "bet__lte": {"in": "query", "name": "bet__lte", "description": "bet is less-than-or-equal", "required": false, "type": "number"}, "win": {"in": "query", "name": "win", "description": "sharp value for win", "required": false, "type": "number"}, "win__gt": {"in": "query", "name": "win__gt", "description": "win is greater-than", "required": false, "type": "number"}, "win__lt": {"in": "query", "name": "win__lt", "description": "win is less-than", "required": false, "type": "number"}, "win__gte": {"in": "query", "name": "win__gte", "description": "win is greater-than-or-equal", "required": false, "type": "number"}, "win__lte": {"in": "query", "name": "win__lte", "description": "win is less-than-or-equal", "required": false, "type": "number"}, "revenue": {"in": "query", "name": "revenue", "description": "sharp value for revenue", "required": false, "type": "number"}, "revenue__gt": {"in": "query", "name": "revenue__gt", "description": "revenue is greater-than", "required": false, "type": "number"}, "revenue__lt": {"in": "query", "name": "revenue__lt", "description": "revenue is less-than", "required": false, "type": "number"}, "revenue__gte": {"in": "query", "name": "revenue__gte", "description": "revenue is greater-than-or-equal", "required": false, "type": "number"}, "revenue__lte": {"in": "query", "name": "revenue__lte", "description": "revenue is less-than-or-equal", "required": false, "type": "number"}, "device": {"in": "query", "name": "device", "description": "code of player's device", "required": false, "type": "string"}, "balanceBefore": {"in": "query", "name": "balanceBefore", "description": "balance before round of revenue", "required": false, "type": "number"}, "balanceBefore__gt": {"in": "query", "name": "balanceBefore__gt", "description": "balance before round is greater-than", "required": false, "type": "number"}, "balanceBefore__lt": {"in": "query", "name": "balanceBefore__lt", "description": "balance before round is less-than", "required": false, "type": "number"}, "balanceBefore__gte": {"in": "query", "name": "balanceBefore__gte", "description": "balance before round is greater-than-or-equal", "required": false, "type": "number"}, "balanceBefore__lte": {"in": "query", "name": "balanceBefore__lte", "description": "balance before round is less-than-or-equal", "required": false, "type": "number"}, "balanceAfter": {"in": "query", "name": "balanceAfter", "description": "balance after round of revenue", "required": false, "type": "number"}, "balanceAfter__gt": {"in": "query", "name": "balanceAfter__gt", "description": "balance after round is greater-than", "required": false, "type": "number"}, "balanceAfter__lt": {"in": "query", "name": "balanceAfter__lt", "description": "balance after round is less-than", "required": false, "type": "number"}, "balanceAfter__gte": {"in": "query", "name": "balanceAfter__gte", "description": "balance after round is greater-than-or-equal", "required": false, "type": "number"}, "balanceAfter__lte": {"in": "query", "name": "balanceAfter__lte", "description": "balance after round is less-than-or-equal", "required": false, "type": "number"}, "ts": {"in": "query", "name": "ts", "description": "sharp time of activity in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "ts__gte": {"in": "query", "name": "ts__gte", "description": "start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "ts__lte": {"in": "query", "name": "ts__lte", "description": "end date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "ts__gt": {"in": "query", "name": "ts__gt", "description": "start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "ts__lt": {"in": "query", "name": "ts__lt", "description": "end date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "firstTs": {"in": "query", "name": "firstTs", "description": "sharp first timestamp of activity in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "firstTs__gt": {"in": "query", "name": "firstTs__gt", "description": "first timestamp greater than in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "firstTs__lt": {"in": "query", "name": "firstTs__lt", "description": "first timestamp less than in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "firstTs__gte": {"in": "query", "name": "firstTs__gte", "description": "first timestamp greater than or equal in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "firstTs__lte": {"in": "query", "name": "firstTs__lte", "description": "first timestamp less than or equal in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "playedGames__gt": {"in": "query", "name": "playedGames__gt", "description": "Amount of played games", "required": false, "type": "string"}, "playedGames__lt": {"in": "query", "name": "playedGames__lt", "description": "Amount of played games", "required": false, "type": "string"}, "playedGames__gte": {"in": "query", "name": "playedGames__gte", "description": "Amount of played games", "required": false, "type": "string"}, "playedGames__lte": {"in": "query", "name": "playedGames__lte", "description": "Amount of played games", "required": false, "type": "string"}, "playedGames": {"in": "query", "name": "playedGames", "description": "Amount of played games", "required": false, "type": "string"}, "totalBets__gt": {"in": "query", "name": "totalBets__gt", "description": "Total sum of players bets", "required": false, "type": "string"}, "totalBets__lt": {"in": "query", "name": "totalBets__lt", "description": "Total sum of players bets", "required": false, "type": "string"}, "totalBets__gte": {"in": "query", "name": "totalBets__gte", "description": "Total sum of players bets", "required": false, "type": "string"}, "totalBets__lte": {"in": "query", "name": "totalBets__lte", "description": "Total sum of players bets", "required": false, "type": "string"}, "totalBets": {"in": "query", "name": "totalBets", "description": "Total sum of players bets", "required": false, "type": "string"}, "totalWins__gt": {"in": "query", "name": "totalWins__gt", "description": "Total sum of players bets", "required": false, "type": "string"}, "totalWins__lt": {"in": "query", "name": "totalWins__lt", "description": "Total sum of players bets", "required": false, "type": "string"}, "totalWins__gte": {"in": "query", "name": "totalWins__gte", "description": "Total sum of players bets", "required": false, "type": "string"}, "totalWins__lte": {"in": "query", "name": "totalWins__lte", "description": "Total sum of players bets", "required": false, "type": "string"}, "totalWins": {"in": "query", "name": "totalWins", "description": "Total sum of players bets", "required": false, "type": "string"}, "paymentDate": {"in": "query", "name": "paymentDate", "description": "Payment date in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "paymentDate__gt": {"in": "query", "name": "paymentDate__gt", "description": "Payment date greater than in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "paymentDate__lt": {"in": "query", "name": "paymentDate__lt", "description": "Payment date lower than in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "paymentDate__gte": {"in": "query", "name": "paymentDate__gte", "description": "Payment date greater than or equal in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "paymentDate__lte": {"in": "query", "name": "paymentDate__lte", "description": "Payment date lower than or equal in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "paymentDateHour": {"in": "query", "name": "paymentDateHour", "description": "Payment time truncate to hour in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "paymentDateHour__gt": {"in": "query", "name": "paymentDateHour__gt", "description": "Payment time truncate to hour greater than in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "paymentDateHour__lt": {"in": "query", "name": "paymentDateHour__lt", "description": "Payment time truncate to hour lower than in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "paymentDateHour__gte": {"in": "query", "name": "paymentDateHour__gte", "description": "Payment time truncate to hour greater than or equal in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "paymentDateHour__lte": {"in": "query", "name": "paymentDateHour__lte", "description": "Payment time truncate to hour lower than or equal in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "spinNumber": {"name": "spinNumber", "in": "path", "description": "Spin number", "required": true, "type": "integer"}, "languageQuery": {"name": "language", "in": "query", "description": "If specified, visualisation will be translated to given language", "required": false, "type": "string"}, "currencyInQuery": {"name": "currency", "in": "query", "type": "string", "description": "currency code to represent result in.", "required": false}, "playerCodeInQuery": {"name": "playerCode", "in": "query", "type": "string", "description": "Player code to search for", "required": false}, "gameCodeInQuery": {"name": "gameCode", "in": "query", "type": "string", "description": "Gamecode to search for", "required": false}, "insertedAt": {"in": "query", "name": "insertedAt", "description": "insertedAt date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "insertedAt__gt": {"in": "query", "name": "insertedAt__gt", "description": "insertedAt date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "insertedAt__lt": {"in": "query", "name": "insertedAt__lt", "description": "insertedAt date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "insertedAt__gte": {"in": "query", "name": "insertedAt__gte", "description": "insertedAt date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "insertedAt__lte": {"in": "query", "name": "insertedAt__lte", "description": "insertedAt date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "extTrxIdRequired": {"name": "extTrxId", "in": "query", "type": "string", "description": "External trx id generated by ext game provider to search for", "required": true}, "gameProviderCodeRequired": {"name": "gameProviderCode", "in": "query", "type": "string", "description": "Game Provider Code to search for", "required": true}, "roundIdInQuery": {"in": "query", "name": "roundId", "description": "public round id", "required": false, "type": "string"}, "extTrxId": {"in": "query", "name": "extTrxId", "description": "external reference", "required": false, "type": "string"}, "gameProviderCode": {"name": "gameProviderCode", "in": "query", "type": "string", "description": "Game Provider Code to search for", "required": false}, "spinTypeStrictEquality": {"in": "query", "name": "type", "description": "game type equal to value", "required": false, "type": "string"}, "spinTypeIn": {"in": "query", "name": "type__in", "description": "game type from comma separated list", "required": false, "type": "string"}}}