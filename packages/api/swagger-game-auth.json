{"swagger": "2.0", "info": {"description": "Skywind - API for Game Auth", "version": "5.54", "title": "Skywind - API for Game Auth"}, "basePath": "/v1", "produces": ["application/json"], "securityDefinitions": {"gameProviderAuthKey": {"description": "Tokens contains game provider authentication information", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-auth-token", "in": "header"}}, "paths": {"/game/session": {"get": {"tags": ["Game Session"], "summary": "Request player game session information", "parameters": [{"name": "token", "in": "query", "description": "Internal token with game token data", "type": "string", "required": true}], "responses": {"200": {"description": "Player game session information", "schema": {"$ref": "#/definitions/GameSessionInfo"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 240: Game not found"}, "404": {"description": "- 85: <PERSON><PERSON><PERSON><PERSON> not found\n- 102: Player not found\n- 51: Could not find entity\n"}}}}, "/game/start": {"post": {"tags": ["Start game"], "summary": "Authenticates player to play game", "parameters": [{"name": "startGameRequest", "in": "body", "description": "Start game request", "required": true, "schema": {"required": ["startGameRequest"], "properties": {"startGameRequest": {"type": "object", "properties": {"startGameToken": {"type": "string", "description": "Start game token", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJicmFuZElkIjoxMDAwMiwiZ2FtZUNvZGUiOiJHQU1FMDA..."}, "language": {"type": "string", "description": "Players language", "example": "en"}, "deviceId": {"type": "string", "description": "Players device id", "example": "web"}}, "required": ["startGameToken"]}, "ip": {"type": "string", "description": "Ip address of the player", "example": "********"}, "referrer": {"type": "string", "description": "The domain players comes from", "example": "super.casino.com"}}}}], "responses": {"200": {"description": "The player has been authenticated", "schema": {"$ref": "#/definitions/StartGameResult"}}}}}, "/game/fun/start": {"post": {"tags": ["Start game"], "summary": "Authenticates player to play fun game", "parameters": [{"name": "startGameRequest", "in": "body", "description": "Start game request", "required": true, "schema": {"required": ["startGameToken"], "properties": {"startGameToken": {"type": "string", "description": "Start game token", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJicmFuZElkIjoxMDAwMiwiZ2FtZUNvZGUiOiJHQU1FMDA..."}}}}], "responses": {"200": {"description": "The player has been authenticated", "schema": {"required": ["gameToken", "balance"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}}}}}}}, "/game/anonymous/settings": {"get": {"security": [{"gameProviderAuthKey": []}], "tags": ["Without Token"], "summary": "Get additional settings to perform play methods without token", "parameters": [{"$ref": "#/parameters/brandId"}, {"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/playMode"}], "responses": {"200": {"description": "Player game session information", "schema": {"$ref": "#/definitions/SettingsWithoutToken"}}, "400": {"description": "- 311: Game provider suspended\n"}, "401": {"description": "- 201: Provider secret incorrect\n"}, "404": {"description": "- 312: Game provider not found\n"}}}}, "/game/anonymous/auth": {"get": {"tags": ["Without Token"], "security": [{"gameProviderAuthKey": []}], "summary": "Authenticate game provider", "parameters": [], "responses": {"201": {"description": "Player game session information"}, "400": {"description": "- 311: Game provider suspended\n"}, "401": {"description": "- 201: Provider secret incorrect\n"}, "404": {"description": "- 312: Game provider not found\n"}}}}, "/game/url": {"post": {"tags": ["Play"], "summary": "Create new start game url", "parameters": [{"name": "data", "in": "body", "description": "Create new start game request", "required": true, "schema": {"required": ["startGameToken", "gameId"], "properties": {"startGameToken": {"type": "string", "description": "Start game token", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJicmFuZElkIjoxMDAwMiwiZ2FtZUNvZGUiOiJHQU1FMDA.."}, "gameId": {"type": "string", "description": "Game ID", "example": "sw-game-1"}}}}], "responses": {"200": {"description": "Test", "schema": {"$ref": "#/definitions/PlayerGameURLInfo"}}}}}, "/games": {"post": {"tags": ["Play"], "summary": "Get list of available games", "parameters": [{"name": "data", "in": "body", "description": "Get list of available games request", "required": true, "schema": {"required": ["startGameToken", "filter"], "properties": {"startGameToken": {"type": "string", "description": "Start game token", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJicmFuZElkIjoxMDAwMiwiZ2FtZUNvZGUiOiJHQU1FMDA.."}, "filter": {"type": "object", "description": "Filter games by game features", "example": {"isGRCGame": true}}}}}], "responses": {"200": {"description": "Games list", "schema": {"$ref": "#/definitions/AvailableGamesList"}}}}}, "/gameVersion": {"get": {"tags": ["Play"], "summary": "Gets game version details", "parameters": [{"$ref": "#/parameters/gameToken"}, {"name": "gameVersion", "in": "query", "description": "Game version", "required": true, "type": "string"}], "responses": {"200": {"description": "The game version details has been returned", "schema": {"$ref": "#/definitions/GameVersionDetails"}}}}}, "/game/launch": {"post": {"tags": ["Game launcher"], "summary": "Gets game url by received token", "parameters": [{"$ref": "#/parameters/gameLauncherRequest"}], "responses": {"200": {"description": "Game url", "schema": {"type": "string", "example": "<game_url>"}}, "400": {"description": "- 708: It is forbidden to start game from unauthorized site\n- 736: Entity is under maintenance, but maintenance url is not defined\"\n- 751: Referrer is missing\n- 803: Game launcher token is expired\n- 804: Game launcher token error", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 85: C<PERSON><PERSON>cy not found\n- 240: Game not found\n- 306: Game is suspended", "schema": {"$ref": "#/definitions/Error"}}}}}, "/history/events": {"get": {"tags": ["Play"], "summary": "Gets player events", "parameters": [{"$ref": "#/parameters/gameToken"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}], "responses": {"200": {"description": "The player's event details has been returned", "schema": {"type": "array", "items": {"$ref": "#/definitions/EventHistory"}}}}}}, "/history/rounds": {"get": {"tags": ["Play"], "summary": "Gets player rounds history", "parameters": [{"$ref": "#/parameters/gameToken"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/roundId__lt"}, {"$ref": "#/parameters/roundId__lte"}, {"$ref": "#/parameters/roundId__gt"}, {"$ref": "#/parameters/roundId__gte"}], "responses": {"200": {"description": "The player's round history has been returned", "schema": {"$ref": "#/definitions/RoundHistory"}}}}}, "/history/rounds/{roundId}": {"get": {"tags": ["Play"], "summary": "Gets player round events", "parameters": [{"$ref": "#/parameters/gameToken"}, {"name": "roundId", "in": "path", "description": "Round identifier", "required": true, "type": "string"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}], "responses": {"200": {"description": "The player's round details has been returned", "schema": {"type": "array", "items": {"$ref": "#/definitions/EventHistory"}}}}}}, "/history/rounds/{roundId}/events/{eventId}": {"get": {"tags": ["Play"], "summary": "Gets player event details", "parameters": [{"$ref": "#/parameters/gameToken"}, {"name": "roundId", "in": "path", "description": "Round identifier", "required": true, "type": "string"}, {"name": "eventId", "in": "path", "description": "Event identifier", "required": true, "type": "string"}], "responses": {"200": {"description": "The player's event details has been returned", "schema": {"$ref": "#/definitions/EventDetails"}}}}}, "/history/gh-app/spin-details": {"get": {"tags": ["Play"], "summary": "Gets spin details for game history app", "parameters": [{"$ref": "#/parameters/ghAppToken"}], "responses": {"200": {"description": "Player's spin details", "schema": {"$ref": "#/definitions/EventDetails"}}}}}, "/history/gh-app/round-details": {"get": {"tags": ["Play"], "summary": "Gets array of spin details in round for game history app", "parameters": [{"$ref": "#/parameters/ghAppToken"}], "responses": {"200": {"description": "Player's spin details", "schema": {"type": "array", "items": {"$ref": "#/definitions/EventDetails"}}}}}}, "/operator": {"get": {"tags": ["Game Session"], "summary": "Get operator info", "parameters": [{"name": "token", "in": "query", "description": "Internal token with game token data", "type": "string", "required": true}], "responses": {"400": {"description": "- 62: One of the parents is suspended"}, "404": {"description": "- 51: Could not find entity\n"}}}}}, "parameters": {"gameLauncherRequest": {"name": "gameLauncherRequest", "in": "body", "description": "Game launcher request", "required": true, "schema": {"$ref": "#/definitions/GameLauncherRequest"}}, "providerGameCode": {"name": "providerGameCode", "in": "path", "description": "Provider game code", "required": true, "type": "string"}, "brandId": {"name": "brandId", "in": "query", "description": "Brand ID", "type": "number", "required": true}, "gameCode": {"name": "gameCode", "in": "query", "description": "Game code", "type": "string", "required": true}, "playMode": {"name": "playMode", "in": "query", "description": "Play mode", "type": "string", "required": true}, "gameToken": {"name": "gameToken", "in": "query", "description": "Game token", "required": true, "type": "string"}, "ghAppToken": {"name": "token", "in": "query", "description": "Token generated for game history app", "required": true, "type": "string"}, "gameInformation": {"name": "info", "in": "body", "description": "Create/update game information request", "required": true, "schema": {"$ref": "#/definitions/GameRegistration"}}, "offset": {"name": "offset", "in": "query", "description": "Result list offset", "required": false, "type": "integer", "default": 0}, "limit": {"name": "limit", "in": "query", "description": "Result list limit", "required": false, "type": "integer", "default": 20}, "sortBy": {"name": "sortBy", "in": "query", "description": "Sorting key", "required": false, "type": "string"}, "sortOrder": {"name": "sortOrder", "in": "query", "description": "Sorting order", "required": false, "type": "string", "enum": ["ASC", "DESC"]}, "roundId": {"in": "query", "name": "roundId", "description": "roundId equal to value", "required": false, "type": "string"}, "roundId__gt": {"in": "query", "name": "roundId__gt", "description": "roundId is greater-than", "required": false, "type": "string"}, "roundId__gte": {"in": "query", "name": "roundId__gte", "description": "roundId is greater-than-or-equal", "required": false, "type": "string"}, "roundId__lt": {"in": "query", "name": "roundId__lt", "description": "roundId is less-than", "required": false, "type": "string"}, "roundId__lte": {"in": "query", "name": "roundId__lte", "description": "roundId is less-than-or-equal", "required": false, "type": "string"}, "ts": {"name": "ts", "in": "query", "description": "Timestamp (ISO 8601 timestamp)", "required": false, "type": "string"}, "ts__lt": {"name": "ts__lt", "in": "query", "description": "Timestamp (ISO 8601 timestamp)", "required": false, "type": "string"}, "ts__lte": {"name": "ts__lte", "in": "query", "description": "Timestamp (ISO 8601 timestamp)", "required": false, "type": "string"}}, "definitions": {"GameRegistration": {"type": "object", "description": "Game registration information", "required": ["providerGameCode", "title", "type", "url", "defaultInfo", "info", "limits"], "properties": {"providerGameCode": {"$ref": "#/definitions/ProviderGameCode"}, "title": {"type": "string", "description": "Game title", "example": "Game 1"}, "type": {"type": "string", "enum": ["slot", "action"], "description": "Game type", "example": "slot"}, "url": {"type": "string", "description": "Game URL", "example": "http://api.test/game"}, "defaultInfo": {"$ref": "#/definitions/GameDescription"}, "info": {"$ref": "#/definitions/GameDescriptionByLocale"}, "limits": {"$ref": "#/definitions/LimitsByCurrencyCode"}, "labels": {"$ref": "#/definitions/GameLabelList"}}}, "GameDescription": {"type": "object", "description": "Game information", "required": ["name", "description", "limits"], "properties": {"name": {"type": "string", "description": "Name of the game", "example": "Mr. <PERSON>"}, "description": {"type": "string", "description": "game description", "example": "Mr. Monkey slots"}, "limits": {"$ref": "#/definitions/LimitsByCurrencyCode"}}}, "GameDescriptionByLocale": {"type": "object", "description": "Game description per locale", "additionalProperties": {"$ref": "#/definitions/GameDescription"}, "example": {"USD": {"name": "Game name", "description": "Game description"}}}, "GameLabelList": {"type": "array", "description": "Game label list", "items": {"$ref": "#/definitions/GameLabel"}}, "GameLabel": {"type": "object", "description": "Game label", "required": ["title", "group"], "properties": {"title": {"type": "string", "description": "Game label title", "example": "jackpot"}, "group": {"type": "string", "description": "Game label group: class, platform or feature", "example": "feature"}}}, "Error": {"type": "object", "properties": {"code": {"type": "integer", "description": "error code", "example": 677}, "message": {"type": "string", "description": "error message", "example": "Negative transaction operation value"}}}, "GameLauncherRequest": {"type": "object", "required": ["launcherToken", "webSiteDomain"], "properties": {"launcherToken": {"type": "string", "description": "Represents a token for launching a game", "example": "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.uPpPe_nVa0NBs0LvCDyhycMQBOGEDWFTcdGI5SgPqHrWgtFS7228tcFgizsITFfFdj0spPodSoapxi8h0cXtAg"}, "webSiteDomain": {"type": "string", "description": "Represents a website as a referrer", "example": "<website_domain>"}}}, "RoundHistory": {"type": "object", "properties": {"roundId": {"type": "integer", "description": "Round ID", "example": 100000063}, "brandId": {"type": "string", "description": "Brand public id", "example": "feE3Sb39"}, "playerCode": {"type": "string", "description": "Player code", "example": "PLAYER1"}, "gameCode": {"type": "string", "description": "Game code", "example": "sw_mrmnky"}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}, "bet": {"type": "number", "description": "Total bet", "example": 0.1}, "win": {"type": "number", "description": "Total winning", "example": 2}, "revenue": {"type": "number", "description": "Revenue", "example": -1.9}, "firstTs": {"type": "string", "description": "time of first action in round", "example": "2017-07-14T07:07:01.080Z"}, "ts": {"type": "string", "description": "time of last action in round", "example": "2017-07-14T07:07:11.930Z"}, "finished": {"type": "boolean", "description": "Whether the round has ended", "example": true}, "isTest": {"type": "boolean", "description": "Whether the round has been created only for testing", "example": false}, "balanceBefore": {"type": "number", "description": "Player's balance before round", "example": 25000}, "balanceAfter": {"type": "number", "description": "Player's balance after round", "example": 24950}, "device": {"type": "string", "description": "Player's device", "example": "web"}, "credit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 10}, "debit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 5}}}, "EventHistory": {"type": "object", "required": ["roundId", "spinNumber", "view", "type", "currency", "bet", "win", "endOfRound"], "properties": {"roundId": {"type": "integer", "description": "Round identifier", "example": 200000134}, "spinNumber": {"type": "integer", "description": "Spin number", "example": 200000134}, "type": {"type": "string", "description": "History item type", "example": "slot"}, "view": {"type": "string", "description": "Result view", "example": [[2, 12, 7, 4, 3], [5, 12, 1, 13, 4], [1, 2, 5, 2, 1], [3, 6, 4, 6, 7]]}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}, "bet": {"type": "number", "description": "Total bet", "example": 0.1}, "win": {"type": "number", "description": "Total winning", "example": 1.5}, "endOfRound": {"type": "boolean", "description": "Whether this spin was at the end of the round", "example": true}, "ts": {"type": "string", "description": "Time of spin (ISO 8601 timestamp)", "example": "2017-02-16T16:37:13.613Z"}, "test": {"type": "boolean", "description": "Whether the round has been created only for testing", "example": false}, "credit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 10}, "debit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 5}}}, "EventDetails": {"type": "object", "required": ["roundId", "spinNumber", "gameId", "gameVersion"], "properties": {"roundId": {"type": "integer", "description": "Round identifier", "example": 200000134}, "spinNumber": {"type": "integer", "description": "Spin number", "example": 200000134}, "gameId": {"type": "string", "description": "Game module id", "example": "sw_gol"}, "gameVersion": {"type": "string", "description": "Game module version", "example": "0.1.1"}, "details": {"type": "string", "description": "Game event details", "example": "{data: \"Some spin data\"}"}, "initSettings": {"type": "string", "description": "Game init settings", "example": "{data: \"Some game init information\"}"}, "credit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 10}, "debit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 5}}}, "GameSessionInfo": {"type": "object", "properties": {"isGRCGame": {"type": "boolean", "description": "Is this GRC game", "example": "false"}, "mustStoreExternalBetWinHistory": {"type": "boolean", "description": "Should we store external history information", "example": "boolean"}, "sharedPromoEnabled": {"type": "boolean", "description": "Should we use our promotions", "example": "false"}, "providerGameCode": {"type": "string", "description": "Provider game code. Used if only 'mustStoreExternalBetWinHistory': true", "example": "sw_al"}, "promo": {"type": "object", "description": "Provider game code. Used if only 'mustStoreExternalBetWinHistory': true", "$ref": "#/definitions/PlayerPromos"}}}, "PlayerPromos": {"type": "object", "properties": {"freeBet": {"type": "array", "items": {"$ref": "#/definitions/FreeBet"}}, "bonusCoin": {"type": "object", "$ref": "#/definitions/BonusCoin"}}}, "FreeBet": {"type": "object", "required": ["promoId"], "properties": {"promoId": {"type": "number", "description": "Promotion id", "example": 123143}, "coin": {"type": "number", "description": "Coin value", "example": 10}, "rewardId": {"type": "boolean", "description": "Reward id", "example": 1998791}, "externalId": {"type": "string", "description": "External id", "example": "234243"}}}, "BonusCoin": {"type": "object", "required": ["promoId"], "properties": {"promoId": {"type": "number", "description": "Promotion id", "example": 123143}, "exchangeRate": {"type": "number", "description": "Exchange rate", "example": 10}, "rewardId": {"type": "boolean", "description": "Reward id", "example": 19982766}, "externalId": {"type": "string", "description": "External id", "example": "234243"}}}, "GameToken": {"type": "string", "description": "Game token", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzY290Y2guaW8iLCJleHAiOjEzMDA4MTkzO..."}, "StartGameResult": {"type": "object", "required": ["response"], "properties": {"response": {"type": "object", "description": "Start game response", "$ref": "#/definitions/StartGameResponse"}, "sessionInfo": {"type": "object", "description": "Player game session info", "$ref": "#/definitions/GameSessionInfo"}, "deferredPayments": {"type": "array", "description": "Player game session info", "items": {"$ref": "#/definitions/DeferredPayment"}}, "operatorInfo": {"type": "object", "description": "Operator details", "$ref": "#/definitions/OperatorInfo"}}}, "OperatorInfo": {"type": "object", "required": ["id", "path", "version"], "properties": {"id": {"type": "number", "description": "Operator ID", "example": 35234}, "version": {"type": "number", "description": "version of the record of operator", "example": 1}, "path": {"type": "string", "description": "Path ID", "example": ":TLE1:OP1:MERCH1"}}}, "StartGameResponse": {"type": "object", "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "limits": {"$ref": "#/definitions/Limits"}, "player": {"$ref": "#/definitions/Player"}, "jurisdictionCode": {"type": "string", "example": "COM"}, "region": {"type": "string", "example": "default"}, "renderType": {"type": "number", "enum": [0, 1, 2], "description": "History rendering type", "example": 0}, "jrsdSettings": {"type": "object", "properties": {"showRTP": {"type": "boolean", "example": true}, "rulesDateStamped": {"type": "boolean", "example": true}}}, "brandSettings": {"type": "object", "properties": {"fullscreen": {"type": "boolean", "example": true}}}, "gameSettings": {"type": "object", "properties": {"turbo": {"type": "boolean", "example": true}, "fastPlay": {"type": "boolean", "example": true}, "turboPlus": {"type": "boolean", "example": true}}}}}, "DeferredPaymentAttributes": {"type": "object", "required": ["brandId", "playerCode", "currencyCode"], "properties": {"brandId": {"type": "integer", "example": "1123"}, "playerCode": {"type": "string", "example": "player001"}, "currencyCode": {"type": "string", "example": "USD"}, "gameCode": {"type": "string", "example": "sw_al"}, "originalGameCode": {"type": "string", "example": "sw_al"}, "jackpotId": {"type": "string", "example": "sw-global-jackpot"}}}, "DeferredPayment": {"type": "object", "required": ["brandId", "playerCode", "currencyCode"], "allOf": [{"$ref": "#/definitions/DeferredPaymentAttributes"}], "properties": {"id": {"type": "string", "example": "1123"}}}, "SettingsWithoutToken": {"type": "object", "required": ["operatorInfo", "sessionInfo", "transferEnabled"], "properties": {"transferEnabled": {"type": "boolean", "description": "Is it transferable game", "example": "true"}, "operatorInfo": {"type": "object", "description": "Operator details", "$ref": "#/definitions/OperatorInfo"}, "sessionInfo": {"type": "object", "description": "Operator details", "$ref": "#/definitions/SessionInfoWithoutToken"}}}, "SessionInfoWithoutToken": {"type": "object", "properties": {"mustStoreExternalBetWinHistory": {"type": "boolean", "description": "Do we need to store external bet win history"}, "gameProviderCode": {"type": "string", "description": "Provider game id", "example": "sw_al"}}}, "PlayerGameURLInfo": {"type": "object", "required": ["url", "token"], "properties": {"url": {"type": "string", "description": "game URL for specific player", "example": "http://super_game.com/"}, "token": {"type": "string", "format": "byte", "description": "player token to access game", "example": "oJqXX2tkAADKn3MpcM9kVbVk53neuIYI62dEkYdYubl+9lyXRECjQww3VsmEPfMoUkO6uqB56WDPhPGdS3aGnQ"}}}, "GameVersionDetails": {"type": "object", "required": ["gameId", "gameVersion", "initSettings"], "properties": {"gameId": {"type": "string", "description": "Game module id", "example": "sw_gol"}, "gameVersion": {"type": "string", "description": "Game module version", "example": "0.1.1"}, "initSettings": {"type": "string", "description": "Game init settings", "example": "{data: \"Some game init information\"}"}}}, "AvailableGamesList": {"type": "object", "required": ["games"], "properties": {"games": {"type": "array", "items": {"type": "string"}, "description": "game URL for specific player", "example": ["sw_mrmnky", "sw_gol"]}}}, "Limits": {"type": "object", "description": "Limits", "properties": {"winMax": {"type": "integer", "example": 1500000}, "stakeAll": {"type": "array", "items": {"type": "number"}, "example": [0.01, 0.02, 0.03]}, "stakeDef": {"type": "number", "example": 0.04}, "stakeMax": {"type": "number", "example": 5}, "stakeMin": {"type": "number", "example": 0.01}, "maxTotalStake": {"type": "number", "example": 250}}}, "LimitsByCurrencyCode": {"type": "object", "description": "Limits per currency", "additionalProperties": {"$ref": "#/definitions/Limits"}}, "ProviderGameCode": {"type": "string", "description": "Game code", "example": "GAME001"}, "Player": {"type": "object", "properties": {"code": {"type": "string", "example": "ev7tsaT20200922T120516"}, "id": {"type": "number", "example": 3}, "status": {"type": "string", "example": "normal"}, "currency": {"type": "string", "example": "USD", "description": "currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)"}, "country": {"type": "string", "example": "US"}, "language": {"type": "string", "example": "en"}, "gameGroup": {"type": "string"}, "agentId": {"type": "string"}, "isTest": {"type": "boolean", "example": true}, "lastLogin": {"type": "string", "description": "Last login time (ISO 8601 timestamp)", "example": "2018-06-14T20:56:57.714Z"}, "createdAt": {"type": "string", "description": "ISO 8601 timestamp", "example": "2018-06-14T20:56:57.714Z"}, "updatedAt": {"type": "string", "description": "ISO 8601 timestamp", "example": "2018-06-14T20:56:57.714Z"}, "deactivatedAt": {"type": "string", "description": "ISO 8601 timestamp", "example": "2018-06-14T20:56:57.714Z"}, "brandId": {"type": "integer", "example": 3}, "brandTitle": {"type": "string", "example": "Main entity"}, "defaultGameGroup": {"type": "string"}, "isBlocked": {"type": "boolean", "example": false}}}}}