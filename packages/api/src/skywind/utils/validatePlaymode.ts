import { BrandEntity } from "../entities/brand";
import { Merchant } from "../entities/merchant";
import { PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import * as Errors from "../errors";

export function validatePlaymode(brand: BrandEntity, playmode: PlayMode = PlayMode.REAL, merchant?: Merchant): boolean {
    if (playmode !== PlayMode.PLAY_MONEY) {
        return true;
    }

    if (!(brand.isMerchant && merchant?.params?.supportPlayMoney)) {
        throw new Errors.ValidationError("Play money is not supported operation");
    }

    return true;
}
