const expressValidator = require("express-validator");

export const DOCKER_URL_REGEXP = /^http[s]?:\/\/[\w-.]*(?::([0-9]+))?\/?$/;
export function isValidUrl(value, options = { supportDockerDomain: false }) {
    if (typeof value !== "string" || value.includes("/..")) {
        return false;
    }

    if (options.supportDockerDomain) {
        return DOCKER_URL_REGEXP.test(value);
    } else {
        return expressValidator.validator.isURL(value, options);
    }
}
