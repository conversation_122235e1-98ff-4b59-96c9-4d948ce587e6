import { IpLocationError } from "../errors";
import { measures } from "@skywind-group/sw-utils";
import * as isCidr from "is-cidr";
import maxmind, { CityResponse, Reader } from "maxmind";
import { COUNTRIES } from "./common";
import measure = measures.measure;

const MAXMIND_LOCATION_DB_PATH = __dirname + "/../../../resources/maxmind/GeoIP2-City.mmdb";

export interface IpLocation {
    countryCode: string;
    regionCode?: string;
}

export interface IpLocationService {
    validateWhitelistIP(ip: string): boolean;

    getCountryAndRegionCodesByIp(ip: string): Promise<string>;

    getLocationByIp(ip: string): Promise<IpLocation>;

    isWhitelisted(ip: string, whitelist: string[]): boolean;

    isIpOfPrivateAddressSpace(ip: string): boolean; // refer to https://tools.ietf.org/html/rfc1918#page-3
}

function isIp4InCidrs(ip: string, rules: string[]) {
    function ip4ToInt(value: string) {
        return value.split(".").reduce((int, oct) => (int << 8) + parseInt(oct, 10), 0) >>> 0;
    }

    const ipInt = ip4ToInt(ip);
    return rules.filter(isCidr.v4).some(rule => {
        const [range, bits] = rule.split("/", 2);
        const mask = ~(2 ** (32 - Number(bits || 32)) - 1);
        return (ipInt & mask) === (ip4ToInt(range) & mask);
    });
}

export class MaxMindIpLocationService implements IpLocationService {

    private locationLookup: Reader<CityResponse>;
    private locationLookupPending: Promise<Reader<CityResponse>>;
    private static twentyBitPrivateBlockRange = [
        "172.16.", "172.17.", "172.18.", "172.19.", "172.20.", "172.21.",
        "172.22.", "172.23.", "172.24.", "172.25.", "172.26.", "172.27.", "172.28.", "172.29.", "172.30.", "172.31."
    ];

    @measure({ name: "MaxMindIpLocationService.validateWhitelistIP" })
    public validateWhitelistIP(ip: string): boolean {
        if (typeof ip !== "string") {
            return false;
        }
        if (ip === "*.*.*.*") {
            return true;
        }
        if (maxmind.validate(ip)) {
            return true;
        }
        if (isCidr.v4(ip)) {
            return true;
        }
        const parts = ip.split(".");
        if (parts.length !== 4) {
            return false;
        }
        for (const part of parts) {
            if (!part) {
                return false;
            }
            if (part === "*") {
                continue;
            }
            const n = Number(part);
            if (isNaN(n)) {
                return false;
            }
            if (n < 0 || n > 255) {
                return false;
            }
        }
        return true;
    }

    @measure({ name: "MaxMindIpLocationService.isWhitelisted" })
    public isWhitelisted(ip: string, whitelist: string[]): boolean {
        if (!maxmind.validate(ip)) {
            return false;
        }
        ip = ip.replace("::ffff:", "");
        if (whitelist.indexOf(ip) >= 0) {
            return true;
        }
        // supports only IPv4 mask
        if (isIp4InCidrs(ip, whitelist)) {
            return true;
        }
        // supports only IPv4 mask
        if (whitelist.indexOf("*.*.*.*") >= 0) {
            return true;
        }
        const parts = ip.split(".");
        for (let i = parts.length - 1; i >= 0; i -= 1) {
            parts[i] = "*";
            const mask = parts.join(".");
            if (whitelist.indexOf(mask) >= 0) {
                return true;
            }
        }
        return false;
    }

    @measure({ name: "MaxMindIpLocationService.isIpOfPrivateAddressSpace" })
    public isIpOfPrivateAddressSpace(ip: string): boolean {
        if (!maxmind.validate(ip)) {
            return false;
        }
        ip = ip.replace("::ffff:", "");

        if (ip.startsWith("10.")) {
            return true;
        }

        if (ip.startsWith("192.168.")) {
            return true;
        }

        const parts = ip.split(".");
        const twoFirstBytesOfIp = parts[0] + "." + parts[1] + ".";
        return MaxMindIpLocationService.twentyBitPrivateBlockRange.includes(twoFirstBytesOfIp);
    }

    /**
     *  Checks for the presence of country and region codes in the countries.json file
     *  and returns appropriate value.
     *  If country and region are presented => country-region
     *  If country is presented => country
     */
    @measure({ name: "MaxMindIpLocationService.getCountryAndRegionCodesByIp", isAsync: true })
    public async getCountryAndRegionCodesByIp(ip: string): Promise<string | undefined> {
        const location = await this.getLocationByIp(ip);

        if (!location || !location.countryCode) {
            return undefined;
        }

        if (location.regionCode) {
            const countryAndRegionCodes = `${location.countryCode}-${location.regionCode}`;

            if (COUNTRIES[countryAndRegionCodes]) {
                return countryAndRegionCodes;
            }
        }

        if (COUNTRIES[location.countryCode]) {
            return location.countryCode;
        }

        return undefined;
    }

    @measure({ name: "MaxMindIpLocationService.getLocationByIp", isAsync: true })
    public async getLocationByIp(ip: string): Promise<IpLocation | undefined> {
        const lookup = await this.getLocationLookup();
        const response: CityResponse = lookup.get(ip);
        if (!response) {
            return undefined;
        }

        let countryCode: string;
        if (response.country && response.country.iso_code) {
            countryCode = response.country.iso_code;
        } else if (response.registered_country && response.registered_country.iso_code) {
            countryCode = response.registered_country.iso_code;
        }

        let regionCode: string;
        if (response.subdivisions && response.subdivisions.length && response.subdivisions[0].iso_code) {
            regionCode = response.subdivisions[0].iso_code;
        }

        return countryCode ? { countryCode, regionCode } : undefined;
    }

    private async getLocationLookup(): Promise<Reader<CityResponse>> {
        if (this.locationLookup) {
            return this.locationLookup;
        }

        if (this.locationLookupPending) {
            return this.locationLookupPending;
        }

        this.locationLookupPending = this.getLocationLookupPending();
        return this.locationLookupPending;
    }

    private async getLocationLookupPending(): Promise<Reader<CityResponse>> {
        try {
            this.locationLookup = await maxmind.open(MAXMIND_LOCATION_DB_PATH);
            return this.locationLookup;

        } catch (error) {
            throw new IpLocationError(error);

        } finally {
            this.locationLookupPending = undefined;
        }
    }
}

const maxMindIpLocationService = new MaxMindIpLocationService();

export function getIpLocationService(): IpLocationService {
    return maxMindIpLocationService;
}
