import { Request } from "express";
import * as crypto from "node:crypto";
import config from "../../skywind/config";
import { IpHolder } from "../services/security";

export interface HttpRequest {
    query: any;
    headers: any;
    ip?: string;
}

export function findIp(req: HttpRequest): string {
    let ip: string = req.query && req.query.ip;
    const xForwardedForHeader = req.headers["x-forwarded-for"];
    if (!ip && xForwardedForHeader) {
        const forwardedFor = xForwardedForHeader.split(",");
        ip = forwardedFor[0].replace(" ", "");
    }
    return ip;
}

export interface RequestInfo {
    ip: string;
    userAgent: string;
    gs?: string;
}

export function getRequestInfoFomRequest(req: HttpRequest & IpHolder): RequestInfo {
    return {
        ip: req.resolvedIp || findIp(req) || req.ip,
        userAgent: req.headers["user-agent"] || "N/A",
        gs: req.headers["x-gs"] || ""
    };
}

export function requestLogData(req: Request & IpHolder) {
    return buildLogData(req.originalUrl, req.method, req.query, req.body, getRequestInfoFomRequest(req));
}

export function buildLogData(url: string, method: string, query: any, body: any, info: RequestInfo) {
    const logData = {
        method: method,
        url: url,
        query: query,
        ip: info.ip,
        userAgent: info.userAgent,
        gs: info.gs
    };
    if (body && ((method === "POST" || method === "PATCH" || method === "PUT")) &&
        Object.keys(body).length > 0 && !shouldSkipBody(url)) {
        logData["body"] = getSecuredObjectData(body);
    }
    return logData;
}

function shouldSkipBody(url: string) {
    return config.logParams.skipBodyOnUrl.some((skipUrl) => url.startsWith(skipUrl));
}

function getSecuredObjectData(dataObject: Object) {
    const securedData = {};
    for (const key in dataObject) {
        if (!dataObject.hasOwnProperty(key)) {
            continue;
        }
        const value = dataObject[key];
        if (isSecurityProperty(key)) {
            if (value) {
                securedData[key] = encryptData(value);
            } else {
                securedData[key] = value;
            }
        } else if (value !== null && typeof value === "object") {
            securedData[key] = getSecuredObjectData(value);
        } else {
            securedData[key] = value;
        }
    }
    return securedData;
}

function isSecurityProperty(key: string) {
    return config.logParams.secureKeys.some((secureKey) => secureKey === key);
}

function encryptData(data: string) {
    return crypto.pbkdf2Sync(data.toString(),
        config.logParams.secureSalt,
        config.logParams.encryptIters,
        config.logParams.keylen,
        "sha512").toString("base64");
}
