import { createTransport } from "nodemailer";
import { SmtpOptions, EmailTemplate } from "../entities/settings";

import logger from "./logger";
import config from "../config";
import * as Errors from "../errors";
import { lazy } from "@skywind-group/sw-utils";

const log = logger("email");

export interface SimpleEmailData {
    email: string;
    options: any;
}

export interface EmailData {
    fromEmail?: string;
    fromName?: string;
    subject: string;
    htmlPart: string;
}

export interface EmailService {
    sendEmail(recipients: string[], emailData: EmailData): Promise<void>;
    getFromEmail(): string;
}

class MailJetEmailService implements EmailService {

    private readonly mailjet: any;
    private readonly mailjetSendEmail: any;

    constructor() {
        this.mailjet = require("node-mailjet")
            .connect(config.mailjet.publicKey, config.mailjet.privateKey, {
                perform_api_call: !config.mailjet.isTest // used for tests. default is true
            });

        this.mailjetSendEmail = this.mailjet.post("send");
    }

    public async sendEmail(recipients: string[], emailData: EmailData): Promise<void> {

        const recipientsEmails = recipients.map((email: string) => ({ Email: email }));

        const dataToSend = {
            FromEmail: emailData.fromEmail,
            FromName: emailData.fromName,
            Subject: emailData.subject,
            "Html-part": emailData.htmlPart,
            Recipients: recipientsEmails
        };

        return new Promise<void>((resolve, reject) => {
            this.mailjetSendEmail
                .request(dataToSend)
                .then((data) => {
                    resolve();
                })
                .catch(err => {
                    log.error(err.message, `An error occurred when sending email to: ${recipients}`);
                    reject(new Errors.EmailSendingError());
                });
        });
    }

    public getFromEmail(): string {
        return config.mailjet.sentFromEmail;
    }
}

class ConsoleEmailService implements EmailService {
    public getFromEmail(): string {
        return config.mailjet.sentFromEmail;
    }

    public sendEmail(recipients: string[], emailData: EmailData): Promise<void> {
        const dataToSend = {
            FromEmail: emailData.fromEmail,
            FromName: emailData.fromName,
            Subject: emailData.subject,
            "Html-part": emailData.htmlPart,
            Recipients: recipients.map((email: string) => ({ Email: email }))
        };

        log.info(dataToSend, "Console email");
        return Promise.resolve();
    }

}

const mailJetEmailService = lazy<EmailService>(() => new MailJetEmailService());

export function getDefaultEmailService(): EmailService {
    if (config.defaultMailAgent === "console") {
        return new ConsoleEmailService();
    }

    return mailJetEmailService.get();
}

export async function sendEmail(data: SimpleEmailData, smtp: SmtpOptions, template: EmailTemplate): Promise<boolean> {
    const transporter = createTransport(smtp);
    const templateSender = transporter.templateSender(template);
    try {
        await templateSender({ to: data.email, }, data.options);
        return true;
    } catch (err) {
        log.error(err, `An error occurred when sending email to: ${data.email}`);
        return Promise.reject(new Errors.EmailSendingError());
    }
}

/**
 * Sends auth code using default email service.
 */
export async function sendAuthCodeEmail(toEmail: string,
                                        username: string,
                                        authcode: string,
                                        template: EmailTemplate): Promise<void> {

    const sender = getDefaultEmailService();

    const emailData: EmailData = {
        fromEmail: sender.getFromEmail(),
        fromName: template.from,
        subject: template.subject,
        htmlPart: template.html.replace("{{authCode}}", authcode).replace("{{username}}", username)
    };

    return sender.sendEmail([toEmail], emailData);
}
