export interface Grouper<I, O> {
    group(grouper: (resource: I) => groupKey, filter?: (resource: I) => boolean): Forker<I, O>;
}

export interface Forker<I, O> {
    fork(forker: (group: groupKey, resource: I[]) => Promise<O>): <PERSON><PERSON><I, O>;
}

export interface Joiner<I, O> {
    join(joiner: (acc: O, cur: O) => void, fallback?: () => O): Promise<O>;
}

interface ResourceProvider<T> {
    provide(): Promise<T>;
}

// Can be moved to generic param
export type groupKey = number;

export class GroupForkJoinPool {
    private constructor() {
    }

    public static createFromSupplier<I, O>(supplier: () => Promise<I[]>): Grouper<I, O> {
        return new AsyncStreamGrouper<I, O>(supplier);
    }
}

class AsyncStreamGrouper<I, O> implements Grouper<I, O>, ResourceProvider<Map<groupKey, I[]>> {
    private grouper: (resource: I) => groupKey;
    private filter?: (resource: I) => boolean;

    constructor(private readonly supplier: () => Promise<I[]>) {
    }

    public group(grouper: (resource: I) => groupKey, filter?: (resource: I) => boolean): Forker<I, O> {
        this.grouper = grouper;
        this.filter = filter;
        return new AsyncStreamForker<I, O>(this);
    }

    public async provide(): Promise<Map<groupKey, I[]>> {
        const result = new Map<groupKey, I[]>();

        const resources = await this.supplier();
        for (const resource of resources) {
            if (this.filter) {
                if (!this.filter(resource)) {
                    continue;
                }
            }
            const key = this.grouper(resource);
            const group = result.get(key) || [];
            group.push(resource);
            result.set(key, group);
        }
        return result;
    }
}

class AsyncStreamForker<I, O> implements Forker<I, O>, ResourceProvider<O[]> {
    private forker: (group: groupKey, resource: I[]) => Promise<O>;

    constructor(private readonly supplier: ResourceProvider<Map<groupKey, I[]>>) {
    }

    public fork(forker: (group: groupKey, resource: I[]) => Promise<O>): Joiner<I, O> {
        this.forker = forker;
        return new AsyncStreamJoiner(this);
    }

    public async provide(): Promise<O[]> {
        const result: Promise<O>[] = [];
        const groups = await this.supplier.provide();
        for (const [key, resources] of groups.entries()) {
            const out = this.forker(key, resources);
            result.push(out);
        }
        return Promise.all(result);
    }
}

class AsyncStreamJoiner<I, O> implements Joiner<I, O> {

    constructor(private readonly supplier: ResourceProvider<O[]>) {
    }

    public async join(joiner: (acc: O, cur: O) => void, fallback?: () => O): Promise<O> {
        const data = await this.supplier.provide();
        if (!data || !Array.isArray(data) || data.length === 0) {
            if (fallback) {
                return fallback();
            } else {
                return undefined;
            }
        }
        return data.reduce((a, c) => {
            joiner(a, c);
            return a;
        });

    }
}
