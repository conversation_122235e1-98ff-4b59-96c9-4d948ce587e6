export interface JackpotFilterParams {
    siteCode?: string;
    currency?: string;
}

export class JackpotFilterBuilder {
    public static readonly SITE_CODE = new RegExp("{siteCode}", "g");
    public static readonly CURRENCY = new RegExp("{currency}", "g");

    public static build(template: string, params: JackpotFilterParams): string {
        if (params.siteCode) {
            template = template.replace(JackpotFilterBuilder.SITE_CODE, params.siteCode);
        }
        if (params.currency) {
            template = template.replace(JackpotFilterBuilder.CURRENCY, params.currency);
        }
        return template;
    }
}
