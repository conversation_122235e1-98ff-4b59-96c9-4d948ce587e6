import { BaseEntity, EntityStatus } from "../entities/entity";
import * as Errors from "../errors";
import { BrandEntity } from "../entities/brand";

export function validateEntityStatus(entity: BaseEntity) {
    if (entity.status === EntityStatus.BLOCKED_BY_ADMIN) {
        throw new Errors.ValidationError(`${entity.name} has been blocked by Admin`);
    }
}

export async function validateEntityTestStatus(entity: BaseEntity) {
    const isNotBrandOrNotMerchant = !entity.isBrand() && !(entity as BrandEntity).isMerchant;
    if (isNotBrandOrNotMerchant) {
        throw new Errors.ValidationError(`${entity.name} should be brand or merchant`);
    }
    if (entity.isTest) {
        throw new Errors.ValidationError(`${entity.name} should be not a test`);
    }
}
