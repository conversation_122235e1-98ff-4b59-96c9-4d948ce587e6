import { Sequelize, ModelStatic, QueryTypes, Model, FindAndCountOptions, FindOptions } from "sequelize";
import { ServerResponse } from "node:http";
import { executeLongQuery } from "../storage/db";

const MAX_INTEGER = 2147483647;

export type WithPagingInfo<T = unknown> = T[] & PagingInfo[];

export interface PagingInfo {
    total: number;
    limit: number;
    offset: number;
}

export interface BodyPagingInfo<T = any> extends PagingInfo {
    items?: T[];
}

export type DBToResultMapper<TInstance, Result> = (instance: TInstance) => Result;
type ExtendedFindOptions = FindAndCountOptions<any> & { useMaster?: boolean };

export class PagingHelper {
    public static async findAndCountAll<T, TInstance extends Model, TAttributes>(
        model: ModelStatic<TInstance>,
        findOptions: ExtendedFindOptions,
        mapper: DBToResultMapper<TInstance, T>,
        disableDistinct: boolean = false // distinct works not correctly for composite primary key
    ): Promise<WithPagingInfo<T>> {
        if (findOptions.include && !disableDistinct) {
            findOptions.distinct = true;
        }
        const rowsAndCount = await model.findAndCountAll(findOptions);
        const rows = rowsAndCount.rows.map(mapper);
        return PagingHelper.fillInfo(rows, rowsAndCount.count, findOptions);
    }

    public static async findAsyncAndCountAll<T, TInstance extends Model, TAttributes>(
        model: ModelStatic<TInstance>,
        findOptions: ExtendedFindOptions,
        mapper: DBToResultMapper<TInstance, Promise<T>>,
        disableDistinct: boolean = false // distinct works not correctly for composite primary key
    ): Promise<WithPagingInfo<T>> {
        if (findOptions.include && !disableDistinct) {
            findOptions.distinct = true;
        }
        const rowsAndCount = await model.findAndCountAll(findOptions);
        const rows: T[] = await Promise.all(rowsAndCount.rows.map(mapper));
        return PagingHelper.fillInfo(rows, rowsAndCount.count, findOptions);
    }

    public static hasPagingInfo(obj: any): boolean {
        return obj[PagingHelper.PAGING_INFO] !== undefined;
    }

    public static fillHeaders(resp: ServerResponse, obj: any): void {
        if (obj) {
            const info: PagingInfo = obj[PagingHelper.PAGING_INFO];
            const total: number = Array.isArray(info.total) ? info.total.length : info.total;
            resp.setHeader("x-paging-total", total.toString());
            resp.setHeader("x-paging-limit", info.limit.toString());
            resp.setHeader("x-paging-offset", info.offset.toString());
        }
    }

    public static copyInfo<T>(target: T[], source: any[] & PagingInfo[]): WithPagingInfo<T> {
        target[PagingHelper.PAGING_INFO] = source[PagingHelper.PAGING_INFO];
        return target as WithPagingInfo<T>;
    }

    private static PAGING_INFO: string = "PAGING_INFO";

    public static fillInfo<T>(rows: T[], count: number, options: FindOptions<any>): WithPagingInfo<T> {
        const result: WithPagingInfo<T> = (rows as any);

        result[PagingHelper.PAGING_INFO] = { total: count, limit: options.limit || 0, offset: options.offset || 0 };
        return result;
    }

    public static getPagingInfo(obj: any): PagingInfo {
        return obj[PagingHelper.PAGING_INFO];
    }

    public static async findAndCountAllNative<T, TInstance extends Model, TAttributes>(
        selectQuery: string, countQuery: string,
        db: Sequelize,
        model: ModelStatic<TInstance>,
        limit: number,
        offset: number,
        mapper: DBToResultMapper<TInstance, T>
    ): Promise<WithPagingInfo<T>> {
        return executeLongQuery(db, async (trx): Promise<WithPagingInfo<T>> => {
            const countQueryResult: any = await db.query(countQuery,
                {
                    replacements: { limit: MAX_INTEGER, offset: 0 },
                    plain: true,
                    type: QueryTypes.SELECT,
                    transaction: trx
                });
            const count = +countQueryResult.count;
            const rows = await db.query(selectQuery,
                {
                    replacements: { limit, offset },
                    model,
                    mapToModel: true,
                    type: QueryTypes.SELECT,
                    transaction: trx
                });
            return PagingHelper.fillInfo(rows.map(mapper), count, { limit, offset }) as any;
        });
    }

    public static async findAllNative<T, TInstance extends Model, TAttributes>(
        query: string,
        db: Sequelize,
        model: ModelStatic<TInstance>,
        limit: number,
        offset: number,
        mapper: DBToResultMapper<TInstance, T>
    ): Promise<T[]> {
        return executeLongQuery(db, async (trx): Promise<T[]> => {
            const rows: TInstance[] = await db.query(query,
                {
                    replacements: { limit, offset },
                    model,
                    mapToModel: true,
                    type: QueryTypes.SELECT,
                    transaction: trx
                });
            return rows.map(mapper);

        });
    }

    public static async findAsyncAllNative<T, TInstance extends Model, TAttributes>(
        query: string,
        db: Sequelize,
        model: ModelStatic<TInstance>,
        limit: number,
        offset: number,
        mapper: DBToResultMapper<TInstance, Promise<T>>,
        withSequelizeReplacements = true,
    ): Promise<T[]> {
        return executeLongQuery(db, async (trx): Promise<T[]> => {
            const queryOptions = {
                replacements: { limit, offset },
                model,
                mapToModel: true,
                type: QueryTypes.SELECT,
                transaction: trx
            };
            if (!withSequelizeReplacements) {
                delete queryOptions.replacements;
            }
            const rows: TInstance[] = await db.query(query, queryOptions);

            return Promise.all(rows.map(mapper));
        });
    }

    public static fillBody(data: any): BodyPagingInfo {
        if (data) {
            const info: PagingInfo = data[PagingHelper.PAGING_INFO];
            const total: number = Array.isArray(info.total) ? info.total.length : info.total;
            return {
                offset: +info.offset,
                limit: +info.limit,
                total,
                items: data
            };
        }
    }
}
