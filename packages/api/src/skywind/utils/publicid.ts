import { publicId } from "@skywind-group/sw-utils";

export const encodeId = (id: number | string) => publicId.instance.encode(id);
export const decodeId = (id: number | string) => publicId.instance.decode(id);
const encodeArrayOfIds = (id): string => {
    if (typeof id === "number") {
        return encodeId(id);
    }
    return id;
};
export const isPrivateKey = (key: string): boolean => {
    const sensitiveKeys = ["id"];

    return key.endsWith("Id") || key.endsWith("PID") || sensitiveKeys.indexOf(key) !== -1;
};
export const encodeEachInObject = (obj: any) => {
    const excludedKeys = ["eventId"];
    if (!obj) {
        return obj;
    }
    for (const key of Object.keys(obj)) {
        if (!isPrivateKey(key) || excludedKeys.includes(key)) {
            continue;
        }
        if (typeof obj[key] === "number") {
            obj[key] = encodeId(obj[key]);
        } else if (Array.isArray(obj[key])) {
            obj[key] = obj[key].map(encodeArrayOfIds);
        }
    }
    for (const key of Object.keys(obj)) {
        if (typeof obj[key] !== "object") {
            continue;
        }
        if (key === "details" || key === "initSettings") {
            continue;
        }
        encodeEachInObject(obj[key]);
    }
    return obj;
};
