import { Storage, StorageOptions, DownloadResponse, DownloadOptions } from "@google-cloud/storage";
import { InternalServerError } from "../errors";
import { logging } from "@skywind-group/sw-utils";

const log = logging.logger("google-bucket");

export class GoogleBucket {
    private bucketName: string;
    private storage: Storage;

    constructor(bucketName: string, options: StorageOptions) {
        this.bucketName = bucketName;
        this.storage = new Storage(options);
    }

    public async downloadFile(srcFileName: string, distFileName: string): Promise<DownloadResponse> {
        try {
            await this.fileExists(srcFileName);

            const options: DownloadOptions = {
                // The path to which the file should be downloaded, e.g. "./file.txt"
                destination: distFileName
            };
            return await this.storage.bucket(this.bucketName).file(srcFileName).download(options);
        } catch (error) {
            const err = new InternalServerError(`Failed to download file ${srcFileName}: ${error.message}`);
            log.error(err);
            throw err;
        }
    }

    private async fileExists(srcFileName: string): Promise<void> {
        const data = await this.storage.bucket(this.bucketName).file(srcFileName).exists();

        if (data && data[0]) {
            return;
        }
        throw new InternalServerError(`File ${srcFileName} doesn't exist`);
    }
}
