import { BAD_TRANSACTION_ID, ITrxId } from "@skywind-group/sw-wallet";
import { WalletErrors, WalletFacade } from "@skywind-group/sw-management-wallet";

export const sanitizeTrxId = async <T, P extends keyof T>(data: T, prop: P) =>  {
    const transactionId: any = data[prop];
    const trxId: ITrxId = await WalletFacade.parseTransactionId(transactionId);

    try {
        data[prop] = trxId.timestamp && (trxId as any);
    } catch (err) {
        if (err === BAD_TRANSACTION_ID) {
            return Promise.reject(new WalletErrors.BadTransactionId());
        }

        return Promise.reject(err);
    }
};
