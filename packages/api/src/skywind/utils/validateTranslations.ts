import { LANGUAGES as SUPPORTED_LANGUAGES } from "./common";

/**
 *
 * @param translations
 * @param fieldTitleName   This field adds a label to the existing error message for nested object
 * @param validateCallback A callback function that adds custom validate
 */
export function validateTranslations(
    translations: any,
    fieldTitleName: string = "",
    validateCallback?: (translationItem: any,
                        availableTranslationFields: string[],
                        language: string,
                        errors: string[]) => void ): string[] {
    const label: string = fieldTitleName ? `${fieldTitleName}.` : "";
    if (typeof translations !== "object" || Array.isArray(translations)) {
        return [ `${label}translations should be an object` ];
    }

    const languages = Object.keys(translations);
    if (!languages.length) {
        return [];
    }

    const errors: string[] = [];
    for (const language of languages) {
        if (!SUPPORTED_LANGUAGES[language]) {
            errors.push(`${label}translations.${language} is unsupported`);
            continue;
        }

        const translationItem = translations[language];
        if (typeof translationItem !== "object" || Array.isArray(translationItem)) {
            errors.push(`${label}translations.${language} should be an object`);
            continue;
        }

        if (!translationItem.title || typeof translationItem.title !== "string") {
            errors.push(`${label}translations.${language}.title is required and should be a string`);
        }

        if ("description" in translationItem && typeof translationItem.description !== "string") {
            errors.push(`${label}translations.${language}.description should be a string`);
        }

        const availableTranslationItemField = ["title", "description"];
        if (validateCallback) {
            validateCallback(translationItem, availableTranslationItemField, language, errors);
        }
        const invalidFields = Object.keys(translationItem).filter(key => !availableTranslationItemField.includes(key));
        if (invalidFields.length) {
            errors.push(`${label}translations.${language} have invalid keys - ${invalidFields}`);
        }
    }

    return errors;
}

export function validateTranslateIconField(translationItem: any,
                                           availableFields: string[],
                                           language: string,
                                           existErrors: string[]) {
    availableFields.push("icon");
    if ("icon" in translationItem && typeof translationItem.icon !== "string") {
        existErrors.push(`translations.${language}.icon should be a string`);
    }
}
