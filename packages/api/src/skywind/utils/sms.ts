// require the <PERSON>wilio module and create a REST client
import config from "../config";

import logger from "./logger";
import * as Errors from "../errors";

const log = logger("sms");

const client = require("twilio")(config.twilio.accountSid, config.twilio.accountToken);

export async function sendAuthCodeSms(toNumber: string,
                                      authCode: string,
                                      username: string,
                                      smsTemplate: string): Promise<void> {
    return new Promise<void>((resolve, reject) => {
        client.messages.create({
            to: toNumber,
            from: config.twilio.accountPhoneNumber,
            body: smsTemplate.replace("{{authCode}}", authCode).replace("{{username}}", username)
        }, function (err, message) {
            if (err) {
                log.error(err.message, `An error occurred when sending sms to: ${toNumber}`);
                reject(new Errors.SmsSendingError());
            }
            log.debug("Successfully sent sms auth code to: " + `${toNumber}`);
            resolve();
        });
    });
}
