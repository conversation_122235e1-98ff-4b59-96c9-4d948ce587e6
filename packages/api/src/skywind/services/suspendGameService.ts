import { Lazy, lazy, measures, redis } from "@skywind-group/sw-utils";
import { ChainableCommander, Redis as RedisClient } from "ioredis";
import { getRedisPool } from "../storage/redis";
import { OperatorSuspendedGames } from "@skywind-group/sw-management-gameprovider";
import measure = measures.measure;

export interface SuspendGameService {
    suspendGames(ids: number[], gameCode: string[]): Promise<void>;

    restoreGames(ids: number[], gameCode: string[]): Promise<void>;

    getSuspendedGame(id: number): Promise<OperatorSuspendedGames>;
}

export class DefaultSuspendGameService implements SuspendGameService {
    constructor(private readonly pool: redis.RedisPool<RedisClient>) {
    }

    public async suspendGames(ids: number[], gameCode: string[]): Promise<void> {
        const ts = Date.now();
        return this.pool.usingDb<void>(async (client: RedisClient) => {
            const pipeLine: ChainableCommander = client.multi();
            for (const id of ids) {
                pipeLine.sadd(this.suspendedGames(id), gameCode);
                pipeLine.incrby(this.suspendedGamesVersion(id), 1);
            }
            await pipeLine.exec();
        });
    }

    public async restoreGames(ids: number[], gameCode: string[]): Promise<void> {
        const ts = Date.now();
        return this.pool.usingDb<void>(async (client: RedisClient) => {
            const pipeLine: ChainableCommander = client.multi();
            for (const id of ids) {
                pipeLine.srem(this.suspendedGames(id), gameCode);
                pipeLine.incrby(this.suspendedGamesVersion(id), 1);
            }
            await pipeLine.exec();
        });
    }

    @measure({ name: "SuspendGameService.getSuspendedGames", isAsync: true })
    public async getSuspendedGame(id: number): Promise<OperatorSuspendedGames> {
        return this.pool.usingDb<OperatorSuspendedGames>(async (client: RedisClient) => {
            const version = await client.get(this.suspendedGamesVersion(id));
            const games: string[] = await client.smembers(this.suspendedGames(id));
            return { games: games.sort(), version: +(version || 0) };
        });
    }

    private suspendedGames(brandId: number) {
        return `suspendedGames:${brandId}`;
    }

    private suspendedGamesVersion(brandId: number) {
        return `suspendedGames:${brandId}:version`;
    }
}

export const defaultSuspendGameService: Lazy<SuspendGameService>
    = lazy<SuspendGameService>(() => new DefaultSuspendGameService(getRedisPool()));
