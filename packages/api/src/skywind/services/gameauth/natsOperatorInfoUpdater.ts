import { UpdateGamesStatus, OperatorInfo, OperatorInfoUpdater } from "@skywind-group/sw-management-gameprovider";
import { Messaging } from "@skywind-group/sw-messaging";

/**
 * Propagate changes to operator-info repository (gameprovider-api)
 */
export class NatsOperatorInfoUpdater implements OperatorInfoUpdater {
    constructor(private readonly messaging: Messaging,
                private readonly channelPrefix: string) {
    }

    public async removeByPath(path: string): Promise<void> {
        return this.messaging.publish(`${this.channelPrefix}.removeByPath`, path);
    }

    public async update(info: OperatorInfo): Promise<void> {
        return this.messaging.publish(`${this.channelPrefix}.update`, info);
    }

    public async restoreGame(req: UpdateGamesStatus): Promise<void> {
        return this.messaging.publish(`${this.channelPrefix}.restoreGame`, req);
    }

    public async suspendGame(req: UpdateGamesStatus): Promise<void> {
        return this.messaging.publish(`${this.channelPrefix}.suspendGame`, req);
    }
}
