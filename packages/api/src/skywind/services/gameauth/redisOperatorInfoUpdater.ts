import { Redis } from "ioredis";
import { redis } from "@skywind-group/sw-utils";
import { UpdateGamesStatus, OperatorInfo, OperatorInfoUpdater } from "@skywind-group/sw-management-gameprovider";

/**
 * Propagate changes to operator-info repository (gameprovider-api)
 */
export class RedisOperatorInfoUpdater implements OperatorInfoUpdater {
    constructor(private readonly pool: redis.RedisPool<Redis>,
                private readonly channelPrefix: string) {
    }

    public async removeByPath(path: string): Promise<void> {
        return this.pool.usingDb<any>(async (client) => client.publish(`${this.channelPrefix}.removeByPath`, path));
    }

    public async update(info: OperatorInfo): Promise<void> {
        return this.pool.usingDb<any>(async (client) => client.publish(`${this.channelPrefix}.update`,
            JSON.stringify(info)));
    }

    public async restoreGame(req: UpdateGamesStatus): Promise<void> {
        return this.pool.usingDb<any>(async (client) => client.publish(`${this.channelPrefix}.restoreGame`,
            JSON.stringify(req)));
    }

    public async suspendGame(req: UpdateGamesStatus): Promise<void> {
        return this.pool.usingDb<any>(async (client) => client.publish(`${this.channelPrefix}.suspendGame`,
            JSON.stringify(req)));
    }
}
