import { GameSessionFactory } from "@skywind-group/sw-management-gameprovider";
import { EntityGame } from "../../entities/game";
import * as GameService from "../game";
import { getBrand } from "../playService";
import { getEntitySettings } from "../settings";
import { GameTokenData } from "@skywind-group/sw-wallet-adapter-core";
import { Lazy, lazy } from "@skywind-group/sw-utils";
import { EntitySettings } from "../../entities/settings";
import { createPlayerSession } from "../playService";

/**
 *  Create game session information:
 *   - find brand
 *   - find game
 *   - check promotions
 */
export class DefaultGameSessionFactory implements GameSessionFactory<any> {
    public async create(gameTokenData: GameTokenData): Promise<any> {
        const brand = await getBrand(gameTokenData.brandId);
        const settings: EntitySettings = await getEntitySettings(brand.path);
        const entityGame: EntityGame = await GameService.findOneEntityGame(brand, gameTokenData.gameCode);
        return createPlayerSession(brand, gameTokenData, settings, entityGame);
    }
}

// todo aguzanov consider to change to DI approach
export const defaultGameSessionFactory: Lazy<GameSessionFactory<any>> = lazy(() => new DefaultGameSessionFactory());
