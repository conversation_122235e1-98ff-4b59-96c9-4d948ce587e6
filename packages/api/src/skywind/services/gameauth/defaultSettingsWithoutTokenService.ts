import { getBrand, isTransferEnabled } from "../playService";
import { BrandEntity } from "../../entities/brand";
import { EntityGame } from "../../entities/game";
import * as GameService from "../../services/game";
import * as MerchantCache from "../../cache/merchant";
import { Lazy, lazy } from "@skywind-group/sw-utils";
import {
    OperatorInfoRepository,
    ProviderAuthDetails,
    SettingsRequest,
    SettingsWithoutToken,
    SettingsWithoutTokenService
} from "@skywind-group/sw-management-gameprovider";
import { getEntitySettings } from "../settings";
import { defaultOperatorDetailsRepository } from "./defaultOperatorInfoRepository";
import { verifyProviderSecret } from "../security";
import { EntitySettings } from "../../entities/settings";

export class DefaultSettingsWithoutTokenService implements SettingsWithoutTokenService {
    constructor(public readonly operatorInfoRepository: OperatorInfoRepository) {
    }

    public async getSettings(auth: ProviderAuthDetails, request: SettingsRequest): Promise<SettingsWithoutToken> {
        await this.auth(auth);
        const brand: BrandEntity = await getBrand(request.brandId);
        const settings: EntitySettings = await getEntitySettings(brand.path);
        const operatorInfo = await this.operatorInfoRepository.findById(brand.id);

        if (!request.gameCode) {
            return {
                operatorInfo,
                transferEnabled: false,
                extHistorySettings: {
                    mustStoreExternalBetWinHistory: false,
                    isNewExternalBetWinHistory: false,
                    offlineWin: false
                }
            };
        }

        const entityGame: EntityGame = await GameService.findOneEntityGame(brand, request.gameCode);

        const transferEnabled: boolean = brand.isMerchant ?
                                         isTransferEnabled(entityGame,
                                             await MerchantCache.findOne(brand), request.playMode) :
                                         false;

        const mustStoreExternalBetWinHistory: boolean =
            entityGame.game.gameProvider && entityGame.game.gameProvider.mustStoreExtHistory;

        const isNewExternalBetWinHistory: boolean = settings.newExternalBetWinHistoryEnabled;

        return {
            operatorInfo,
            transferEnabled,
            extHistorySettings: {
                mustStoreExternalBetWinHistory,
                isNewExternalBetWinHistory,
                gameProviderCode: entityGame.game.gameProvider.code,
                offlineWin: entityGame.game.features.offlineWin
            }
        };
    }

    public async auth(auth: ProviderAuthDetails): Promise<void> {
        await verifyProviderSecret(auth.providerUser, auth.providerCode, auth.providerSecret);
    }
}

// todo aguzanov consider to change to DI approach
export const defaultSettingsWithoutTokenService: Lazy<SettingsWithoutTokenService> =
    lazy(() => new DefaultSettingsWithoutTokenService(defaultOperatorDetailsRepository.get()));
