import { doStartFunGame, doStartGame } from "../playService";
import { Lazy, lazy } from "@skywind-group/sw-utils";
import {
    StartFunGameRequest,
    StartGameRequest,
    StartGameResult,
    StartGameService
} from "@skywind-group/sw-management-gameprovider";
import { runWithAsyncLocalWallet } from "@skywind-group/sw-management-wallet";
import { RawWallet } from "@skywind-group/sw-wallet";
import config from "../../config";

export class DefaultStartGameService implements StartGameService {

    public async startGame(authRequest: StartGameRequest,
                           ip?: string,
                           referrer?: string,
                           wallet?: RawWallet): Promise<StartGameResult> {
        if (config.asyncLocalWalletEnabled) {
            return runWithAsyncLocalWallet({ wallet }, () => {
                return doStartGame(authRequest, ip, referrer);
            });
        }
        return doStartGame(authRequest, ip, referrer);
    }

    public startFunGame(startRequest: StartFunGameRequest): Promise<StartGameResult> {
        return doStartFunGame(startRequest);
    }
}

export const defaultStartGameService: Lazy<StartGameService> = lazy(() => new DefaultStartGameService());
