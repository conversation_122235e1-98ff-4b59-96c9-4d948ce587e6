import { OperatorInfoUpdater } from "@skywind-group/sw-management-gameprovider";
import config from "../../config";
import { getRedisPool } from "../../storage/redis";
import { NatsOperatorInfoUpdater } from "./natsOperatorInfoUpdater";
import { RedisOperatorInfoUpdater } from "./redisOperatorInfoUpdater";
import { createMessaging } from "@skywind-group/sw-messaging";
import { lazy, Lazy } from "@skywind-group/sw-utils";

export const defaultOperatorInfoUpdater: Lazy<OperatorInfoUpdater> =
    lazy(() => {

        if (config.operatorInfoNotification.type === "redis") {
            return new RedisOperatorInfoUpdater(getRedisPool(), config.operatorInfoNotification.channelPrefix);
        } else if (config.operatorInfoNotification.type === "nats") {
            return new NatsOperatorInfoUpdater(createMessaging(config.nats),
                config.operatorInfoNotification.channelPrefix);
        } else {
            throw new Error("Unknown operator notification type");
        }
    });
