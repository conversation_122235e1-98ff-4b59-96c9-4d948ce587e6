import * as MerchantCache from "../../cache/merchant";
import { getBrand } from "../playService";
import { Lazy, lazy } from "@skywind-group/sw-utils";
import { MerchantTypeImpl } from "../merchantType";
import * as MerchantTypesCache from "../../cache/merchantTypes";
import { OperatorInfo, OperatorInfoRepository } from "@skywind-group/sw-management-gameprovider";
import { defaultSuspendGameService, SuspendGameService } from "../suspendGameService";

/**
 * Base implementation of OperatorInfoRepository.
 *
 */
export class DefaultOperatorInfoRepository {
    constructor(private readonly suspendGameService: SuspendGameService) {
    }

    public async findById(id: number, ignoreValidations?: boolean): Promise<OperatorInfo> {
        const brand = await getBrand(id, ignoreValidations);
        const suspendedGames = await this.suspendGameService.getSuspendedGame(id);
        if (brand.isMerchant) {
            const merchant = await MerchantCache.findOne(brand);
            const merchantType: MerchantTypeImpl = await MerchantTypesCache.findOne(merchant.type);
            const url = merchantType?.url;

            const typeDetails = { ...merchantType, isInternalAdapter: !url };
            return {
                id,
                path: brand.path,
                info: merchant.toInfo(),
                typeInfo: {
                    type: merchant.type, typeDetails,
                },
                suspendedGames,
                version: merchant.version
            };
        }

        return { id, path: brand.path, suspendedGames, version: brand.getVersion() };
    }
}

// todo aguzanov consider DI approach
export const defaultOperatorDetailsRepository: Lazy<OperatorInfoRepository>
    = lazy(() => new DefaultOperatorInfoRepository(defaultSuspendGameService.get()));
