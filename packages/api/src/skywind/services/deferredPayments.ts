import { lazy, Lazy } from "@skywind-group/sw-utils";
import { createDeferredPaymentFacade, DeferredPaymentFacade } from "@skywind-group/sw-management-deferredpayment";
import { getRedisPool } from "../storage/redis";
import config from "../config";
import { WalletFacade } from "@skywind-group/sw-management-wallet";
import { defaultOperatorDetailsRepository } from "./gameauth/defaultOperatorInfoRepository";
import {
    DeferredPaymentRegistrationService,
    DeferredPaymentRegistrationServiceImpl
} from "./deferredPaymentRegistrationService";
import { DefaultPlayServiceFactory } from "@skywind-group/sw-management-gameprovider-core";

export const defaultDeferredPaymentFacade: Lazy<DeferredPaymentFacade> = lazy(() =>
    createDeferredPaymentFacade({
        on: config.deferredPayment.on,
        url: config.deferredPayment.url,
        secret: config.internalServerToken.secret,
        cacheExpireIn: config.startGameToken.expiresIn,
        keepAlive: config.deferredPayment.keepAlive,
        retries: config.deferredPayment.retries,
        requestTimeout: config.deferredPayment.requestTimeout
    }, getRedisPool())
);

export const defaultDeferredRegistrationService: Lazy<DeferredPaymentRegistrationService> = lazy(() =>
    new DeferredPaymentRegistrationServiceImpl(defaultDeferredPaymentFacade.get(),
        new DefaultPlayServiceFactory(),
        defaultOperatorDetailsRepository.get(),
        WalletFacade)
);
