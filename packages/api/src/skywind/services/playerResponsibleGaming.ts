import * as Errors from "../errors";
import { literal, Op, Transaction, WhereOptions } from "sequelize";
import { sequelize as db } from "../storage/db";
import { PagingHelper } from "../utils/paginghelper";
import { BaseEntity } from "../entities/entity";
import {
    PlayerResponsibleGaming,
    PlayerResponsibleGamingDBInstance,
    PlayerResponsibleGamingSettings,
    PlayerResponsibleGamingSettingsDBInstance,
    PlayerResponsibleGamingSuspensionInfo,
    ResponsibleGamingSettingsCreate,
    ResponsibleGamingSettingsType,
    UKResponsibleGamingSettings
} from "../models/playerResponsibleGamingSettings";
import { Models } from "../models/models";
import config from "../config";
import { EntityResponsibleGamingSettings, EntitySettings } from "../entities/settings";
import EntitySettingsService, { getEntitySettings } from "./settings";
import * as PlayerRespGamingCache from "../cache/playerResponsibleGamingSettings";
import getBlockPlayerService from "./blockedPlayer";
import { BrandEntity } from "../entities/brand";
import * as FilterService from "../services/filter";
import { APPROVED, PM_TYPE_DEPOSIT, TRANSFER_IN } from "../entities/payment";
import { createPlayerSessionFacade, PlayerSessionFacade } from "./player/playerSessionFacade";
import { AdapterErrors } from "@skywind-group/sw-management-adapters";
import { cloneDeep } from "lodash";
import { SELF_EXCLUSION_COUNT_DB_FIELD } from "../entities/player";

const PlayerModel = Models.PlayerModel;
export const SELF_EXCLUSION_MAX_ATTEMPTS = 2;

export interface PlayerResponsibleGamingUpdateSettings {
    casino?: ResponsibleGamingSettingsCreate;
    sport_bet?: ResponsibleGamingSettingsCreate;
}

export type PlayerRegulationSetting = "realityCheck" | "lossLimit" | "depositLimit" |
    "casinoTimeoutTillDate" | "selfExclusionTillDate";

export type ResponsibleGamingTimeframe = "daily" | "weekly" | "monthly";

const SETTINGS_TYPE = ["sport_bet", "casino"];

export const getPlayerResponsibleGamingService = (entity: BaseEntity): PlayerResponsibleGamingService => {
    const playerSessionService: PlayerSessionFacade = createPlayerSessionFacade();
    return new PlayerResponsibleGamingServiceImpl(entity, playerSessionService);
};

export interface PlayerResponsibleGamingService {
    checkAndGetPlayerResponsibleGamingSettings(playerCode): Promise<PlayerResponsibleGaming>;

    searchPlayerResponsibleGamingSettings(query: WhereOptions<any>): Promise<PlayerResponsibleGaming[]>;

    getExclusionAndTimeoutReport(query: WhereOptions<any>): Promise<PlayerResponsibleGamingSuspensionInfo[]>;

    getEntityResponsibleGamingSettings(entity: BaseEntity): Promise<EntityResponsibleGamingSettings>;

    checkEntityHasResponsibleGamingEnabled(responsibleGamingSettings: EntityResponsibleGamingSettings): Promise<void>;

    deleteResponsibleGamingPendingChange(playerCode, settings: PlayerResponsibleGamingUpdateSettings):
        Promise<PlayerResponsibleGaming>;

    updatePlayerResponsibleGamingSettings(playerCode, settings: PlayerResponsibleGamingUpdateSettings):
        Promise<PlayerResponsibleGaming>;

    validatePlayerRestrictions(playerCode: string, settingsType?: ResponsibleGamingSettingsType):
        Promise<PlayerResponsibleGamingImpl>;

    validatePlayerSelfExclusionRestriction(playerCode: string): Promise<PlayerResponsibleGamingImpl>;

    validatePlayerDepositRestriction(playerCode: string, depositAmount: number): Promise<PlayerResponsibleGamingImpl>;

    validateSettings(settings: PlayerResponsibleGamingUpdateSettings): Promise<void>;
}

export default class PlayerResponsibleGamingServiceImpl implements PlayerResponsibleGamingService {

    public readonly sortableKeys = ["playerCode", "jurisdiction"];
    public readonly DEFAULT_SORT_KEY = "playerCode";
    public static readonly queryParamsKeys = [
        "sortBy",
        "sortOrder",
        "offset",
        "limit",
        "playerCode",
        "jurisdiction",
        "casinoTimeoutTillDate",
        "selfExclusionTillDate",
        "isOnTimeout",
        "isSelfExcluded",
        "type"
    ];

    public readonly exclusionSortableKeys = ["playerCode", "jurisdiction", "createdAt", ];
    public static readonly exclusionParamsKeys = [
        "sortBy",
        "sortOrder",
        "offset",
        "limit",
        "playerCode",
        "endTime",
        "createdAt",
        "suspensionTypes"
    ];

    public static readonly suspensionType = [
        "time-out",
        "self-exclusion"
    ];

    public static readonly suspensionFieldsKeys = {
        "time-out": "settings.casinoTimeoutTillDate",
        "self-exclusion": "settings.selfExclusionTillDate"
    };

    private readonly TimeframeStrictnessWeighs = new Map<string, number>([
        ["daily", 5],
        ["weekly", 4],
        ["monthly", 3]
    ]);

    private readonly TimeframeDaysMap = new Map<string, number>([
        ["daily", 1],
        ["weekly", 7],
        ["monthly", 30]
    ]);

    constructor(private entity: BaseEntity,
                private playerSessionService: PlayerSessionFacade = createPlayerSessionFacade()) {
    }

    public async checkAndGetPlayerResponsibleGamingSettings(playerCode): Promise<PlayerResponsibleGaming> {
        const responsibleGamingSettings = await this.getEntityResponsibleGamingSettings(this.entity);
        await this.checkEntityHasResponsibleGamingEnabled(responsibleGamingSettings);

        const settings = await PlayerRespGamingCache.findOne(playerCode, this.entity.id,
            responsibleGamingSettings.jurisdiction);
        return settings ? settings.toInfo() : {};
    }

    public async searchPlayerResponsibleGamingSettings(query: WhereOptions<any>): Promise<PlayerResponsibleGaming[]> {
        const responsibleGamingSettings = await this.getEntityResponsibleGamingSettings(this.entity);
        await this.checkEntityHasResponsibleGamingEnabled(responsibleGamingSettings);

        query["brandId"] = this.entity.id;
        const settingsQuery = {};
        if (query["isOnTimeout"]) {
            delete query["isOnTimeout"];
            settingsQuery["settings.casinoTimeoutTillDate"] = { [Op.ne]: null, [Op.gt]: new Date() };
        }
        if (query["isSelfExcluded"]) {
            delete query["isSelfExcluded"];
            settingsQuery["settings.selfExclusionTillDate"] = { [Op.ne]: null, [Op.gt]: new Date() };
        }

        if (query["casinoTimeoutTillDate"]) {
            settingsQuery["settings.casinoTimeoutTillDate"] = query["casinoTimeoutTillDate"];
            delete query["casinoTimeoutTillDate"];
        }
        if (query["selfExclusionTillDate"]) {
            settingsQuery["settings.selfExclusionTillDate"] = query["selfExclusionTillDate"];
            delete query["selfExclusionTillDate"];
        }
        if (query["type"]) {
            settingsQuery["type"] = query["type"];
            delete query["type"];
        }

        const sortBy = FilterService.getSortKey(query, this.sortableKeys, this.DEFAULT_SORT_KEY);
        const sortOrder = FilterService.valueFromQuery(query, "sortOrder") || "ASC";

        return PagingHelper.findAsyncAndCountAll(Models.PlayerResponsibleGamingModel, {
            where: query,
            offset: FilterService.valueFromQuery(query, "offset"),
            limit: FilterService.valueFromQuery(query, "limit"),
            order: literal(`"${sortBy}" ${sortOrder}`),
            include: [{
                model: Models.PlayerResponsibleGamingSettingsModel,
                where: settingsQuery,
                as: "settings"
            }]
        }, instance => Promise.resolve(new PlayerResponsibleGamingImpl(instance).toInfo()));
    }

    public async getExclusionAndTimeoutReport(
        query: WhereOptions<any>
    ): Promise<PlayerResponsibleGamingSuspensionInfo[]> {
        const responsibleGamingSettings = await this.getEntityResponsibleGamingSettings(this.entity);
        await this.checkEntityHasResponsibleGamingEnabled(responsibleGamingSettings);

        query["brandId"] = this.entity.id;

        const settingsQuery = this.buildPRGSettingsQuery(query as any);

        const sortBy = FilterService.getSortKey(query, this.exclusionSortableKeys, this.DEFAULT_SORT_KEY);
        const sortOrder = FilterService.valueFromQuery(query, "sortOrder") || "ASC";

        const playersList = (
            await Models.PlayerResponsibleGamingModel.findAll({
                where: query,
                offset: FilterService.valueFromQuery(query, "offset"),
                limit: FilterService.valueFromQuery(query, "limit"),
                order: literal(`"${sortBy}" ${sortOrder}`),
                include: [
                    {
                        model: Models.PlayerResponsibleGamingSettingsModel,
                        where: settingsQuery,
                        as: "settings"
                    }
                ]
            })
        ).map(instance => new PlayerResponsibleGamingImpl(instance as PlayerResponsibleGamingDBInstance).toInfo());
        const suspensionsList: PlayerResponsibleGamingSuspensionInfo[] = [];
        for (const player of playersList) {
            for (const type in player.settings) {
                if (!player.settings.hasOwnProperty(type)) {
                    continue;
                }
                const suspensionTypes: string[] = [];
                let endTime: Date = new Date();
                const info = player.settings[type];
                if (info.casinoTimeoutTillDate) {
                    suspensionTypes.push("time-out");
                    endTime = new Date(info.casinoTimeoutTillDate) > endTime ?
                        new Date(info.casinoTimeoutTillDate) : endTime;
                }
                if (info.selfExclusionTillDate) {
                    suspensionTypes.push("self-exclusion");
                    endTime = new Date(info.selfExclusionTillDate) > endTime ?
                        new Date(info.selfExclusionTillDate) : endTime;
                }
                suspensionsList.push({
                    playerCode: player.playerCode,
                    brandId: player.brandId,
                    productType: type,
                    suspensionTypes,
                    createdAt: player.createdAt,
                    endTime,
                });
            }
        }
        return suspensionsList;
    }

    private buildPRGSettingsQuery(query: {
        suspensionTypes: { $eq?: string, $in?: string[] },
        endTime: { $ne?: Date, $gt?: Date, $gte?: Date}
    }): WhereOptions<any> {

        const dateFilter = query.endTime || {};
        dateFilter[Op.ne] = null;
        if (!dateFilter[Op.gt] && !dateFilter[Op.gte]) {
            dateFilter[Op.gt] = new Date();
        }
        delete query.endTime;

        const settingsQuery: any = {};

        if ((query as any)?.suspensionTypes?.[Op.eq]) {
            const typeCode = PlayerResponsibleGamingServiceImpl.suspensionFieldsKeys[query.suspensionTypes[Op.eq]];
            settingsQuery[typeCode] = dateFilter;
            delete query.suspensionTypes;
        }
        if ((query as any)?.suspensionTypes?.[Op.in]) {
            settingsQuery[Op.or] = [];
            query.suspensionTypes[Op.in].map(type => {
                const typeCode = PlayerResponsibleGamingServiceImpl.suspensionFieldsKeys[type];
                const orValue = {};
                orValue[typeCode] = dateFilter;
                (settingsQuery[Op.or] as any).push(orValue);
            });

            delete query.suspensionTypes;
        } else {
            settingsQuery[Op.or] = {
                "settings.selfExclusionTillDate": dateFilter,
                "settings.casinoTimeoutTillDate": dateFilter,
            };
        }
        return settingsQuery;
    }

    public static async getPlayerRespGamingSettings(playerCode: string,
                                                    brandId: number,
                                                    gamingJurisdiction): Promise<PlayerResponsibleGamingImpl> {

        const dbInstance = await Models.PlayerResponsibleGamingModel.findOne(
            {
                where: { playerCode, brandId, jurisdiction: gamingJurisdiction },
                include: [{
                    model: Models.PlayerResponsibleGamingSettingsModel,
                    as: "settings"
                }]
            });
        return dbInstance ? new PlayerResponsibleGamingImpl(dbInstance) : undefined;
    }

    public async getEntityResponsibleGamingSettings(entity: BaseEntity): Promise<EntityResponsibleGamingSettings> {
        const settings = await (new EntitySettingsService(entity)).get();
        return settings.responsibleGaming;
    }

    public async checkEntityHasResponsibleGamingEnabled
    (responsibleGamingSettings: EntityResponsibleGamingSettings): Promise<void> {
        if (!responsibleGamingSettings || !responsibleGamingSettings.enabled) {
            return Promise.reject(new Errors.ResponsibleGamingIsNotEnabledError());
        }
    }

    public async deleteResponsibleGamingPendingChange(playerCode, settings: PlayerResponsibleGamingUpdateSettings):
        Promise<PlayerResponsibleGaming> {

        const respGamingSettings = await this.getEntityResponsibleGamingSettings(this.entity);
        await this.checkEntityHasResponsibleGamingEnabled(respGamingSettings);

        const settingsImpl = await PlayerRespGamingCache.findOne(
            playerCode, this.entity.id, respGamingSettings.jurisdiction);

        if (!settingsImpl) {
            return Promise.reject(new Errors.ResponsibleGamingNotFound());
        }

        if (settingsImpl.isSelfExcluded()) {
            return Promise.reject(
                new Errors.PlayerIsSelfExcludedError().setSelfExclusionDate(settingsImpl.getTimeoutDate()));
        }

        const type = Object.keys(settings)[0] as ResponsibleGamingSettingsType;
        const deletePendingData: ResponsibleGamingSettingsCreate = settings[type];
        const gamingSettings: PlayerResponsibleGamingSettingsImpl = settingsImpl.getSettingsByType(type);
        if (!gamingSettings) {
            return Promise.reject(new Errors.ResponsibleGamingNotFound("Game's responsible gaming settings not found"));
        }

        await this.validateDeletePendingData(gamingSettings.getSettings(), deletePendingData);
        const updateSettings = cloneDeep(gamingSettings.getSettings());

        if (deletePendingData.hasOwnProperty("lossLimit")) {
            delete updateSettings["lossLimitPending"];
            delete updateSettings["lossLimitPendingDate"];
            delete updateSettings["lossLimitPendingTimeframe"];
        } else if (deletePendingData.hasOwnProperty("depositLimit")) {
            delete updateSettings["depositLimitPending"];
            delete updateSettings["depositLimitPendingDate"];
            delete updateSettings["depositLimitPendingTimeframe"];
        } else {
            return settingsImpl.toInfo();
        }

        const updateResult = await Models.PlayerResponsibleGamingSettingsModel.update(
            { settings: updateSettings },
            {
                where: { id: settingsImpl.getId(), type: type },
                returning: true,
                fields: ["settings"]
            });

        if (updateResult[0] === 1) {
            gamingSettings.setSettings(updateSettings);
            PlayerRespGamingCache.reset(playerCode, this.entity.id, respGamingSettings.jurisdiction);
        }

        return settingsImpl.toInfo();
    }

    private validateDeletePendingData(currentSettings: any,
                                      settingsUpdate: ResponsibleGamingSettingsCreate): Promise<void> {
        if (!currentSettings ||
            (settingsUpdate.lossLimit !== undefined && currentSettings.lossLimitPending === undefined) ||
            (settingsUpdate.depositLimit !== undefined && currentSettings.depositLimitPending === undefined) ||
            !(settingsUpdate.lossLimit !== undefined || settingsUpdate.depositLimit !== undefined)) {
            return Promise.reject(new Errors.ValidationError("No pending operation for provided field"));
        }
    }

    public async updatePlayerResponsibleGamingSettings(playerCode, settings: PlayerResponsibleGamingUpdateSettings):
        Promise<PlayerResponsibleGaming> {

        await this.validateSettings(settings);

        const entityRespGamingSettings = await this.getEntityResponsibleGamingSettings(this.entity);
        await this.checkEntityHasResponsibleGamingEnabled(entityRespGamingSettings);

        const playerRespGamingImpl = await PlayerRespGamingCache.findOne(playerCode,
            this.entity.id, entityRespGamingSettings.jurisdiction);

        // no settings were created before
        if (!playerRespGamingImpl) {
            const newGamingItem = await this.createSettingsInDB(playerCode, settings, entityRespGamingSettings);
            await this.handlePlayerStatusAndSession(playerCode, settings);
            return newGamingItem.toInfo();
        } else {
            await this.validatePlayerSettingsBeforeUpdate(playerRespGamingImpl, settings);
            const types = Object.keys(settings); // casino and sport_bet
            await db.transaction(async(transaction: Transaction): Promise<any> => {
                for (const settingsType of types as ResponsibleGamingSettingsType[]) {
                    const respGamingSettingsImpl = playerRespGamingImpl.getSettingsByType(settingsType);
                    // case when settings of this type were not created yet
                    if (!respGamingSettingsImpl) {
                        const settingsDBItem = await Models.PlayerResponsibleGamingSettingsModel.create(
                            PlayerResponsibleGamingSettingsImpl.toNewRecordAttributes(playerRespGamingImpl.getId(),
                                settingsType, settings[settingsType]),
                            { transaction });

                        playerRespGamingImpl.addSettings(
                            new PlayerResponsibleGamingSettingsImpl(settingsDBItem)).toInfo();
                    } else {
                        const updateInfo = this.getUpdateUKSettingsObject(respGamingSettingsImpl.getSettings(),
                            settings[settingsType]);
                        const updateResult = await Models.PlayerResponsibleGamingSettingsModel.update(
                            { settings: updateInfo },
                            {
                                where: { id: playerRespGamingImpl.getId(), type: settingsType },
                                returning: true,
                                fields: ["settings"],
                                transaction
                            });

                        if (updateResult[0] === 1) {
                            respGamingSettingsImpl.setSettings((updateResult[1][0]).settings);
                            PlayerRespGamingCache.reset(
                                playerCode, this.entity.id, entityRespGamingSettings.jurisdiction);
                        }
                    }
                }
            });

            await this.increaseSelfExclusion(playerCode, settings);

            await this.handlePlayerStatusAndSession(playerCode, settings);
            return playerRespGamingImpl.toInfo();
        }
    }

    private async createSettingsInDB(playerCode, settings: PlayerResponsibleGamingUpdateSettings,
                                     entityResponsibleGamingSettings: EntityResponsibleGamingSettings):
        Promise<PlayerResponsibleGamingImpl> {
        let newRespGamingItem: PlayerResponsibleGamingImpl;
        await db.transaction(async(transaction: Transaction): Promise<any> => {
            const respGamingDBAttributes = PlayerResponsibleGamingImpl.toNewRecordAttributes(
                playerCode, this.entity, entityResponsibleGamingSettings);
            const dbItem = await Models.PlayerResponsibleGamingModel.create(
                respGamingDBAttributes, { transaction: transaction });

            const settingsRecords = [];
            const types = Object.keys(settings); // casino and sport_bet
            for (const settingsType of types as ResponsibleGamingSettingsType[]) {
                settingsRecords.push(PlayerResponsibleGamingSettingsImpl.toNewRecordAttributes(
                    dbItem.id, settingsType, settings[settingsType]));
            }

            const settingsItems = await Models.PlayerResponsibleGamingSettingsModel.bulkCreate(
                settingsRecords, { transaction: transaction });

            newRespGamingItem = new PlayerResponsibleGamingImpl(dbItem);
            newRespGamingItem.setSettings(settingsItems.map(item => new PlayerResponsibleGamingSettingsImpl(item)));
        });

        await this.increaseSelfExclusion(playerCode, settings);
        return newRespGamingItem;
    }

// kills session after setting timeout or self exclusion and sets player status to suspended for self exclusion
    private async handlePlayerStatusAndSession(playerCode,
                                               settings: PlayerResponsibleGamingUpdateSettings): Promise<void> {
        const types = Object.keys(settings); // casino and sport_bet
        for (const settingsType of types as ResponsibleGamingSettingsType[]) {
            if (settings[settingsType].casinoTimeoutTillDate || settings[settingsType].selfExclusionTillDate) {
                const reason = settings[settingsType].casinoTimeoutTillDate
                               ? "casinoTimeoutTillDate"
                               : "selfExclusionTillDate";
                await this.playerSessionService.kill({
                    brandId: this.entity.id,
                    playerCode,
                    reason: `Responsible gaming: ${settingsType}.${reason}`
                });
            }
            // additionally block player, so that after exclusion period expires,
            // he would need to ask support to enable him
            if (settings[settingsType].selfExclusionTillDate) {
                const blockService = getBlockPlayerService(this.entity as BrandEntity);
                await blockService.block(playerCode);
            }
        }
    }

    private async validatePlayerSettingsBeforeUpdate(playerSettings: PlayerResponsibleGamingImpl,
                                                     settings: PlayerResponsibleGamingUpdateSettings): Promise<void> {
        if (playerSettings.isSelfExcluded()) {
            return Promise.reject(
                new Errors.PlayerIsSelfExcludedError().setSelfExclusionDate(playerSettings.getSelfExclusionDate()));
        }
        const types = Object.keys(settings); // casino and sport_bet
        for (const settingsType of types as ResponsibleGamingSettingsType[]) {
            // don't allow to set smaller timeout time
            if (playerSettings.isOnTimeout(settingsType) &&
                settings[settingsType].casinoTimeoutTillDate &&
                playerSettings.getTimeoutDate(settingsType).getTime() >
                (new Date(settings[settingsType].casinoTimeoutTillDate).getTime())) {
                return Promise.reject(new Errors.ValidationError("Can't reduce timeout time"));
            }
        }
    }

    public async validatePlayerRestrictions(playerCode: string,
                                            settingsType: ResponsibleGamingSettingsType = "casino"):
        Promise<PlayerResponsibleGamingImpl> {
        const settings: EntitySettings = await getEntitySettings(this.entity.path);
        if (settings.responsibleGaming && settings.responsibleGaming.enabled) {
            const playerSettings = await PlayerRespGamingCache.findOne(
                playerCode,
                this.entity.id,
                settings.responsibleGaming.jurisdiction);
            if (playerSettings) {
                if (playerSettings.isOnTimeout(settingsType)) {
                    return Promise.reject(new Errors.PlayerOnTimeoutError(playerSettings.getTimeoutDate(settingsType)));
                } else if (playerSettings.isSelfExcluded()) {
                    return Promise.reject(
                        new Errors.PlayerIsSelfExcludedError().setSelfExclusionDate(
                            playerSettings.getSelfExclusionDate()));
                }
            }
            return playerSettings;
        }
        return undefined;
    }

    public async validatePlayerSelfExclusionRestriction(playerCode: string): Promise<PlayerResponsibleGamingImpl> {
        const settings: EntitySettings = await getEntitySettings(this.entity.path);
        if (settings.responsibleGaming && settings.responsibleGaming.enabled) {
            const playerSettings = await PlayerRespGamingCache.findOne(
                playerCode,
                this.entity.id,
                settings.responsibleGaming.jurisdiction);
            if (playerSettings && playerSettings.isSelfExcluded()) {
                return Promise.reject(
                    new Errors.PlayerIsSelfExcludedError().setSelfExclusionDate(playerSettings.getSelfExclusionDate()));
            }
            return playerSettings;
        }
        return undefined;
    }

    public async validatePlayerDepositRestriction(playerCode: string,
                                                  depositAmount: number): Promise<PlayerResponsibleGamingImpl> {
        const settings: EntitySettings = await getEntitySettings(this.entity.path);
        if (settings.responsibleGaming && settings.responsibleGaming.enabled) {
            const playerSettings = await PlayerRespGamingCache.findOne(
                playerCode,
                this.entity.id,
                settings.responsibleGaming.jurisdiction);

            if (playerSettings && playerSettings.isSelfExcluded()) {
                return Promise.reject(
                    new Errors.PlayerIsSelfExcludedError().setSelfExclusionDate(playerSettings.getSelfExclusionDate()));
            }

            if (playerSettings && playerSettings.getDepositLimit()) {
                const now = new Date();
                const daysFrame = this.TimeframeDaysMap.get(playerSettings.getDepositLimitTimeframe());
                const depositLimitDate = new Date(now.setDate(now.getDate() - daysFrame));
                const totalPlayerDeposits = (await Models.PaymentModel.aggregate("amount", "sum",
                    {
                        distinct: true,
                        where: {
                            playerCode, brandId: this.entity.id.toString(),
                            orderStatus: APPROVED,
                            endDate: { [Op.gte]: depositLimitDate },
                            [Op.or]: [
                                { orderType: TRANSFER_IN },
                                { orderType: PM_TYPE_DEPOSIT }
                            ]
                        }
                    })) as number || 0;

                if ((depositAmount + totalPlayerDeposits) > playerSettings.getDepositLimit()) {
                    return Promise.reject(new AdapterErrors.RGPlayerDepositLimitReachedError());
                }
            }
            return playerSettings;
        }
        return undefined;
    }

    public async validateSettings(settings: PlayerResponsibleGamingUpdateSettings): Promise<void> {
        const hasInvalidSettingsType: boolean = !!Object.keys(settings)
            .filter(receivedType => !SETTINGS_TYPE.includes(receivedType)).length;
        if (hasInvalidSettingsType) {
            return Promise.reject(new Errors.ValidationError("Invalid setting type"));
        }
        if (!settings.casino && !settings.sport_bet) {
            return Promise.reject(new Errors.ValidationError("No settings specified"));
        }
        if (settings.casino) {
            await this.validateSetting(settings.casino);
            this.enforceNumberValues(settings.casino);
        }
        if (settings.sport_bet) {
            await this.validateSetting(settings.sport_bet);
            this.enforceNumberValues(settings.sport_bet);
        }
    }

    public async validateSetting(data: ResponsibleGamingSettingsCreate): Promise<void> {
        if ("realityCheck" in data &&
            !(data.realityCheck === 20 || data.realityCheck === 40 || data.realityCheck === 60 ||
                data.realityCheck === 120 || data.realityCheck === 180 || data.realityCheck === null)) {
            return Promise.reject(new Errors.ValidationError("Invalid value for reality check"));
        } else if ("lossLimit" in data && data.lossLimit !== null
            && (isNaN(data.lossLimit) || data.lossLimit <= 0 || !data.lossLimitTimeframe)) {
            return Promise.reject(new Errors.ValidationError("Invalid value for loss limit"));
        } else if ("depositLimit" in data && data.depositLimit !== null
            && (isNaN(data.depositLimit) || data.depositLimit <= 0 || !data.depositLimitTimeframe)) {
            return Promise.reject(new Errors.ValidationError("Invalid value for deposit limit"));
        } else if ("casinoTimeoutTillDate" in data
            && (Date.parse(data.casinoTimeoutTillDate) < 0 || isNaN(Date.parse(data.casinoTimeoutTillDate))
                || +(new Date()) - Date.parse(data.casinoTimeoutTillDate) > 0)) {
            return Promise.reject(new Errors.ValidationError("Invalid value for casino timeout"));
        } else if ("selfExclusionTillDate" in data
            && (Date.parse(data.selfExclusionTillDate) < 0 || isNaN(Date.parse(data.selfExclusionTillDate))
                || +(new Date()) - Date.parse(data.selfExclusionTillDate) > 0)) {
            return Promise.reject(new Errors.ValidationError("Invalid value for self exclusion"));
        }
    }

    // ensures that number values are saved as numbers, for example, it will convert "200" to 200
    private enforceNumberValues(data: ResponsibleGamingSettingsCreate): void {
        if ("lossLimit" in data && data.lossLimit !== null) {
            data.lossLimit = +data.lossLimit;
        }
        if ("depositLimit" in data && data.depositLimit !== null) {
            data.depositLimit = +data.depositLimit;
        }
    }

    // builds an update data based on current settings values. function is intended for UK jurisdiction settings
    private getUpdateUKSettingsObject(currentSettings: UKResponsibleGamingSettings,
                                      newSettings: ResponsibleGamingSettingsCreate): UKResponsibleGamingSettings {
        const responsibleGamingSettings = cloneDeep(currentSettings);
        if (newSettings.lossLimit !== undefined) {
            newSettings.lossLimit = newSettings.lossLimit === null ? null : +newSettings.lossLimit;
            if (this.newSettingRequiresCoolingOff(responsibleGamingSettings.lossLimit,
                responsibleGamingSettings.lossLimitTimeframe,
                newSettings.lossLimit, newSettings.lossLimitTimeframe)) {
                responsibleGamingSettings.lossLimitPending = newSettings.lossLimit;
                responsibleGamingSettings.lossLimitPendingTimeframe = newSettings.lossLimitTimeframe;
                const date = new Date();
                date.setHours(date.getHours() + config.responsibleGaming.lossLimitCoolingOffPeriod);
                responsibleGamingSettings.lossLimitPendingDate = date;
            } else {
                responsibleGamingSettings.lossLimit = newSettings.lossLimit;
                responsibleGamingSettings.lossLimitTimeframe = newSettings.lossLimitTimeframe;
                delete responsibleGamingSettings.lossLimitPending;
                delete responsibleGamingSettings.lossLimitPendingTimeframe;
                delete responsibleGamingSettings.lossLimitPendingDate;
            }
        }
        if (newSettings.depositLimit !== undefined) {
            newSettings.depositLimit = newSettings.depositLimit === null ? null : +newSettings.depositLimit;
            if (this.newSettingRequiresCoolingOff(responsibleGamingSettings.depositLimit,
                responsibleGamingSettings.depositLimitTimeframe,
                newSettings.depositLimit, newSettings.depositLimitTimeframe)) {
                responsibleGamingSettings.depositLimitPending = newSettings.depositLimit;
                responsibleGamingSettings.depositLimitPendingTimeframe = newSettings.depositLimitTimeframe;
                const date = new Date();
                date.setHours(date.getHours() + config.responsibleGaming.depositLimitCoolingOffPeriod);
                responsibleGamingSettings.depositLimitPendingDate = date;
            } else {
                responsibleGamingSettings.depositLimit = newSettings.depositLimit;
                responsibleGamingSettings.depositLimitTimeframe = newSettings.depositLimitTimeframe;
                delete responsibleGamingSettings.depositLimitPending;
                delete responsibleGamingSettings.depositLimitPendingTimeframe;
                delete responsibleGamingSettings.depositLimitPendingDate;
            }
        }
        if (newSettings.casinoTimeoutTillDate) {
            responsibleGamingSettings.casinoTimeoutTillDate = new Date(newSettings.casinoTimeoutTillDate);
        }
        if (newSettings.selfExclusionTillDate) {
            responsibleGamingSettings.selfExclusionTillDate = new Date(newSettings.selfExclusionTillDate);
        }
        if (newSettings.realityCheck !== undefined) {
            responsibleGamingSettings.realityCheck = newSettings.realityCheck;
        }

        return responsibleGamingSettings;
    }

    // compare current settings value with new value. if new value is more restrictive - cool-off period is not required
    private newSettingRequiresCoolingOff(currentValue: number,
                                         currentTimeframe: string,
                                         newSettingValue: number,
                                         newSettingTimeframe: string): boolean {

        // no value has been set yet
        if (currentValue === null) {
            return false;
        }

        // setting no restriction
        if (newSettingValue === null) {
            return true;
        }

        // current and new values have the same timeframe
        if (currentTimeframe === newSettingTimeframe) {
            return newSettingValue > currentValue;
        }

        // current and new values have different timeframes - convert both to daily value and compare
        let currentValuePerDay = currentValue;
        if (currentTimeframe === "weekly") {
            currentValuePerDay = currentValue / 7;
        } else if (currentTimeframe === "monthly") {
            currentValuePerDay = currentValue / 30;
        }

        let newValuePerDay = newSettingValue;
        if (newSettingTimeframe === "weekly") {
            newValuePerDay = newValuePerDay / 7;
        } else if (newSettingTimeframe === "monthly") {
            newValuePerDay = newValuePerDay / 30;
        }

        // case when values are equal - need to compare timeframes to find which one is stricter
        if (currentValuePerDay === newValuePerDay) {
            return this.TimeframeStrictnessWeighs.get(currentTimeframe) >
                this.TimeframeStrictnessWeighs.get(newSettingTimeframe);
        }

        return newValuePerDay > currentValuePerDay;
    }

    private async increaseSelfExclusion(playerCode: string, settings: PlayerResponsibleGamingUpdateSettings) {
        const hasSelfExclusion = settings?.casino?.selfExclusionTillDate || settings?.sport_bet?.selfExclusionTillDate;
        if (!hasSelfExclusion) {
            return;
        }

        return PlayerModel.update({ selfExclusionCount: literal(`${SELF_EXCLUSION_COUNT_DB_FIELD} + 1`) }, {
            where: {
                brandId: this.entity.id,
                code: playerCode,
            },
        });
    }
}

export class PlayerResponsibleGamingImpl {
    private id: number;
    private playerCode: string;
    private brandId;
    private jurisdiction: string;
    private settings: PlayerResponsibleGamingSettingsImpl[];

    private createdAt: string;

    constructor(item: PlayerResponsibleGamingDBInstance) {
        this.id = item.id;
        this.playerCode = item.playerCode;
        this.brandId = item.brandId;
        this.jurisdiction = item.jurisdiction;
        this.createdAt = item.createdAt;

        const settingsDBitems = item["settings"];
        if (settingsDBitems && settingsDBitems.length > 0) {
            this.settings = settingsDBitems.map(
                (dbItem) => new PlayerResponsibleGamingSettingsImpl(dbItem)
            );
        } else {
            this.settings = [];
        }
    }

    public toInfo(): PlayerResponsibleGaming {
        return {
            playerCode: this.playerCode,
            brandId: this.brandId,
            jurisdiction: this.jurisdiction,
            settings: this.settingsToInfo(),
            createdAt: this.createdAt
        };
    }

    private settingsToInfo(): any {
        const result = {};
        if (this.settings && this.settings.length) {
            for (const item of this.settings) {
                result[item.getType()] = item.getSettings();
            }
        }
        return result;
    }

    public getId(): number {
        return this.id;
    }

    public isOnTimeout(type: ResponsibleGamingSettingsType = "casino"): boolean {
        const settingsItem = this.getSettingsByType(type);
        if (settingsItem) {
            return settingsItem.isOnTimeout();
        }
        return false;
    }

    public getTimeoutDate(type: ResponsibleGamingSettingsType = "casino"): Date {
        const settingsItem = this.getSettingsByType(type);
        if (settingsItem) {
            return settingsItem.getTimeoutDate();
        }
        return undefined;
    }

    public getSelfExclusionDate(): Date {
        if (this.settings.length) {
            for (const item of this.settings) {
                if (item.isSelfExcluded()) {
                    return item.getSelfExclusionDate();
                }
            }
        }
        return undefined;
    }

    public isSelfExcluded(): boolean {
        return this.settings.length && this.settings.some(item => item.isSelfExcluded());
    }

    public hasRealityCheck(type: ResponsibleGamingSettingsType = "casino"): boolean {
        const settingsItem = this.getSettingsByType(type);
        if (settingsItem) {
            return settingsItem.hasRealityCheck();
        }
        return false;
    }

    public getRealityCheck(type: ResponsibleGamingSettingsType = "casino"): number {
        const settingsItem = this.getSettingsByType(type);
        if (settingsItem) {
            return settingsItem.getRealityCheck();
        }
        return undefined;
    }

    public getDepositLimit(type: ResponsibleGamingSettingsType = "casino"): number {
        const settingsItem = this.getSettingsByType(type);
        if (settingsItem) {
            return settingsItem.getDepositLimit();
        }
        return undefined;
    }

    public getDepositLimitTimeframe(type: ResponsibleGamingSettingsType = "casino"): string {
        const settingsItem = this.getSettingsByType(type);
        if (settingsItem) {
            return settingsItem.getDepositLimitTimeframe();
        }
        return undefined;
    }

    public setSettings(settings: PlayerResponsibleGamingSettingsImpl[]): PlayerResponsibleGamingImpl {
        this.settings = settings;
        return this;
    }

    public addSettings(settings: PlayerResponsibleGamingSettingsImpl): PlayerResponsibleGamingImpl {
        this.settings.push(settings);
        return this;
    }

    public static toNewRecordAttributes(playerCode: string,
                                        brand: BaseEntity,
                                        respGamingSettings: EntityResponsibleGamingSettings): PlayerResponsibleGaming {
        const createPlayerSettings: PlayerResponsibleGaming = {};
        createPlayerSettings.playerCode = playerCode;
        createPlayerSettings.brandId = brand.id;
        createPlayerSettings.jurisdiction = respGamingSettings.jurisdiction;
        return createPlayerSettings;
    }

    public getSettingsByType(type: ResponsibleGamingSettingsType): PlayerResponsibleGamingSettingsImpl {
        if (this.settings.length) {
            for (const item of this.settings) {
                if (item.getType() === type) {
                    return item;
                }
            }
        }
        return undefined;
    }
}

export class PlayerResponsibleGamingSettingsImpl {
    private type: ResponsibleGamingSettingsType;
    private settings: any;

    private updatedAt: string;
    private createdAt: string;

    constructor(item: PlayerResponsibleGamingSettingsDBInstance) {
        this.settings = item.settings;
        this.type = item.type || (item as any).get("settings_type");
        this.updatedAt = item.updatedAt;
        this.createdAt = item.createdAt;
    }

    public toInfo(): PlayerResponsibleGamingSettings {
        return {
            type: this.type,
            settings: this.settings
        };
    }

    public getType(): ResponsibleGamingSettingsType {
        return this.type;
    }

    public isOnTimeout(): boolean {
        const timeoutDate = this.getTimeoutDate();
        if (timeoutDate && timeoutDate.getTime() > new Date().getTime()) {
            return true;
        }
        return false;
    }

    public getTimeoutDate(): Date {
        return this.settings && this.settings.casinoTimeoutTillDate ?
            new Date(this.settings.casinoTimeoutTillDate) : undefined;
    }

    public isSelfExcluded(): boolean {
        const exclDate = this.getSelfExclusionDate();
        if (exclDate && exclDate.getTime() > new Date().getTime()) {
            return true;
        }
        return false;
    }

    public getSelfExclusionDate(): Date {
        return this.settings && this.settings.selfExclusionTillDate ?
            new Date(this.settings.selfExclusionTillDate) : undefined;
    }

    public hasRealityCheck(): boolean {
        return this.settings && this.settings.realityCheck;
    }

    public getRealityCheck(): number {
        return this.settings ? this.settings.realityCheck : undefined;
    }

    public setSettings(settings: any): PlayerResponsibleGamingSettingsImpl {
        this.settings = settings;
        return this;
    }

    public getDepositLimit(): number {
        return this.settings ? this.settings.depositLimit : undefined;
    }

    public getDepositLimitTimeframe(): string {
        return this.settings ? this.settings.depositLimitTimeframe : undefined;
    }

    public getSettings(): any {
        return this.settings || {};
    }

    public static toNewRecordAttributes(parentId: number,
                                        type: ResponsibleGamingSettingsType,
                                        settings: ResponsibleGamingSettingsCreate): PlayerResponsibleGaming {
        const createSettings: PlayerResponsibleGamingSettings = {};
        createSettings.id = parentId;
        createSettings.type = type;
        createSettings.settings = settings;

        return createSettings;
    }
}
