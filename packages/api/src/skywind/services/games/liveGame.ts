import { GameFeatures, LiveRush, LiveTable } from "../../entities/game";
import { merge } from "lodash";

export function mergeLiveSettings(newSettings: GameFeatures, oldSettings: GameFeatures): any {
    const oldLiveSettings: LiveTable = oldSettings?.live;
    const newLiveSettings: LiveTable = newSettings?.live;
    if (!oldLiveSettings) {
        return newSettings?.live ? newSettings.live : newSettings;
    }

    if (!newLiveSettings) {
        return oldSettings?.live ? oldSettings.live : oldSettings;
    }

    const settings = { live: {} };
    if (newLiveSettings) {
        const mergedSettings = merge(oldLiveSettings, newLiveSettings);
        if (mergedSettings && Object.keys(mergedSettings).length) {
            (settings.live as LiveTable) = mergedSettings;
        }
    }
    return settings.live;
}

export function isLiveRush(liveFeature): liveFeature is LiveRush {
    return liveFeature.type && liveFeature.tables;
}
