import {
    ActionGameLimits,
    CurrencyCode,
    Limits,
    LimitsByCurrencyCode,
    LimitsCustomizations,
    LimitType,
    MultiRoomGameLimits,
    RoomGameLimits,
    SlotGameLimits,
    StakedLimits,
    TableGameLimits,
} from "../entities/gamegroup";
import * as Errors from "../errors";
import { BrandEntity } from "../entities/brand";
import { EntityGame, LimitFilters, LimitFiltersByCurrency } from "../entities/game";
import { GAME_TYPES, STAKED_GAMES_TYPES, validatePositiveNumber } from "../utils/common";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { logging } from "@skywind-group/sw-utils";
import { compareLimits, getGameGroupLimitService, getGameGroupService } from "./gamegroup";
import { EntitySettings } from "../entities/settings";
import { BaseEntity } from "../entities/entity";
import { getSchemaDefinitionService } from "./gameLimits/schemaDefinition";
import { getConfigurationFacade } from "./gameLimits/defaultConfigurationFacade";
import { GameLimitsConfigurationInStorage } from "../models/gameLimitsConfiguration";
import { findGameLimitsConfiguration } from "./gameLimits/gameLimitsStorage";
import { getGameLimitLevelService } from "./gameLimits/entityLimitLevels";
import { getNewLimitsFacade } from "./gameLimits/limitsFacade";
import { getEntitySettings } from "./settings";

const log = logging.logger("game-categories");

export interface IsLimitsResponse {
    isValid: boolean;
    invalidField?: string;
}

export async function validateLimits(type: string, limits: LimitsByCurrencyCode): Promise<LimitsByCurrencyCode> {
    if ((!limits || Object.keys(limits).length === 0) && type === GAME_TYPES.external) {
        return;
    }

    let limitsFound: boolean = false;
    if (!limits || !(Object.getOwnPropertyNames(limits).length > 0)) {
        return Promise.reject(new Errors.CurrencyNotFoundError());
    }
    for (const currencyCode of Object.keys(limits)) {
        if (!Currencies.exists(currencyCode)) {
            return Promise.reject(new Errors.CurrencyNotFoundError(currencyCode));
        } else {
            const result: IsLimitsResponse = isLimits(type, limits[currencyCode]);
            if (result.isValid) {
                limitsFound = true;
            } else {
                return Promise.reject(new Errors.LimitsIncorrect(type, currencyCode, result.invalidField));
            }
        }
    }

    if (limitsFound) {
        return limits;
    } else {
        return Promise.reject(new Errors.CurrencyNotFoundError());
    }
}

/**
 * Find player limits for game
 * 1. Find game group limits
 * 2. otherwise get game limits
 *
 * @param brand brand
 * @param gameGroupName
 * @param entityGame game data
 * @param currency currency code
 * @param settings entity settings
 * @param skipJurisdictionFiltering optional flag to skip jurisdiction filtering
 * @param dynamicMaxTotalBetLimit optional param defining operator dynamic max total bet limit
 * @returns  Player limits for game
 */
export async function findPlayerLimits(brand: BrandEntity,
                                       entityGame: EntityGame,
                                       currency: CurrencyCode,
                                       gameGroupName?: string,
                                       settings?: EntitySettings,
                                       skipJurisdictionFiltering?: boolean,
                                       dynamicMaxTotalBetLimit?: number): Promise<[Limits, LimitsCustomizations]> {

    const [limits, limitCustomizations] = await getGameGroupLimitService().findForPlayer(
        brand,
        gameGroupName,
        entityGame,
        currency,
        settings,
        skipJurisdictionFiltering,
        dynamicMaxTotalBetLimit
    );

    if (!limits || !Object.keys(limits).length) {
        return Promise.reject(new Errors.LimitsForCurrencyNotFound(currency));
    }

    const limitFilter = entityGame?.limitFilters?.[currency];
    if (!limitFilter) {
        return [limits, limitCustomizations];
    }

    if (isStakedLimits(limits)) {
        const filteredLimits = filterLimits(entityGame.game.type, limitFilter, limits);
        compareLimits(limits, filteredLimits, limitCustomizations, LimitType.ENTITY_GAME_FILTERS);
        return [filteredLimits, limitCustomizations];
    } else {
        const multiRoomGameLimits: Partial<MultiRoomGameLimits> = {};
        for (const key of Object.keys(limits)) {
            multiRoomGameLimits[key] = filterLimits(entityGame.game.type, limitFilter, limits[key]) as RoomGameLimits;
        }
        compareLimits(limits, multiRoomGameLimits, limitCustomizations, LimitType.ENTITY_GAME_FILTERS);
        return [multiRoomGameLimits, limitCustomizations];
    }
}

export async function findGameLimitsByCurrencyCode(entity: BaseEntity,
                                                   entityGame: EntityGame,
                                                   gameGroupName: string,
                                                   gameGroupId: number,
                                                   currencies: string[],
                                                   settings?: EntitySettings): Promise<LimitsByCurrencyCode> {
    const limits: LimitsByCurrencyCode = {};
    const gameCode = entityGame?.game?.code;
    try {
        const newLimitsDisabled = entityGame?.settings?.newLimitsDisabled;
        if (settings && settings.newLimitsEnabled && !newLimitsDisabled) {
            const schemaId = entityGame?.game?.schemaDefinitionId;
            if (!schemaId) {
                log.warn(`Failed to find limits for game ${gameCode}. Schema definition for game is missing`);
                return limits;
            }

            const schemaDefinition = await getSchemaDefinitionService().retrieve(schemaId);
            const facade = await getConfigurationFacade(schemaDefinition, gameCode);

            const gameLimitsConfiguration: GameLimitsConfigurationInStorage = await findGameLimitsConfiguration(
                entity,
                gameCode,
                schemaDefinition.id,
                gameGroupId);

            const levelCustomizations = await getGameLimitLevelService(entity).getGameLimitLevels({ gameCode });

            for (const currency of currencies) {
                limits[currency] = await getNewLimitsFacade(entity).build(
                    facade,
                    entityGame.game.totalBetMultiplier,
                    currency,
                    gameLimitsConfiguration,
                    false,
                    undefined,
                    levelCustomizations
                );
            }
        } else {
            for (const currency of currencies) {
                [limits[currency]] = await findPlayerLimits(
                    entity as BrandEntity,
                    entityGame,
                    currency,
                    gameGroupName,
                    settings
                );
            }
        }
    } catch (err) {
        // Suppress all limits related errors for lobby
        log.warn(err, `Failed to find limits for game ${gameCode}`);
    }
    return limits;
}

export async function findGameLimits(entity: BaseEntity,
                                     entityGames: EntityGame[],
                                     currency?: string,
                                     gameGroupName?: string,
                                     gameGroupId?: number): Promise<Record<string, LimitsByCurrencyCode>> {
    const settings = await getEntitySettings(entity.path);
    if (!gameGroupId && gameGroupName) {
        const gameGroup = await getGameGroupService().findOne(entity,
            { name: gameGroupName },
            false,
            settings.gameGroupsInheritance);
        if (gameGroup) {
            gameGroupId = gameGroup.toJSON().id;
        }
    }
    const currencies = currency ? [currency] : entity.getCurrencies();
    const gameLimits: Record<string, {}> = {};
    for (const entityGame of entityGames) {
        gameLimits[entityGame.id] = await findGameLimitsByCurrencyCode(
            entity,
            entityGame,
            gameGroupName,
            gameGroupId,
            currencies,
            settings
        );
    }
    return gameLimits;
}

export function filterLimits(type: string, filter: LimitFilters, limits: Limits): Limits {
    const stakedLimits = limits as StakedLimits;

    if (!STAKED_GAMES_TYPES.includes(type) || !filter ||
        !(stakedLimits && Array.isArray(stakedLimits.stakeAll) && stakedLimits.stakeAll.length)) {

        return limits;
    }

    let stakeAll = [...stakedLimits.stakeAll];
    if ("stakeMax" in filter) {
        stakeAll = stakeAll.filter(stake => stake <= filter.stakeMax);
    }

    if ("stakeMin" in filter) {
        stakeAll = stakeAll.filter(stake => stake >= filter.stakeMin);
    }

    let stakeDef = stakedLimits.stakeDef;
    if (!stakeAll.includes(stakeDef)) {
        stakeDef = Math.min(...stakeAll);
    }

    if ("winMax" in limits && "winMax" in filter) {
        (limits as SlotGameLimits).winMax = filter.winMax;
    }
    return {
        ...limits,
        stakeMin: Math.min(...stakeAll),
        stakeMax: Math.max(...stakeAll),
        stakeDef,
        stakeAll
    };
}

export function filterGameLimits(type: string,
                                 limitFilterByCurrency: LimitFiltersByCurrency,
                                 limitsByCurrency: LimitsByCurrencyCode): LimitsByCurrencyCode {
    if (!(STAKED_GAMES_TYPES.includes(type) && limitFilterByCurrency)) {
        return limitsByCurrency;
    }
    const currencies = Object.keys(limitsByCurrency);

    return currencies.reduce((newLimits, currency) => {
        const filter = limitFilterByCurrency && limitFilterByCurrency[currency];
        const limits = limitsByCurrency && limitsByCurrency[currency];

        if (!limits) {
            log.warn(`Empty limits for currency ${currency}`);
            return newLimits;
        }

        if (isStakedLimits(limits)) {
            newLimits[currency] = filterLimits(type, filter, limits);
        } else {
            const multiRoomGameLimits: Partial<MultiRoomGameLimits> = {};

            for (const key of Object.keys(limits)) {
                multiRoomGameLimits[key] = filterLimits(type, filter, limits[key]) as RoomGameLimits;
            }
            newLimits[currency] = multiRoomGameLimits;
        }

        return newLimits;
    }, {});
}

/**
 * Check is input object can be a Limits object
 * with all required fields
 * @param type game type
 * @param limit
 * @returns {boolean}
 */
export function isLimits(type: string, limit: Limits): IsLimitsResponse {
    switch (type) {
        case GAME_TYPES.slot:
        case GAME_TYPES.external:
            return isSlotGameLimits(limit as SlotGameLimits);
        case GAME_TYPES.action:
            return isActionGameLimits(limit as ActionGameLimits);
        case GAME_TYPES.table:
        case GAME_TYPES.live:
            return isTableGameLimits(limit as TableGameLimits);
        default:
            return { isValid: false };
    }
}

function isSlotGameLimits(limits: SlotGameLimits): IsLimitsResponse {
    const properties = [
        "stakeDef",
        "stakeMax",
        "stakeMin",
        "maxTotalStake"
    ];

    const stakeAll = limits.stakeAll;

    if (stakeAll) {
        if (!Array.isArray(stakeAll) || !stakeAll.length) {
            return { isValid: false, invalidField: "stakeAll" };
        } else {
            const hasInvalidValue = stakeAll.some((value) => !Number.isFinite(value) || value < 0);
            const hasDuplicates = new Set(stakeAll).size !== stakeAll.length;
            if (hasInvalidValue || hasDuplicates) {
                return { isValid: false, invalidField: "stakeAll" };
            }
        }
    }

    for (const property of properties) {
        const value = limits[property];
        if (value) {
            if (!Number.isFinite(value) || value < 0) {
                return { isValid: false, invalidField: property };
            } else if (properties.slice(0, 3)
                    .indexOf(property) !== -1 && Array.isArray(stakeAll) && stakeAll.length &&
                stakeAll.indexOf(value) === -1) {
                return { isValid: false, invalidField: property };
            }
        }
    }

    if (limits.winMax !== undefined) {
        const isValid: boolean = Number.isFinite(limits.winMax) && limits.winMax > 0;
        return { isValid, invalidField: "winMax" };
    }

    if (limits.stakeMin && Array.isArray(limits.stakeAll) && limits.stakeAll.length &&
        limits.stakeMin !== limits.stakeAll[0]) {
        return { isValid: false, invalidField: "stakeMin" };
    }

    if (limits.stakeMax && Array.isArray(limits.stakeAll) && limits.stakeAll.length &&
        limits.stakeMax !== limits.stakeAll[limits.stakeAll.length - 1]) {
        return { isValid: false, invalidField: "stakeMax" };
    }

    return { isValid: true };
}

function isActionGameLimits(limits: ActionGameLimits): IsLimitsResponse {
    const properties = [
        "coinsRate",
    ];

    for (const property of properties) {
        const value = limits[property];

        // SWS-3524 Now we have arcade game without coin rate
        if (value === undefined) {
            continue;
        }

        if (!Number.isFinite(value) || value < 0) {
            return { isValid: false, invalidField: property };
        }
    }

    return { isValid: true };
}

function isTableGameLimits(limits: TableGameLimits): IsLimitsResponse {
    if (!limits) {
        return { isValid: false };
    }

    if ((limits as RoomGameLimits).stakeMin && (limits as RoomGameLimits).stakeMax) {
        if (!isRoomGameLimits(limits as RoomGameLimits)) {
            return { isValid: false };
        }
    } else {
        for (const key in limits as MultiRoomGameLimits) {
            if (!isRoomGameLimits(limits[key])) {
                return { isValid: false, invalidField: key };
            }
        }
    }

    return { isValid: true };
}

function isRoomGameLimits(limits: RoomGameLimits): boolean {
    const properties = [
        "stakeDef",
        "stakeMin",
        "stakeMax",
    ];

    if (!limits) {
        return false;
    }

    const chipsList = limits.stakeAll;

    if (!Array.isArray(chipsList) || !chipsList.length) {
        return false;
    } else {
        const hasInvalidValue = chipsList.some((value) => !Number.isFinite(value) || value < 0);
        const hasDuplicates = new Set(chipsList).size !== chipsList.length;
        if (hasInvalidValue || hasDuplicates) {
            return false;
        }
    }

    for (const property of properties) {
        const value = limits[property];

        if (!Number.isFinite(value) || value < 0) {
            return false;
        }
    }
    return validateOptionalRoomGameLimits(limits);
}

function validateOptionalRoomGameLimits(limits: RoomGameLimits): boolean {
    const optionalProperties: string[] = ["totalStakeMin", "totalStakeMax", "order"];
    for (const property of optionalProperties) {
        if (!limits[property]) {
            continue;
        }
        if (!Number.isFinite(limits[property]) || limits[property] < 0) {
            return false;
        }
    }
    if (limits.isDefaultRoom !== undefined && typeof limits.isDefaultRoom !== "boolean") {
        return false;
    }

    if (!limits.bets) {
        return true;
    }

    for (const betKey in limits.bets) {
        if (!validatePositiveNumber(limits.bets[betKey].min) || !validatePositiveNumber(limits.bets[betKey].max)) {
            return false;
        }
    }

    return true;
}

export function validateLimitFilters(type: string,
                                     limitFilters: LimitFiltersByCurrency,
                                     limits: LimitsByCurrencyCode): boolean {
    if (!limitFilters) {
        return true;
    }

    if (!STAKED_GAMES_TYPES.includes(type)) {
        throw new Errors.ValidationError(`Only stacked games support limit filters. "${type}" type isn't supported`);
    }

    if (typeof limitFilters !== "object") {
        throw new Errors.ValidationError("limitFilters should be an object");
    }

    for (const currencyCode of Object.keys(limitFilters)) {
        if (!Currencies.exists(currencyCode)) {
            throw new Errors.CurrencyNotFoundError(currencyCode);
        }

        const limit = limits[currencyCode] as SlotGameLimits;
        if (!limit) {
            throw new Errors.ValidationError(`Game Limit not found by currency - ${currencyCode}`);
        }
        const limitFilter = limitFilters[currencyCode];

        if (isStakedLimits(limit)) {
            validateSlotLimitFilters(limitFilter, limit, currencyCode);
        } else {
            for (const key of Object.keys(limit)) {
                validateSlotLimitFilters(limitFilter, limit[key], currencyCode);
            }
        }

    }

    return true;
}

const isStakedLimits = (limit: Limits): boolean => {
    const stakedLimits = limit as StakedLimits;
    // hard method to find out that limits belongs to StakedLimits
    return !!(stakedLimits.stakeMin && stakedLimits.stakeMax);
};

function validateSlotLimitFilters(limitFilter: LimitFilters, limit: SlotGameLimits, currencyCode: string): boolean {
    if (!Array.isArray(limit.stakeAll) || !limit.stakeAll.length) {
        throw new Errors.ValidationError(
            `Old limit filter couldn't be applied - stakeAll is absent for currency ${currencyCode}`);
    }

    let stakeAll = [...limit.stakeAll];
    if ("stakeMax" in limitFilter) {
        if (!validatePositiveNumber(limitFilter.stakeMax)) {
            throw new Errors.ValidationError("limitFilters.stakeMax should be a positive number");
        }

        stakeAll = stakeAll.filter(stake => stake <= limitFilter.stakeMax);
    }

    if ("stakeMin" in limitFilter) {
        if (!validatePositiveNumber(limitFilter.stakeMin)) {
            throw new Errors.ValidationError("limitFilters.stakeMin should be a positive number");
        }

        stakeAll = stakeAll.filter(stake => stake >= limitFilter.stakeMin);
    }

    if (!stakeAll.length) {
        throw new Errors.ValidationError(`StakeAll empty after filter for currency - ${currencyCode}`);
    }

    if (limitFilter.stakeMax && limitFilter.stakeMin && limitFilter.stakeMin >= limitFilter.stakeMax) {
        throw new Errors.ValidationError("Invalid limitFilter (stakeMin >= stakeMax)");
    }

    if ("winMax" in limitFilter) {
        if (!validatePositiveNumber(limitFilter.winMax)) {
            throw new Errors.ValidationError("limitFilters.winMax should be a positive number");
        }
    }

    return true;
}

export const sanitizeCurrencyLimits = (type: string,
                                       limits: Limits,
                                       isGameGroup: boolean,
                                       overrideDefault: boolean = false) => {
    if ("winMax" in limits && isGameGroup && !overrideDefault) {
        return {
            ...(limits as object),
            winMax: undefined
        };
    }
    return limits;
};
