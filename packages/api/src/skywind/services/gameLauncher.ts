import { token } from "@skywind-group/sw-utils";
import { getMerchantSearchService, MerchantImpl } from "./merchant";
import { EntityGame } from "../entities/game";
import { BrandEntity } from "../entities/brand";
import { WEBSITE_WHITELISTED_CHECK_LEVEL } from "../entities/entity";
import EntityCache from "../cache/entity";

import { GameLauncherTokenData, verifyGameLauncherToken } from "../utils/token";
import logger from "../utils/logger";
import * as Errors from "../errors";
import { getEntityGame } from "./entityGameService";
import { EntitySettings } from "../entities/settings";
import { getEntitySettings } from "./settings";
import { URL } from "url";
import { MerchantGameInitRequest } from "@skywind-group/sw-wallet-adapter-core";
import { getAvailableSiteService } from "./availableSites";
import { getGameURLInfo } from "./gameUrl/getGameURLInfo";

const log = logger("game-launcher");

export interface GameLauncherRequest {
    launcherToken: string;
    webSiteDomain: string;
}

export interface GameUrl {
    url: string;
}

export async function launch(request: GameLauncherRequest): Promise<GameUrl> {
    log.info({ request }, "Game launcher request");

    try {
        const gameLauncherTokenData: GameLauncherTokenData = await getGameLaunchTokenData(request.launcherToken);

        const brand: BrandEntity = await EntityCache.findOne<BrandEntity>({ id: gameLauncherTokenData.brandId });

        if (!brand) {
            return Promise.reject(new Errors.EntityCouldNotBeFound());
        }

        const entitySettings: EntitySettings = await getEntitySettings(brand.path);

        const referrer: string = await checkWebSiteWhitelistedAndExtractDomain(brand,
            request.webSiteDomain,
            gameLauncherTokenData,
            entitySettings);
        const { request: gameUrlRequest, player, isLobby, gameCode } = gameLauncherTokenData;

        const throwErrorForHidden = true;
        const entityGame: EntityGame = await getEntityGame(brand, gameCode, throwErrorForHidden);

        let merchant: MerchantImpl;
        if ((gameUrlRequest as MerchantGameInitRequest)?.merchantCode) {
            const { merchantType, merchantCode } = gameLauncherTokenData.request as MerchantGameInitRequest;
            merchant = await getMerchantSearchService().findOneByTypeAndCode(merchantType, merchantCode);
        }
        gameUrlRequest.referrer = referrer;

        return await getGameURLInfo({
            entityGame,
            brand,
            entitySettings,
            disableLauncher: true,
            merchant,
            player,
            isLobby,
            request: gameUrlRequest
        });
    } catch (error) {
        return Promise.reject(error);
    }
}

export async function getGameLaunchTokenData(launchGameToken: string) {
    try {
        return await verifyGameLauncherToken(launchGameToken);

    } catch (error) {
        if (error instanceof token.TokenVerifyException) {
            throw new Errors.GameLauncherTokenError();
        }

        if (error instanceof token.TokenExpiredException) {
            throw new Errors.GameLauncherTokenExpired();
        }
        throw error;
    }
}

export async function checkWebSiteWhitelistedAndExtractDomain(brand: BrandEntity,
                                                              referer: string,
                                                              gameLauncherTokenData: GameLauncherTokenData,
                                                              entitySettings?: EntitySettings) {

    let correctedReferer: string;
    try {
        if (!referer) {
            throw new Errors.ReferrerMissingError();
        }

        correctedReferer = correctReferer(referer);
        if (!await getAvailableSiteService(brand).checkSiteIsAuthorized(correctedReferer)) {
            throw new Errors.UnauthorizedSite(correctedReferer);
        }

    } catch (error) {
        const logData = {
            launcherData: gameLauncherTokenData,
            referer: correctedReferer,
            errorCode: error.code,
            errorMessage: error.message
        };
        log.warn(logData, "Website is not whitelisted for type: " + brand.inheritedWebSiteWhitelistedCheck);

        const entitySettingValueToBlock = typeof entitySettings?.blockGameLaunchForEmptyReferrer === "boolean"
            ? entitySettings.blockGameLaunchForEmptyReferrer
            : true;

        const isBlocked = brand.inheritedWebSiteWhitelistedCheck === WEBSITE_WHITELISTED_CHECK_LEVEL.ERROR
            && (error instanceof Errors.ReferrerMissingError ? entitySettingValueToBlock : true);

        if (isBlocked) {
            throw error;
        }
    }
    return correctedReferer;
}

export function correctReferer(referer: string): string {
    return new URL(referer).hostname;
}
