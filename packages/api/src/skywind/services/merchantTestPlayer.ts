import { literal, WhereOptions } from "sequelize";
import * as Errors from "../errors";
import { PagingHelper } from "../utils/paginghelper";
import * as FilterService from "./filter";
import { EntitySettings, MAX_TEST_PLAYERS_DEFAULT } from "../entities/settings";
import { getEntitySettings } from "./settings";
import { BaseEntity } from "../entities/entity";
import { Models } from "../models/models";
import { CreateTestPlayer, MerchantTestPlayer, SOURCE, UpdateData } from "../entities/merchantTestPlayer";

const MerchantTestPlayerModel = Models.MerchantTestPlayerModel;
const sortableKeys = ["code", "status"];
const DEFAULT_SORT_KEY = "code";

export const queryParamsKeys = [
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
    "code"
];

export default function getMerchantTestPlayerService(brand: BaseEntity): MerchantTestPlayerService {
    return new MerchantTestPlayerServiceImpl(brand);
}

export interface MerchantTestPlayerService {
    add(data: CreateTestPlayer): Promise<MerchantTestPlayer>;

    count(): Promise<number>;

    findOne(code: string): Promise<MerchantTestPlayer>;

    remove(code: string): Promise<{ brandId: number, code: string }>;

    find(query: WhereOptions): Promise<MerchantTestPlayer[]>;

    update(code: string, data: UpdateData): Promise<MerchantTestPlayer>;
}

class MerchantTestPlayerServiceImpl implements MerchantTestPlayerService {
    constructor(public entity: BaseEntity) {
    }

    public async add(data: CreateTestPlayer): Promise<MerchantTestPlayer> {
        const exists = await MerchantTestPlayerModel.findOne({
            where: {
                brandId: this.entity.id,
                code: data.code
            }
        });
        if (exists) {
            return;
        }
        await this.checkNumberOfTestPlayers();
        this.validateAdd(data);
        await this.create(data.code, data.source, data.startDate, data.endDate);
        return this.findOne(data.code);
    }

    private validateAdd({ startDate, endDate }: CreateTestPlayer) {
        const isToday = (someDate: Date) => {
            const today = new Date();
            return someDate.getDate() === today.getDate() &&
                someDate.getMonth() === today.getMonth() &&
                someDate.getFullYear() === today.getFullYear();
        };
        if (startDate) {
            const currentDate = new Date();
            const validateStartDate = new Date(startDate);
            if (!isToday(validateStartDate)) {
                if (currentDate > validateStartDate) {
                    throw new Errors.ValidationError("current date > startDate");
                }
            }
        }

        if (startDate && endDate) {
            if (new Date(endDate) <= new Date(startDate)) {
                throw new Errors.ValidationError("end date <= start date");
            }
        }
    }

    private async checkNumberOfTestPlayers() {
        const numberOfTestPlayers = await this.count();
        const settings: EntitySettings = await getEntitySettings(this.entity.path);
        const maxAllowedTestPlayers = settings.maxTestPlayers !== undefined ?
                                      settings.maxTestPlayers : MAX_TEST_PLAYERS_DEFAULT;
        if (numberOfTestPlayers >= maxAllowedTestPlayers) {
            return Promise.reject(new Errors.NumberOfTestPlayersIsExceeded(maxAllowedTestPlayers));
        }
    }

    public async count(): Promise<number> {
        return MerchantTestPlayerModel.count({
            where: {
                brandId: this.entity.id
            }
        });
    }

    private async create(code: string,
                         source: SOURCE = SOURCE.INTEGRATION,
                         startDate: Date = new Date(),
                         endDate?: Date) {
        try {
            await MerchantTestPlayerModel.create({
                brandId: this.entity.id,
                code,
                source,
                startDate,
                endDate
            });
        } catch (err) {
            if (err.name === "SequelizeUniqueConstraintError") {
                return;
            }
            return Promise.reject(err);
        }
    }

    public async findOne(code: string): Promise<MerchantTestPlayer> {
        const instance = await MerchantTestPlayerModel.findOne({
            where: {
                brandId: this.entity.id,
                code
            }
        });
        if (instance) {
            return instance.toInfo();
        }
    }

    public async remove(code: string): Promise<{ brandId: number, code: string }> {
        const status = await MerchantTestPlayerModel.destroy({
            where: {
                brandId: this.entity.id,
                code
            }
        });

        await this.raiseForStatus(status);

        return {
            brandId: this.entity.id,
            code
        };
    }

    private async raiseForStatus(status: number) {
        if (status === 0) {
            return Promise.reject(new Errors.PlayerNotFoundError());
        }
    }

    public async find(query: WhereOptions = {}): Promise<MerchantTestPlayer[]> {
        query["brandId"] = this.entity.id;

        const sortBy = FilterService.getSortKey(query, sortableKeys, DEFAULT_SORT_KEY);
        const sortOrder = FilterService.valueFromQuery(query, "sortOrder") || "ASC";

        return PagingHelper.findAndCountAll(MerchantTestPlayerModel,
            {
                where: query,
                offset: FilterService.valueFromQuery(query, "offset"),
                limit: FilterService.valueFromQuery(query, "limit"),
                order: literal(`"${sortBy}" ${sortOrder}`),
            },
            player => ({
                code: player.code,
                brandId: this.entity.id,
                source: player.source,
                startDate: player.startDate,
                endDate: player.endDate
            }));
    }

    public async update(code: string, data: UpdateData): Promise<MerchantTestPlayer> {
        const item = await MerchantTestPlayerModel.findOne({
            where: {
                brandId: this.entity.id,
                code: code
            }
        });
        if (!item) {
            return Promise.reject(new Errors.PlayerNotFoundError());
        }
        if (data.endDate) {
            data.endDate = new Date(data.endDate);
            const startDate = item.get("startDate");
            if (startDate && startDate >= data.endDate) {
                throw new Errors.ValidationError("end date <= start date");
            }
        }
        const dbItem = await item.update(data);
        return dbItem.toInfo();
    }

}
