import { LobbySessionInfo, PlayerLobbySession } from "@skywind-group/sw-management-playersession";

export class AuditPlayerLobbySession implements PlayerLobbySession {
    constructor(private readonly original: PlayerLobbySession) {
    }

    public async create(brandId: number, playerCode: string, sessionInfo: LobbySessionInfo): Promise<void> {
        return this.original.create(brandId, playerCode, sessionInfo);
    }

    public async find(brandId: number, playerCode: string): Promise<any> {
        return this.original.find(brandId, playerCode);
    }

    public kill(brandId: number,
                playerCode: string,
                reason?: string): Promise<boolean> {
        return this.original.kill(brandId, playerCode, reason);
    }
}
