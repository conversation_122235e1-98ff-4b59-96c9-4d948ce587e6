import { createPlayerLoginInfo, LoginData, LoginInfo } from "../playerLogin";
import { BaseEntity } from "../../entities/entity";
import { EntitySettings } from "../../entities/settings";
import { getEntitySettings } from "../settings";
import { getChildIds } from "../entity";
import { PlayerModel } from "../../models/player";
import * as Errors from "../../errors";
import EntityCache from "../../cache/entity";
import * as SecurityService from "../security";
import { SECURITY_AUTH_TYPE, SECURITY_EVENT_TYPE } from "../security";
import PlayerResponsibleGamingServiceImpl from "../playerResponsibleGaming";
import { Player, PlayerStatus } from "../../entities/player";
import { PlayerImpl } from "../brandPlayer";
import { getBrandPlayerValidator } from "../brandPlayerValidator";
import { getGameGroupService } from "../gamegroup";
import logger from "../../utils/logger";
import { createPlayerSessionFacade } from "./playerSessionFacade";
import config from "../../config";
import { CountrySource, getIpCountrySource } from "../../utils/countrySource";
import { validatePlayerCountryRestrictions } from "../../utils/validateCountriesRestrictions";
import { sendPlayerBlockingAnalytics } from "../playerBlockingAnalyticsService";
import { getPlayerInfoService } from "../playerInfo";
import { Models } from "../../models/models";
import { Op } from "sequelize";

const plModel = Models.PlayerModel;

export interface PlayerLoginService {
    login(loginData: LoginData, ip?: string): Promise<LoginInfo>;
}

abstract class LoginImpl {
    protected log = logger();
    protected settings: EntitySettings;

    protected constructor(protected entity: BaseEntity, protected tokenConfig?: any) {
    }

    public async login(loginData: LoginData, ip?: string): Promise<LoginInfo> {
        const playerInstance = await this.findPlayerInstance(loginData.code);
        const player: Player = new PlayerImpl(playerInstance);

        this.settings = await getEntitySettings(this.entity.path);
        await this.validationRules(player, loginData, ip);

        await this.updatePlayer(player, playerInstance);

        return await createPlayerLoginInfo(player, { userId: loginData.userId }, this.tokenConfig);
    }

    protected abstract findPlayerInstance(code: string): Promise<PlayerModel>;

    protected async getPlayerInstance(entityIds: number[], code: string): Promise<PlayerModel> {
        const playerInstance = await plModel.findOne({
                where: {
                    brandId: { [Op.in]: entityIds },
                    code: code,
                },
                include: [
                    {
                        association: PlayerModel.associations.gamegroup,
                    }
                ]
            }
        );
        if (!playerInstance) {
            this.log.error("Player not found");
            return Promise.reject(new Errors.PlayerOrPasswordDoesntMatch());
        }
        return playerInstance;
    }

    protected async validationRules(player: Player, loginData: LoginData, ip?: string): Promise<void> {
        let countrySource: CountrySource;
        try {
            countrySource = await getIpCountrySource({
                entitySettings: this.settings,
                playerInfo: await getPlayerInfoService().getPlayerInfo(player.code, this.entity.id),
                ip
            });
            await validatePlayerCountryRestrictions(this.entity, this.settings, player.currency, countrySource);
        } catch (err) {
            await sendPlayerBlockingAnalytics(err, countrySource, {
                initiator: "player-login",
                playerCode: player.code,
                currencyCode: player.currency,
                ip,
                brandId: this.entity.id
            });
            return Promise.reject(err);
        }

        await SecurityService.checkBlockedAuthentication(this.entity.key, loginData.code, SECURITY_AUTH_TYPE.PLAYER);

        await new PlayerResponsibleGamingServiceImpl(this.entity)
            .validatePlayerSelfExclusionRestriction(loginData.code);

        const password: string = await SecurityService.encryptPassword(player.salt, loginData.password);

        const enableCaptcha = SecurityService.isCaptchaEnabled(SECURITY_AUTH_TYPE.PLAYER);
        if (password !== player.password) {
            await SecurityService.incrementFailures(this.entity.key, loginData.code, SECURITY_AUTH_TYPE.PLAYER);
            let outputCaptcha;
            if (enableCaptcha) {
                const attemptsFailedLogin = await SecurityService
                    .getFailedAttempts(this.entity.key, loginData.code);
                outputCaptcha = SecurityService.checkCaptcha(+attemptsFailedLogin) ?
                    await SecurityService.generateCaptcha(this.entity.key, loginData.code) : undefined;
            }
            if (!player.password) {
                this.log.error("Player created without password");
            } else {
                this.log.error("Password does not match");
            }
            return Promise.reject(new Errors.PlayerOrPasswordDoesntMatch(outputCaptcha));
        }

        if (enableCaptcha) {
            await SecurityService.checkAndRemoveCaptcha(
                this.entity.key,
                loginData.code,
                SECURITY_AUTH_TYPE.PLAYER,
                SECURITY_EVENT_TYPE.LOGIN,
                loginData.captchaToken,
                loginData.csrfToken
            );
        }

        await this.checkPlayerDeactivation(player);
        getBrandPlayerValidator().validatePlayerSuspended(player);

        await this.checkAnotherSession(loginData);
    }

    protected async checkPlayerDeactivation(player: Player) {
        if (player.deactivatedAt && new Date(player.deactivatedAt).getTime() < Date.now()) {
            player.status = PlayerStatus.SUSPENDED;
            player.deactivatedAt = null;
            await player.save();
        }
    }

    protected async updatePlayer(player: Player, playerInstance: PlayerModel) {
        player.lastLogin = new Date();
        await player.save();

        let gameGroupInstance;
        if (!playerInstance.get("gamegroup")) {
            gameGroupInstance = await getGameGroupService().findOneDefault(this.entity, this.settings);
            if (gameGroupInstance) {
                player.updateGameGroup(gameGroupInstance.toJSON());
            }
        }
    }

    protected async checkAnotherSession(loginData: LoginData) {
        if (config.playerLoginCheckAnotherSession &&
            "force" in loginData &&
            (typeof loginData.force !== "boolean" || loginData.force !== true)) {
            const sessionExists = await createPlayerSessionFacade().sessionExists({
                brandId: this.entity.id, playerCode: loginData.code
            });
            if (sessionExists) {
                return Promise.reject(new Errors.PlayerAlreadyHasSession());
            }
        }
    }
}

class PlayerLoginImpl extends LoginImpl implements PlayerLoginService {
    constructor(entity: BaseEntity, tokenConfig?: any) {
        super(entity, tokenConfig);
    }

    protected async findPlayerInstance(code: string): Promise<PlayerModel> {
        return await this.getPlayerInstance([this.entity.id], code);
    }
}

class TerminalPlayerLoginImpl extends LoginImpl implements PlayerLoginService {
    constructor(entity: BaseEntity, tokenConfig?: any) {
        super(entity, tokenConfig);
    }

    protected async findPlayerInstance(code: string): Promise<PlayerModel> {
        let entityIds: number[] = [this.entity.id];
        const entitySettings: EntitySettings = await getEntitySettings(this.entity.path);
        if (entitySettings && entitySettings.isPlayerCodeUniqueInSubtree) {
            entityIds = entityIds.concat(getChildIds(this.entity));
        }
        const playerInstance = await this.getPlayerInstance(entityIds, code);
        this.entity = await EntityCache.findById(playerInstance.get("brandId"));
        return playerInstance;
    }
}

export function getTerminalPlayerLoginService(entity: BaseEntity, tokenConfig?: any): PlayerLoginService {
    return new TerminalPlayerLoginImpl(entity, tokenConfig);
}

export function getPlayerLoginService(entity: BaseEntity, tokenConfig?: any): PlayerLoginService {
    return new PlayerLoginImpl(entity, tokenConfig);
}
