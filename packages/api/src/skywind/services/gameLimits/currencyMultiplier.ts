import {
    CurrencyMultiplier,
    CurrencyMultiplierData,
    CurrencyMultiplierDBInstance,
    CurrencyMultiplierModel,
    DEFAULT_CURRENCY,
} from "../../models/currencyMultiplier";
import { FindOptions, literal, UniqueConstraintError, WhereOptions } from "sequelize";
import * as Errors from "../../errors";
import { BaseEntity } from "../../entities/entity";
import { ResourceNotFoundError, ValidationError } from "../../errors";
import * as FilterService from "../filter";
import { Lazy, lazy } from "@skywind-group/sw-utils";
import { PagingHelper } from "../../utils/paginghelper";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import logger from "../../utils/logger";
import { Models } from "../../models/models";

const log = logger("currency-multipliers");

export const getCurrencyMultiplierService = (): CurrencyMultiplierService => {
    return new CurrencyMultiplierService();
};

class CurrencyMultiplierService {
    public static minMultiplier = 1;
    public static maxMultiplier = 100000;
    public static sortableKeys = ["createdAt"];
    public static defaultSortKey = "createdAt";
    public static defaultLimit = 20;
    public static defaultOffset = 0;
    public static multiplierForBaseCurrency = 1;

    constructor(private model = Models.CurrencyMultiplierModel) { }

    public getModel(): CurrencyMultiplierModel {
        return this.model;
    }

    public async createOrUpdate(entity: BaseEntity,
                                data: CurrencyMultiplierData): Promise<CurrencyMultiplier> {
        try {
            this.validateEntity(entity);

            const item = await this.getModel().findOne({ where: { entityId: entity.id }});
            if (item) {
                this.validateUpdateData(data);
                return await this.update(item, data);
            }

            this.validateCreateData(data);

            return await this.create(entity, data);
        } catch (err) {
            if (err instanceof UniqueConstraintError) {
                return Promise.reject(new Errors.CurrencyMultiplierAlreadyExists());
            }
            return Promise.reject(err);
        }

    }

    public async findOne(entity: BaseEntity,
                         options: WhereOptions<any> = {},
                         raiseError: boolean = false): Promise<CurrencyMultiplier> {

        options["entityId"] = entity.id;

        const findOptions: FindOptions<any> = {
            where: options
        };

        const item =  await this.getModel().findOne(findOptions);

        if (!item && raiseError) {
            return Promise.reject(new ResourceNotFoundError("Currency multiplier list not found"));
        }

        return item && item.toInfo();
    }

    public async list(options: WhereOptions<any> = {}, returnAll: boolean = false)
        : Promise<CurrencyMultiplier[]> {

        const sortBy = FilterService.getSortKey(options,
            CurrencyMultiplierService.sortableKeys, CurrencyMultiplierService.defaultSortKey);
        const sortOrder = FilterService.valueFromQuery(options, "sortOrder") || "ASC";
        const offset = FilterService.valueFromQuery(options, "offset") as number ||
            CurrencyMultiplierService.defaultOffset;
        const limit = FilterService.valueFromQuery(options, "limit") || CurrencyMultiplierService.defaultLimit;

        const findOptions: FindOptions<any> = {
            where: options,
            order: literal(`"${sortBy}" ${sortOrder}`),
            offset
        };

        if (!returnAll) {
            findOptions.limit = limit;
        }

        return await PagingHelper.findAndCountAll(
            this.getModel(),
            findOptions,
            (item: CurrencyMultiplierDBInstance) => item.toInfo()
        );
    }

    private async create(entity: BaseEntity,
                         data: CurrencyMultiplierData): Promise<CurrencyMultiplier> {
        const clearedData: CurrencyMultiplier = {
            entityId: entity.id,
            baseCurrency: data.baseCurrency,
            multipliers: data.multipliers,
            externalBrandId: data.externalBrandId
        };

        const createdItem = await this.getModel().create(clearedData);

        return createdItem.toInfo();
    }

    protected validateCreateData(data: CurrencyMultiplierData): void {
        this.validateBaseCurrency(data.baseCurrency);
        this.validateMultipliers(data);
    }

    private async update(instance: CurrencyMultiplierDBInstance,
                         data: Partial<CurrencyMultiplierData>): Promise<CurrencyMultiplier> {
        const clearedData: Partial<CurrencyMultiplier> = {};
        if (data.baseCurrency) {
            clearedData.baseCurrency = data.baseCurrency;
        }
        if (data.multipliers) {
            clearedData.multipliers = data.multipliers;
        }
        const updatedItem = await instance.update(clearedData);

        return updatedItem.toInfo();
    }

    private validateUpdateData(data: Partial<CurrencyMultiplierData>): void {
        if (data.baseCurrency) {
            this.validateBaseCurrency(data.baseCurrency);
        }
        if (data.multipliers) {
            this.validateMultipliers(data);
        }
    }

    private validateEntity(entity: BaseEntity) {
        if (entity.isSuspended()) {
            throw new Errors.ParentSuspendedError();
        }
    }

    private validateBaseCurrency(baseCurrency: string) {

        if (!baseCurrency) {
            throw new ValidationError("baseCurrency is required");
        }

        const currencyMultiplier = Currencies.get(baseCurrency).multiplier;
        const isNotCompatible =  currencyMultiplier !== this.baseCurrencyMultiplier.get();
        if (isNotCompatible) {
            log.warn(`Currency ${baseCurrency} can't be base currency: ` +
            `multiplier ${currencyMultiplier} is differ from ${DEFAULT_CURRENCY}`);
        }
    }

    private baseCurrencyMultiplier: Lazy<number> = lazy(() => {
        return Currencies.get(DEFAULT_CURRENCY).multiplier;
    });

    private validateMultipliers(data: Partial<CurrencyMultiplierData>): void {
        const errorMessage = "multipliers - must be an array with currencyCode and currencyMultiplier";
        if (!Array.isArray(data.multipliers)) {
            throw new ValidationError(errorMessage);
        }

        for (const currencyMultiplier of data.multipliers) {
            if (typeof currencyMultiplier !== "object" || !currencyMultiplier.currencyCode ||
                !currencyMultiplier.currencyMultiplier) {

                throw new ValidationError(errorMessage);
            }

            if (typeof currencyMultiplier.currencyMultiplier !== "number" ||
                currencyMultiplier.currencyMultiplier < CurrencyMultiplierService.minMultiplier ||
                currencyMultiplier.currencyMultiplier > CurrencyMultiplierService.maxMultiplier) {
                throw new ValidationError(
                    "Currency multiplier must be in range " +
                    `[${CurrencyMultiplierService.minMultiplier}, ${CurrencyMultiplierService.maxMultiplier}]`);
            }

            if (data.baseCurrency === currencyMultiplier.currencyCode &&
                currencyMultiplier.currencyMultiplier !== CurrencyMultiplierService.multiplierForBaseCurrency) {
                throw new ValidationError(
                    `Currency multiplier must be ${CurrencyMultiplierService.multiplierForBaseCurrency} ` +
                    `for base currency ${currencyMultiplier.currencyCode}`
                );
            }
        }

        const receivedCurrencyCodes = data.multipliers.map(({ currencyCode }) => currencyCode);
        if (receivedCurrencyCodes.length !== new Set([...receivedCurrencyCodes]).size) {
            throw new ValidationError("Multipliers include duplicate currencies");
        }
    }

}
