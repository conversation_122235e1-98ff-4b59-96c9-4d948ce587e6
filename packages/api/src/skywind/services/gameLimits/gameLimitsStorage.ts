import logger from "../../utils/logger";
import * as path from "path";
import { GameLimitsConfigurationInStorage, UpdateGameLimitsInStorage } from "../../models/gameLimitsConfiguration";
import * as redisClient from "../../storage/redis";
import { mergeDeep } from "../../utils/common";
import { redis } from "@skywind-group/sw-utils";
import { BaseEntity } from "../../entities/entity";
import RedisProc = redis.RedisProc;
import { LIMITS_CONFIGURATION_STATUS } from "./helper";

export class GameLimitsStorage {
    private static readonly loadGameLimits = new RedisProc(logger("limits"),
        path.resolve(__dirname, "../../../../resources/lua/loadGameLimitsConfigurations.lua"));
    private readonly globalGameLimitsKey: string = "sw-management-api:game-limits";
    private readonly globalActiveGameLimitsListKey = "sw-management-api:active-game-limits";
    private readonly key;
    private readonly defaultKeyValue = "-1";
    private readonly keysWithValues = ["gameLimits", "filters", "levels"];

    constructor(private entityPath: string,
                private definitionId: number,
                private gameCode?: string,
                private gameGroupId?: number,
                private segmentId?: number | string,
                private propertyToLoad?: string) {

        this.key = `${this.globalGameLimitsKey}${entityPath}${definitionId}:${gameCode || this.defaultKeyValue}:` +
            (gameGroupId || segmentId || this.defaultKeyValue);
    }

    public async retrieve(): Promise<GameLimitsConfigurationInStorage> {
        return redisClient.usingDb(async (db) => {
            const values = await db.hgetall(this.key);
            return this.parseValues(values);
        });
    }

    public async save(data: GameLimitsConfigurationInStorage): Promise<void> {
        return redisClient.usingDb(async (db) => {
            const status = data.status || LIMITS_CONFIGURATION_STATUS.ACTIVE;

            const toSave: UpdateGameLimitsInStorage = { processing: false };

            for (const key of this.keysWithValues) {
                if (key in data) {
                    toSave[key] = data[key] ? JSON.stringify(data[key]) : null;
                }
            }

            if (data.defaultLevel) {
                toSave.defaultLevel = data.defaultLevel ? data.defaultLevel : null;
            }

            if (Object.keys(toSave).length) {
                await db.multi()
                    .hmset(this.key, toSave)
                    .hsetnx(this.key, "status", status)
                    .sadd(this.globalActiveGameLimitsListKey, this.key)
                    .exec();
            }
        });
    }

    public async setProcessingFlag(processing: boolean): Promise<void> {
        return redisClient.usingDb(async (db) => {
            await db.hset(this.key, "processing", processing as any);
        });
    }

    public async setStatus(status: LIMITS_CONFIGURATION_STATUS, processing: boolean): Promise<void> {
        return redisClient.usingDb(async (db) => {
            if (status === LIMITS_CONFIGURATION_STATUS.ACTIVE) {
                await db.multi()
                    .sadd(this.globalActiveGameLimitsListKey, this.key)
                    .hmset(this.key, {
                        status: status,
                        processing: processing
                    })
                    .exec();
            } else {
                await db.multi()
                    .srem(this.globalActiveGameLimitsListKey, this.key)
                    .hmset(this.key, {
                        status: status,
                        processing: processing
                    })
                    .exec();
            }
        });
    }

    public async delete(): Promise<void> {
        await redisClient.usingDb(async (db) => {
            const multi = db.multi().del(this.key).srem(this.globalActiveGameLimitsListKey, this.key);
            try {
                return multi.exec();
            } catch (err) {
                await multi.discard();
                throw err;
            }
        });
    }

    public async search(): Promise<GameLimitsConfigurationInStorage[]> {
        return redisClient.usingDb<GameLimitsConfigurationInStorage[]>(async (db) => {
            const result = await GameLimitsStorage.loadGameLimits.exec<string>(db,
                [this.globalGameLimitsKey, this.globalActiveGameLimitsListKey],
                [
                    this.entityPath,
                    this.definitionId.toString(),
                    this.gameCode,
                    this.gameGroupId?.toString() || this.defaultKeyValue,
                    this.segmentId?.toString(),
                    this.propertyToLoad
                ]);

            if (result) {
                return this.parseSearchResult(result);
            }
        });
    }

    public merge(gameLimits: GameLimitsConfigurationInStorage[]): GameLimitsConfigurationInStorage {
        if (gameLimits) {
            const masterConfiguration = gameLimits[0];

            return mergeDeep({}, masterConfiguration, ...gameLimits);
        }
    }

    public async searchAndMerge(): Promise<GameLimitsConfigurationInStorage> {
        const matchedConfigurations = await this.search();
        return this.merge(matchedConfigurations);
    }

    private parseSearchResult(result: string): GameLimitsConfigurationInStorage[] {
        const parsedResult = JSON.parse(result);
        if (!Array.isArray(parsedResult)) {
            return;
        }
        // low priority returns first
        return parsedResult
            .filter(c => !this.isEmptyValues(c))
            .map(c => this.parseValues(c));
    }

    private isEmptyValues(values): boolean {
        return !values || this.keysWithValues.every(key => !values[key]);
    }

    private parseValues(values): GameLimitsConfigurationInStorage {
        const isEmpty = this.isEmptyValues(values);
        if (isEmpty) {
            return;
        }

        const result: GameLimitsConfigurationInStorage = {
            processing: values.processing ? JSON.parse(values.processing) : undefined,
            status: values.status ? values.status : undefined
        };

        for (const key of this.keysWithValues) {
            result[key] = values[key] ? JSON.parse(values[key]) : undefined;
        }
        if (values.defaultLevel) {
            result.defaultLevel = values.defaultLevel;
        }
        if (values.entityPath) {
            result.entityPath = values.entityPath;
        }

        return result;
    }
}

export async function findGameLimitsConfiguration(brand: BaseEntity,
                                                  gameCode: string,
                                                  definitionId: number,
                                                  gameGroupId?: number,
                                                  segmentId?: number): Promise<GameLimitsConfigurationInStorage> {
    const gameLimitsStorage: GameLimitsStorage = new GameLimitsStorage(brand.path,
        definitionId,
        gameCode,
        gameGroupId,
        segmentId);

    return gameLimitsStorage.searchAndMerge();
}
