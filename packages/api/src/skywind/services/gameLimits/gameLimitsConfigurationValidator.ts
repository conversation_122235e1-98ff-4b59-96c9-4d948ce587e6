import { SchemaValidation } from "./schemaDefinition";
import { BaseEntity, ChildEntity } from "../../entities/entity";
import {
    BetTypeRoomLimits,
    CreateGameLimitsConfiguration,
    GameLimits,
    GameLimitsByCurrencyCode, GameLimitsConfiguration,
    GameLimitsConfigurationDetailedInfo, GameLimitsConfigurationInStorage,
    GameLimitsFilters, MultiRoomGameLimits, RoomGameLimits,
    UpdateGameLimitsConfiguration
} from "../../models/gameLimitsConfiguration";
import * as Errors from "../../errors";
import {
    SchemaConfigurationDefaultValue
} from "../../models/schemaConfiguration";
import { EntitySettings } from "../../entities/settings";
import EntitySettingsService from "../settings";
import { DEFAULT_CURRENCY } from "../../models/currencyMultiplier";
import { validatePositiveNumber } from "../../utils/common";
import { BaseLimitsValidator, getCurrencyLimits } from "./helper";
import { ValidationError } from "../../errors";
import { getNewLimitsFacade } from "./limitsFacade";
import { findGameLimitsConfiguration, GameLimitsStorage } from "./gameLimitsStorage";
import { limitLevelService } from "./limitLevels";
import { LimitLevel } from "../../models/limitLevels";
import { DefaultConfigurationFacade } from "./defaultConfigurationFacade";
import { getSchemaConfigurationService } from "./schemaConfiguration";
import { GameLimitsPermissionType, Schema } from "../../entities/schemaDefinition";

export class GameLimitsConfigurationValidatorService {
    private readonly maxLevelLength: number = 64;
    private readonly levelService = limitLevelService.get();

    constructor(private keyEntity: BaseEntity, private isMaster = false) {
    }

    public validateCreation(data: CreateGameLimitsConfiguration) {
        this.validateUpdate(data);

        if (!data.schemaDefinitionId && !data.gameCode) {
            throw new Errors.ValidationError(
                "Schema definition or game code is required to create game limits configuration");
        }

        if (!data.gameLimits && !data.filters && !data.levels) {
            throw new Errors.ValidationError("Game limits must be not empty object");
        }

        if (data.segment && data.gameGroupName) {
            throw new Errors.ValidationError("Gamegroup with segment are not allowed");
        }
    }

    private validateObject(obj: object, objectName: string) {
        if (typeof obj !== "object" ||
            Array.isArray(obj) || (obj !== null && Object.keys(obj).length === 0)) {
            throw new Errors.ValidationError(`${objectName} must be not empty object`);
        }
    }

    public validateUpdate(data: UpdateGameLimitsConfiguration) {
        if (this.isMaster && data.filters) {
            throw new Errors.ValidationError("Filters not allowed for master configuration");
        }

        ["gameLimits", "filters", "segment"].forEach(field => {
            if (field in data) {
                this.validateObject(data[field], field);
            }
        });
    }

    public async validate(entity: BaseEntity,
                          facade: DefaultConfigurationFacade,
                          data: UpdateGameLimitsConfiguration,
                          currentConfiguration?: GameLimitsConfigurationDetailedInfo): Promise<void> {

        if (data.filters) {
            this.validateCurrencies(entity, Object.keys(data.filters));
            this.validateFilters(data.filters, facade.definition.schema);
        }

        const gameLimitsConfig = currentConfiguration
                                 ? { ...currentConfiguration, ...data }
                                 : data;

        if (!gameLimitsConfig.levels && !gameLimitsConfig.filters && !gameLimitsConfig.gameLimits) {
            throw new ValidationError("Game limits should be non empty object");
        }

        let levels: LimitLevel[] = [];
        if (facade.isLevelSupported) {
            const levelPIDs = await this.getAvailableLevels(entity,
                facade,
                currentConfiguration || gameLimitsConfig);

            levels = await this.validateLevels(entity, levelPIDs, data.levels, data.defaultLevel);
        }

        const limitsValidator = new GameLimitsValidator(
            this.keyEntity, entity, facade, gameLimitsConfig, levels);

        if (data.gameLimits) {
            const currencies = Object.keys(data.gameLimits);
            this.validateCurrencies(entity, currencies);
            
            await limitsValidator.validateGameLimits(currentConfiguration?.gameLimits);
        }
    }

    public validateCurrencies(entity: BaseEntity, currencies: string[]) {
        const supportedCurrencies = entity.getCurrencies();
        const invalidCurrencies = currencies.filter(c => c !== DEFAULT_CURRENCY && !supportedCurrencies.includes(c));

        if (invalidCurrencies && invalidCurrencies.length) {
            throw new Errors.ValidationError(`Currencies are not supported: ${invalidCurrencies}`);
        }

        if (entity.isMaster() && !currencies.includes(DEFAULT_CURRENCY)) {
            throw new Errors.ValidationError(
                `Master entity should have gameLimits for default currency - ${DEFAULT_CURRENCY}`);
        }
    }

    private async getAvailableLevels(entity: BaseEntity,
                                     facade: DefaultConfigurationFacade,
                                     gameLimitsConfig: GameLimitsConfiguration): Promise<string[]> {

        const gameLimitsStorage: GameLimitsStorage = new GameLimitsStorage(entity.path,
            gameLimitsConfig.schemaDefinitionId,
            gameLimitsConfig.gameCode,
            gameLimitsConfig.gameGroupId,
            gameLimitsConfig.segmentId,
            "levels");

        const mergedConfig = await gameLimitsStorage.searchAndMerge();

        if (mergedConfig && mergedConfig.levels && mergedConfig.levels.length > 0) {
            return mergedConfig.levels;
        }

        return facade.defaultLevels;
    }

    private async validateLevels(entity: BaseEntity,
                                 allowedLevels: string[],
                                 newLevels?: string[],
                                 defaultLevel?: string) {

        if (defaultLevel) {
            if (typeof defaultLevel !== "string") {
                throw new Errors.ValidationError("default level is invalid value");
            }

            if (!this.isLevelAvailable(newLevels || allowedLevels, defaultLevel)) {
                throw new Errors.ValidationError(`default level ${defaultLevel} is not available`);
            }
        }

        if (newLevels) {
            if (!Array.isArray(newLevels) || newLevels.length === 0 ||
                newLevels.some(level => typeof level !== "string" || level.length > this.maxLevelLength)) {

                throw new Errors.ValidationError("levels are invalid value");
            }

            return this.levelService.validateLevelsByPID(entity.id, newLevels);
        }

        return this.levelService.validateLevelsByPID(entity.id, allowedLevels);
    }

    private isLevelAvailable(allowedLevels: string[], level: string) {
        return allowedLevels.includes(level);
    }

    public validateEntity(entity: BaseEntity): void {
        if (entity.isSuspended()) {
            throw new Errors.ParentSuspendedError();
        }
    }

    public validateFilters(filters: GameLimitsFilters, schema: Schema) {
        const isStakedLimits = Object.keys(schema.properties).includes("stakeAll");
        if (!isStakedLimits) {
            throw new Errors.ValidationError("Filters are allowed only for staked limits");
        }

        // TODO: There may be a bug here. The 'loop' always works only one time
        for (const currency of Object.keys(filters)) {
            this.validateObject(filters[currency], `filters.${currency}`);
            if ("minTotalBet" in filters[currency] && !validatePositiveNumber(filters[currency].minTotalBet)) {
                throw new Errors.ValidationError("minTotalBet should be a positive number");
            }

            if ("maxTotalBet" in filters[currency] && !validatePositiveNumber(filters[currency].maxTotalBet)) {
                throw new Errors.ValidationError("maxTotalBet should be a positive number");
            }

            if ("minTotalBet" in filters[currency] && "maxTotalBet" in filters[currency]) {
                if (filters[currency].minTotalBet >= filters[currency].maxTotalBet) {
                    throw new Errors.ValidationError("maxTotalBet must be greater than minTotalBet");
                }
            }

            return true;
        }
    }
}

class GameLimitsValidator {
    private readonly schemaValidation = new SchemaValidation();
    private readonly commonValidator = new BaseLimitsValidator();
    private readonly gameLimits: GameLimits;
    private currencies: string[];
    private allowOverrideStakeAll: boolean;
    private parentCurrenciesLimits: GameLimitsByCurrencyCode = {};
    private newLevels: string[] = [];
    private payouts: Map<string, number> = new Map();

    constructor(private readonly keyEntity: BaseEntity,
                private readonly entity: BaseEntity,
                private readonly facade: DefaultConfigurationFacade,
                private readonly gameLimitsConfig: GameLimitsConfiguration,
                private readonly levels: LimitLevel[] = []) {

        this.gameLimits = gameLimitsConfig.gameLimits;

        if (gameLimitsConfig.levels) {
            this.newLevels = gameLimitsConfig.levels.filter(level => !this.isLevelFromSchema(level));
        }

    }

    public async validateGameLimits(existingGameLimits: GameLimitsByCurrencyCode): Promise<void> {
        await this.init(this.facade.isLevelSupported);

        for (const currency of this.currencies) {
            const defaults = await getNewLimitsFacade(this.entity)
                .defaults(currency, this.facade);

            const existingCurrencyLimits = existingGameLimits?.[currency];

            if (this.facade.isLevelSupported) {
                this.validateLevelsList(currency);

                for (const level of this.levels) {
                    const currencyLevelLimits = (this.gameLimits as any)?.[currency]?.[level.pid];

                    if (this.facade.isLevelSupported && this.entity.isMaster() && currencyLevelLimits) {
                        getSchemaConfigurationService()
                            .validateFields(currencyLevelLimits,
                                this.facade.definition, currency === DEFAULT_CURRENCY, true);
                    }

                    const isNew = this.newLevels.some(newLevel => level.pid === newLevel);
                    await this.validateCurrencyLimits(currency, defaults, existingCurrencyLimits, level);

                    if (isNew) {
                        const parentCurrencyLimits = (this.parentCurrenciesLimits as any)?.[currency]?.[level.title];

                        if (!currencyLevelLimits && !parentCurrencyLimits && currency === DEFAULT_CURRENCY) {
                            throw new ValidationError(
                                `Limits for new level ${level.title} and currency ${currency} are required`);
                        }

                        if (!currencyLevelLimits) {
                            continue;
                        }
                        this.schemaValidation.validateByDefinition(
                            this.facade.definition, currencyLevelLimits, true);
                    }

                    this.addPayout(currencyLevelLimits, currency);
                }
            } else {
                await this.validateCurrencyLimits(currency, defaults, existingCurrencyLimits);
            }
        }

    }

    private addPayout(gameLimits: RoomGameLimits, currency: string) {
        const bets = gameLimits?.bets;
        if (currency !== DEFAULT_CURRENCY || !bets || !Object.keys(bets).length) {
            return;
        }

        const supportedBets = this.facade.definition.betTypes;
        const betTypes = Object.keys(bets);
        const unsupportedBets = betTypes.filter(bet => !supportedBets.includes(bet));
        if (unsupportedBets && unsupportedBets.length) {
            throw new ValidationError(`Unsupported bet types ${unsupportedBets}`);
        }

        for (const betType of betTypes) {
            if (!("payout" in bets[betType])) {
                bets[betType].payout = this.payouts.get(betType);
            }
        }

    }

    private isLevelFromSchema(level: string) {
        return this.facade.defaultLevels.some(l => l === level);
    }

    private async init(levelsSupported: boolean): Promise<void> {
        const currencies = Object.keys(this.gameLimits);
        this.currencies = currencies.includes(DEFAULT_CURRENCY) ? currencies : [DEFAULT_CURRENCY, ...currencies];

        if (levelsSupported) {
            this.allowOverrideStakeAll = true;

            await this.setParentGameLimits();

            const schemaLevel = Object.keys(this.facade.configuration.EUR)[0];
            const levelBets = this.facade.configuration.EUR[schemaLevel].bets;

            if (levelBets) {
                for (const [betType, value] of Object.entries(levelBets)) {
                    this.payouts.set(betType, (value as any).payout);
                }
            }

            return;
        }

        const entitySettings: EntitySettings = await new EntitySettingsService(this.entity).get();
        this.allowOverrideStakeAll = entitySettings.allowToOverrideDefaultLimits;
    }

    private validateLevelsList(currency) {
        const currencyLimits = this.gameLimitsConfig?.gameLimits?.[currency];
        if (!currencyLimits) {
            return;
        }
        const levelsInLimits = currencyLimits && Object.keys(currencyLimits).filter(level => level !== "aligned");

        const missedLevels = levelsInLimits.filter(level => !this.levels.some(item => item.pid === level));
        if (missedLevels && missedLevels.length) {
            throw new ValidationError(`Please add levels ${missedLevels} to create limits for this levels`);
        }
    }

    private validateGameLimitProperties(currencyGameLimits: GameLimits,
                                        currency: string,
                                        defaultsForCurrency?: GameLimits,
                                        level?: LimitLevel) {
        for (const limitField of Object.keys(currencyGameLimits)) {
            const propertyDefinition = this.facade.definition?.schema?.properties?.[limitField];
            const propertyType = propertyDefinition?.type;
            if (!propertyType) {
                throw new Errors.ValidationError(`Property ${limitField} is missing in schema definition`);
            }

            if (defaultsForCurrency && propertyType === "array") {
                this.validateArray(currencyGameLimits, { ...defaultsForCurrency }, limitField);
            }

            if (limitField === "bets") {
                this.validateBets(currencyGameLimits as RoomGameLimits, currency, level);
            }
        }
    }

    private async validateCurrencyLimits(currency: string,
                                         defaultValuesForCurrency?: SchemaConfigurationDefaultValue,
                                         existingGameLimits?: GameLimits,
                                         level?: LimitLevel): Promise<void> {

        const currencyLimits = (this.gameLimits as GameLimitsByCurrencyCode)?.[currency];

        const currencyLevelLimits = getCurrencyLimits(currencyLimits, level);
        const isDefaultLimits = this.entity.isMaster() && currency === DEFAULT_CURRENCY;

        if (!currencyLevelLimits) {
            if (isDefaultLimits) {
                throw new Errors.ValidationError(
                    `Master entity should have gameLimits for default currency - ${DEFAULT_CURRENCY}`);
            }

            return;
        }

        if (typeof currencyLevelLimits !== "object") {
            throw new Errors.ValidationError(`gameLimits.${currency} should be an object`);
        }

        this.schemaValidation.validateByDefinition(this.facade.definition, currencyLevelLimits, isDefaultLimits);
        this.validatePermissions(currencyLevelLimits, currency, getCurrencyLimits(existingGameLimits, level));

        this.validateGameLimitProperties(
            currencyLevelLimits,
            currency,
            getCurrencyLimits(defaultValuesForCurrency, level),
            level
        );

        this.commonValidator.validateAlertBlockSettings(
            this.facade.definition,
            currencyLevelLimits as RoomGameLimits);
    }

    public validatePermissions(gameLimits: GameLimits,
                               сurrency: string,
                               existingLimits: GameLimits = {}) {

        const schemaPermissions = this.facade.definition.permissions;
        const isMaster = this.keyEntity.isMaster();
        const properties = new Set([...Object.keys(existingLimits), ...Object.keys(gameLimits)]);

        for (const gameLimitsProperty of [...properties.values()].filter(value => value)) {

            const currency = сurrency === DEFAULT_CURRENCY ? "default" : сurrency;
            const permission: GameLimitsPermissionType =
                schemaPermissions?.[currency]?.[gameLimitsProperty] as GameLimitsPermissionType ||
                schemaPermissions?.default?.[gameLimitsProperty];

            const existingValue = (existingLimits as any)?.[gameLimitsProperty];
            const newValue = gameLimits[gameLimitsProperty];

            this.validatePropertyPermission(permission, existingValue, newValue,
                gameLimitsProperty, currency, isMaster);

            if (gameLimitsProperty === "bets") {
                this.validateBetsPermission(gameLimits, сurrency, existingLimits, isMaster);
            }
        }
    }

    private validatePropertyPermission(propertyPermission: GameLimitsPermissionType,
                                       existingValue: any, newValue: any,
                                       property: string,
                                       currency: string,
                                       isMaster: boolean) {
        // throw error only if entity try to change value and doesn't have permission for this
        if (!propertyPermission) {
            throw new Errors.ValidationError(`Property ${property} for currency ${currency} ` +
                "doesn't exist in schema definition");
        }

        const noPermission = !isMaster && propertyPermission !== GameLimitsPermissionType.ENTITY;
        const valueChanged = newValue !== undefined && (existingValue
                                                        ? existingValue !== newValue
                                                        : true);
        if (noPermission && valueChanged) {
            throw new Errors.OperationForbidden(`Not allowed to modify ${property} for currency ` +
                currency);
        }
    }

    private validateBetsPermission(gameLimits: GameLimits,
                                   currency: string,
                                   existingLimits: GameLimits = {},
                                   isMaster: boolean) {

        for (const betType of this.facade.definition.betTypes) {
            const betSchema = this.facade.definition.getBetTypeSchema(betType);
            const props = Object.keys(betSchema.properties) as Array<keyof BetTypeRoomLimits>;

            for (const prop of props) {
                const permission = this.facade.definition.getBetPropPermission(currency, prop) ||
                    this.facade.definition.getBetPropPermission("default", prop);

                const existingValue = (existingLimits as RoomGameLimits)?.bets?.[betType]?.[prop];
                const newValue = (gameLimits as RoomGameLimits)?.bets?.[betType]?.[prop];

                if ((existingValue === undefined && newValue === undefined) || !permission) {
                    continue;
                }

                try {
                    this.validatePropertyPermission(permission, existingValue, newValue,
                        `bets.${betType}.${prop}`, currency, isMaster);
                } catch (err) {
                    // crutch to get payouts from schema for new levels
                    if (err instanceof Errors.OperationForbidden && prop === "payout") {
                        (gameLimits as RoomGameLimits).bets[betType][prop] = this.payouts.get(betType);
                    } else {
                        throw err;
                    }
                }

            }
        }
    }

    private validateArray(currencyGameLimits: GameLimits,
                          defaultValuesForCurrency: GameLimits,
                          gameLimitsProperty: string) {

        const skipDefaultValuesCheck = this.allowOverrideStakeAll && gameLimitsProperty === "stakeAll";
        const availableValues = defaultValuesForCurrency[gameLimitsProperty];
        if (!Array.isArray(availableValues)) {
            throw new Errors.ValidationError(`${gameLimitsProperty} in schema configuration must be array`);
        }
        const values = currencyGameLimits[gameLimitsProperty];

        const invalidValues = !skipDefaultValuesCheck && values.filter(value => !availableValues.includes(value));

        if (invalidValues && invalidValues.length) {
            throw new Errors.ValidationError(`Values ${invalidValues} are not allowed for ` +
                gameLimitsProperty + " allowed values: " + availableValues);
        }

        this.commonValidator.validateStakeAll(currencyGameLimits);
    }

    private async setParentGameLimits() {
        if (this.entity.isMaster()) {
            return;
        }

        const parent = (this.entity as ChildEntity).getParent();

        const gameLimitsConfiguration: GameLimitsConfigurationInStorage = await findGameLimitsConfiguration(
            parent,
            this.gameLimitsConfig.gameCode,
            this.facade.definition.id,
            this.gameLimitsConfig.gameGroupId,
            this.gameLimitsConfig.segmentId);

        for (const currency of this.currencies) {
            this.parentCurrenciesLimits[currency] = await getNewLimitsFacade(parent)
                .build(this.facade, 1, currency, gameLimitsConfiguration);

        }
    }

    private validateBets(gameLimits: RoomGameLimits, currency: string, level: LimitLevel) {
        const betTypes = Object.keys(gameLimits.bets);
        if (!betTypes.length) {
            throw new ValidationError("Bets should be non-empty object");
        }

        const currencyLimits: MultiRoomGameLimits = this.parentCurrenciesLimits?.[currency] as MultiRoomGameLimits;
        const errors: string[] = [];

        if (this.entity.isMaster()) {
            return;
        }

        for (const betType of betTypes) {
            const [betMin, betMax] = this.getMinAndMaxBets(currencyLimits, level, betType);

            if (betMin && ("min" in gameLimits.bets[betType])) {
                const current = gameLimits.bets[betType].min;

                if (betMin > current) {
                    errors.push(`Minimum value for ${currency}.${level}.bets.${betType}.min is ${betMin}`);
                }
            }

            if (betMax && ("max" in gameLimits.bets[betType])) {
                const current = gameLimits.bets[betType].max;

                if (betMax < current) {
                    errors.push(`Maximum value for ${currency}.${level}.bets.${betType}.max is ${betMax}`);
                }
            }
        }

        if (errors.length) {
            throw new ValidationError(errors);
        }
    }

    private getMinAndMaxBets(parentLimits: MultiRoomGameLimits, level: LimitLevel, betType: string): [number, number] {
        const parentBetLimits: BetTypeRoomLimits = parentLimits?.[level.title]?.bets?.[betType];
        if (parentBetLimits) {
            return [parentBetLimits.min, parentBetLimits.max];
        }

        const minBets = [];
        const maxBets = [];

        for (const roomLimits of Object.values(parentLimits).filter(roomLimits => roomLimits.bets?.[betType])) {
            const betTypeLimits = roomLimits.bets[betType];
            if (betTypeLimits.min) {
                minBets.push(betTypeLimits.min);
            }

            if (betTypeLimits.max) {
                maxBets.push(betTypeLimits.max);
            }
        }

        return [Math.min(...minBets), Math.max(...maxBets)];
    }
}
