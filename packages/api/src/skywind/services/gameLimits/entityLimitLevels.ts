import { CRUDServiceImpl } from "../crudService";
import {
    EntityGameLimitLevel,
    EntityGameLimitLevelDBInstance,
    EntityLimitLevelModel,
} from "../../models/entityLimitLevels";
import { ValidationError } from "../../errors";
import * as sequelize from "sequelize";
import { FindOptions, Op, Transaction, WhereOptions } from "sequelize";
import { getChildIds, getParentIds } from "../entity";
import { BaseEntity } from "../../entities/entity";
import { GameFeatures } from "../../entities/game";
import { DEFAULT_CURRENCY } from "../../models/currencyMultiplier";
import EntityCache from "../../cache/entity";
import { Models } from "../../models/models";

export class GameLimitLevelService
    extends CRUDServiceImpl<EntityGameLimitLevelDBInstance, EntityGameLimitLevel, EntityLimitLevelModel> {
    /*
        Flag "internal" means that GET method will return relations which shows that level belong to entity game.
     */
    constructor(private entity: BaseEntity, private internal: boolean = false) {
        super();
    }

    public getModel(): EntityLimitLevelModel {
        return Models.EntityGameLimitLevelModel;
    }

    protected validateCreateData(data: EntityGameLimitLevel): EntityGameLimitLevel {
        if (!data.levelId) {
            throw new ValidationError("Level ID is required");
        }

        if (data.isDefault && data.hidden) {
            throw new ValidationError("Impossible to customize \"isDefault\" and \"hidden\" at once");
        }

        if (data.currency && !this.entity.currencyExists(data.currency)) {
            throw new ValidationError(`Invalid currency code or not belong to the entity - ${data.currency}`);
        }

        if (!this.internal && !(data.isDefault || data.hidden)) {
            throw new ValidationError("Invalid customization - isDefault or hidden should be define");
        }

        if (data.isDefault && data.currency) {
            throw new ValidationError("Default level should be for all currencies");
        }

        if (data.currency === DEFAULT_CURRENCY) {
            throw new ValidationError("You can't customize default currency");
        }

        return {
            gameCode: data.gameCode,
            levelId: data.levelId,
            isDefault: data.isDefault || false,
            hidden: data.hidden || false,
            currency: data.currency || null,
            inherited: data.inherited || false,
            entityId: this.entity.id
        };
    }

    protected async performCreate(cleanedData: EntityGameLimitLevel,
                                  transaction: sequelize.Transaction): Promise<EntityGameLimitLevelDBInstance> {

        await this.validateRelations(cleanedData);

        if (cleanedData.isDefault) {
            await this.getModel().destroy({ where: { entityId: cleanedData.entityId, isDefault: true }, transaction });
        }

        return super.performCreate(cleanedData, transaction);
    }

    private async validateRelations(cleanedData: EntityGameLimitLevel) {
        const { entityId, gameCode } = cleanedData;

        if (!gameCode) {
            return;
        }

        const entityGame = await Models.EntityGameModel
            .findOne({
                where: { entityId },
                include: [{ model: Models.GameModel, where: { code: gameCode } }],
                attributes: ["id"]
            });

        if (!entityGame) {
            throw new ValidationError(`Entity game not found - ${gameCode}`);
        }

        const features: GameFeatures = entityGame.get("game").get("features");
        if (!(features && features.live)) {
            throw new ValidationError("Game should be a live game");
        }

        const level = await Models.LimitLevelModel.findByPk(cleanedData.levelId);
        if (!level) {
            throw new ValidationError("Limit level not found");
        }

        const instances = await this.list({
            where: {
                entityId,
                gameCode,
                [Op.or]: [{ isDefault: true }, { hidden: true }]
            }
        } as FindOptions<any>);

        for (const instance of instances) {
            if (cleanedData.isDefault !== instance.isDefault) {
                continue;
            }

            if (cleanedData.currency !== instance.currency) {
                continue;
            }

            if (cleanedData.hidden !== instance.hidden) {
                continue;
            }

            if (cleanedData.levelId !== instance.levelId) {
                continue;
            }

            throw new ValidationError("Same level customization already exists");
        }

        for (const entityId of [this.entity.id, ...getChildIds(this.entity)]) {
            await this.checkAvailableLevels(entityId, cleanedData);
        }
    }

    private async checkAvailableLevels(entityId: number, cleanedData: EntityGameLimitLevel): Promise<void> {
        const isLevelHidden = (item: { hidden?: boolean, currency?: string }) => item.hidden && !item.currency;
        if (!isLevelHidden(cleanedData)) {
            return;
        }

        const entity = await EntityCache.findById(entityId);

        const entityIds = [...getParentIds(entity), entity.id];
        const levels = await Models.LimitLevelModel.findAll({
            where: {
                entityId: {
                    [Op.in]: entityIds
                }
            },
            include: [this.getModel()]
        });

        for (const level of levels) {
            if (cleanedData.levelId === level.id) {
                continue;
            }

            const levelHidden = level.get("entity_game_limit_levels")
                .map(item => item.toInfo(entity))
                .filter(info => (info.inherited || info.owned) && info.gameCode === cleanedData.gameCode)
                .some(isLevelHidden);

            if (!levelHidden) {
                return;
            }
        }

        throw new ValidationError(`At least one level should be available for entity: ${entity.name}`);
    }

    public async getGameLimitLevels(options?: WhereOptions<any>): Promise<EntityGameLimitLevel[]> {
        const instances = await this.list({
            where: {
                entityId: {
                    [Op.in]: [...getParentIds(this.entity), this.entity.id]
                },
                ...options
            },
            include: [Models.LimitLevelModel]
        });

        return instances
            .filter(instance => this.internal || (instance.isDefault || instance.hidden))
            .map(instance => instance.toInfo(this.entity))
            .filter(info => info.owned || info.inherited);
    }

    protected async performDestroy(instance: EntityGameLimitLevelDBInstance,
                                   transaction: sequelize.Transaction): Promise<void> {
        if (instance.entityId !== this.entity.id) {
            throw new ValidationError("Game limit do not belong to entity");
        }

        return super.performDestroy(instance, transaction);
    }

    public async destroyBySchemaDefinitionId(schemaDefinitionId: number, transaction?: Transaction): Promise<number> {
        const whereOptions: WhereOptions<any> = {
            entityId: this.entity.id,
            schemaDefinitionId,
            gameCode: null,
            isDefault: false,
            hidden: false
        };

        return this.getModel().destroy({ where: whereOptions, transaction });
    }

    public async destroyByGameCodes(gameCodes: string[], transaction?: Transaction): Promise<number> {
        const whereOptions: WhereOptions<any> = {
            entityId: this.entity.id,
            gameCode: { [Op.in]: gameCodes },
            isDefault: false,
            hidden: false
        };

        return this.getModel().destroy({ where: whereOptions, transaction });
    }
}

export const getGameLimitLevelService = (entity: BaseEntity, internal: boolean = false) => new GameLimitLevelService(
    entity, internal);
