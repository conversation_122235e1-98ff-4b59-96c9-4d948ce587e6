import { ValidationError } from "../../errors";
import { SchemaDefinitionInstance } from "../../models/schemaDefinition";
import { GameLimits, RoomGameLimits } from "../../models/gameLimitsConfiguration";
import { LimitLevel } from "../../models/limitLevels";

export class BaseLimitsValidator {

    public validateStakeAll(data: { stakeAll?: number[]; defaultTotalStake?: number; [key: string]: any }): void {
        if (data.stakeAll) {
            data.stakeAll = data.stakeAll.sort((a, b) => a - b);
            let prevBet: number = -1;
            for (const eachBet of data.stakeAll) {
                if (eachBet <= 0) {
                    throw new ValidationError("Bet should be positive");
                }
                if (eachBet === prevBet) {
                    throw new ValidationError("Bet elements should be unique");
                }
                prevBet = eachBet;
            }
            if (data.defaultTotalStake < data.stakeAll[0]) {
                throw new ValidationError("Default total stake less than min coin");
            }
        }
    }

    public validateAlertBlockSettings(schema: SchemaDefinitionInstance, limits: RoomGameLimits): void {
        if (schema.levelsSupported() && limits !== undefined) {
            this.validateAlertBlock(limits.alert, limits.block);

            if (limits.bets) {
                for (const betType in limits.bets) {
                    if (limits.bets.hasOwnProperty(betType)) {
                        this.validateAlertBlock(limits.bets[betType].alert, limits.bets[betType].block);
                    }
                }
            }
        }
    }

    private validateAlertBlock(alert: number, block: number): void {
        if (alert !== undefined && (typeof alert !== "number" || alert <= 0 || alert >= 100)) {
            throw new ValidationError("alert should be number in range (0, 100)");
        }

        if (block !== undefined && (typeof block !== "number" || block <= 0 || block >= 100)) {
            throw new ValidationError("block should be number in range (0, 100)");
        }

        if (alert && block && (block < alert)) {
            throw new ValidationError("Block value must be equal or higher that alert value.");
        }
    }
}

export function getCurrencyLimits(limits: GameLimits, level?: LimitLevel | string): GameLimits {
    if (level) {
        if (typeof level === "string") {
            return (limits as any)?.[level];
        }

        return (limits as any)?.[level.pid];
    }
    return limits;
}

export enum LIMITS_CONFIGURATION_STATUS {
    BROKEN = "broken",
    ARCHIVED = "archived",
    PENDING = "pending",
    INACTIVE = "inactive",
    ACTIVE = "active"
}

export const LIMITS_CONFIGURATION_STATUS_SET = new Set(Object.values(LIMITS_CONFIGURATION_STATUS));
