import { ValidationError } from "../../errors";
import { CurrencyMultiplier, DEFAULT_CURRENCY } from "../../models/currencyMultiplier";
import { CurrencyCode, Limits } from "../../entities/gamegroup";
import {
    BetsLimits,
    GameLimitsByCurrencyCode,
    GameLimitsConfigurationInStorage
} from "../../models/gameLimitsConfiguration";
import { BaseLimitsExchanger, getLimitsExchanger } from "./limitsExchanger";
import { getLimitsCalculator, LimitsCalculator } from "./limitsCalculator";
import { getCurrencyLimits } from "./helper";
import { EntityGameLimitLevel } from "../../models/entityLimitLevels";
import { LimitLevel } from "../../models/limitLevels";
import { limitLevelService } from "./limitLevels";
import { DefaultConfigurationFacade } from "./defaultConfigurationFacade";
import { getCurrencyExchange } from "../currencyExchange";
import { Currencies, CurrencyExchange } from "@skywind-group/sw-currency-exchange";
import { FORBIDDEN_CURRENCIES } from "../../utils/common";
import { Op } from "sequelize";
import { decodeId } from "../../utils/publicid";
import { LIMIT_CONFIGURATION_TYPE, PropertiesSchema, PropertySchema } from "../../entities/schemaDefinition";

const stakeAllProp = "stakeAll";
const alignedProp = "aligned";
const gameLimitsProp = "gameLimits";
const betsProp = "bets";
export const SMART_ROUNDING_PROP = "applySmartRounding";

/*
This function builds limits for a game by SchemaDefinition, SchemaConfiguration and GameLimitsConfiguration.

Fields with calculated:true are calculated using methods from limitsCalculationHelper,
method names are calculate${fieldName}.
There are little set of calculated field names. The methods have params: stakeAll, totalBetMultiplier,
defaultTotalStake.
    defaultTotalStake is special field(not present in defintion) used to find stakeDef.
    stakeAll is special field (array of possible bets). If it present in limitsConfiguration for current currency
    it takes from there. Otherwise it's taken from schema configuration, when aligned flag is true array from schema
    configuration is merged with array for default currency. If algined:false it's taken as is. Otherwise stakeAll
    from defaultCurrency exchanged to current currency.

Fields with configurated: true are taken from GameLimitsConfiguration or SchemaConfiguration with following priorities:
    GameLimitsConfiguration for required currency
    SchemaConfiguration for required currency
    GameLimitsConfiguration for default currency with exchange
    SchemaConfiguration for default currency with exchange
There is special flag constForAllCurrencies it works only for configurated fields. When the flag is true currency
exchange is not applied to the value
*/

export class NewLimitsSystemBuilder {
    private propertyBuilder: LimitBuilder;

    constructor(public facade: DefaultConfigurationFacade,
                public currency: CurrencyCode,
                public gameLimitsConfiguration?: GameLimitsConfigurationInStorage,
                public totalBetMultiplier: number = 1,
                public customMultipliers?: CurrencyMultiplier,
                public gameLimitLevels: EntityGameLimitLevel[] = [],
                public customToEURMultiplier?: number,
                public isSmartRoundingSupported?: boolean) {

    }

    public async build(forceAddDefaultTotalStake?: boolean, skipCurrencyMultiplierError?: boolean): Promise<Limits> {
        const result: Limits = {};

        const properties = this.facade.definition.schema.properties;

        const currencyService = await getCurrencyExchange();
        this.propertyBuilder = new LimitBuilder(this.facade,
            this.currency,
            currencyService,
            this.gameLimitsConfiguration,
            this.totalBetMultiplier,
            this.customMultipliers,
            this.customToEURMultiplier,
            this.isSmartRoundingSupported);

        if (this.facade.isLevelSupported) {
            const levels = await this.getLevels();
            for (const level of levels.filter(({ id }) => this.isAllowedLevel(id))) {

                this.buildLevelValues(
                    result,
                    properties,
                    level,
                    forceAddDefaultTotalStake,
                    skipCurrencyMultiplierError
                );
                if (this.isDefaultLevel(level)) {
                    this.setValue(result, "isDefaultRoom", true, level.title);
                }
            }

            if (!Object.keys(result).length) {
                throw new ValidationError("At least one level should be visible");
            }
        } else {
            this.buildLevelValues(
                result,
                properties,
                undefined,
                forceAddDefaultTotalStake,
                skipCurrencyMultiplierError
            );
        }

        return result;
    }

    private async getLevels(): Promise<LimitLevel[]> {
        const levelPIDs = this.gameLimitsConfiguration?.levels && this.gameLimitsConfiguration.levels.length
                          ? this.gameLimitsConfiguration.levels
                          : this.facade.defaultLevels;

        const levelIds = levelPIDs.map(pid => decodeId(pid));
        const levels = await limitLevelService.get().list({ where: { id: { [Op.in]: levelIds } } });

        return levels.map(item => item.toInfo(true));
    }

    private get defaultLevel(): string {
        return this.gameLimitsConfiguration?.defaultLevel;
    }

    private buildLevelValues(result: Limits,
                             properties: PropertiesSchema,
                             level?: LimitLevel,
                             forceAddDefaultTotalStake?: boolean,
                             skipCurrencyMultiplierError?: boolean) {

        this.propertyBuilder.level = level;

        let stakeAll: number[];
        if (properties[stakeAllProp]) {
            stakeAll = this.propertyBuilder.buildStakeAll(this.currency, skipCurrencyMultiplierError);
            this.setValue(result, stakeAllProp, stakeAll, level?.title);
        }

        const defaultTotalStake = this.propertyBuilder.getCustomValue(
            "defaultTotalStake", properties.defaultTotalStake, skipCurrencyMultiplierError);

        if (forceAddDefaultTotalStake) {
            this.setValue(result, "defaultTotalStake", defaultTotalStake, level?.title);
        }

        const concurrentPlayers = level && this.propertyBuilder
            .getCustomValue("concurrentPlayers", properties.concurrentPlayers, skipCurrencyMultiplierError);

        const bets = level && this.propertyBuilder.getBets(concurrentPlayers);

        for (const prop in properties) {
            if (properties.hasOwnProperty(prop) && prop !== stakeAllProp && prop !== alignedProp) {
                const { limitConfigurationType, type } = properties[prop];

                let value;
                if (type === "object" && prop === betsProp) {
                    value = bets;
                } else if (prop === "exposure") {
                    value = this.propertyBuilder.getTotalExposure(bets);
                } else if (prop === "winMax" &&
                    !(Currencies.get(this.currency).isVirtual || FORBIDDEN_CURRENCIES.includes(this.currency))) {
                    value = this.propertyBuilder.getWinMax();
                } else if (limitConfigurationType === LIMIT_CONFIGURATION_TYPE.CALCULATED) {
                    value = this.propertyBuilder.getBaseCalculatedValue(
                        prop, stakeAll, defaultTotalStake, properties[prop]);
                } else {
                    value = this.propertyBuilder.getCustomValue(prop, properties[prop], skipCurrencyMultiplierError);
                }

                this.setValue(result, prop, value, level?.title);
            }
        }
    }

    private setValue(result: Limits, prop: string, value: number | number[] | boolean, level?: string) {
        if (level) {
            if (!result[level]) {
                result[level] = {};
            }
            if (value !== undefined) {
                result[level][prop] = value;
            }
        } else {
            if (value !== undefined) {
                result[prop] = value;
            }
        }
    }

    private isAllowedLevel(levelId: number): boolean {
        return !this.gameLimitLevels.some(item => item.hidden && item.levelId === levelId &&
            (item.currency === this.currency || !item.currency));
    }

    private isDefaultLevel(level: LimitLevel): boolean {
        const isOwnedDefaultLevel = this.gameLimitLevels.find(item => item.isDefault && item.owned);

        if (isOwnedDefaultLevel) {
            return isOwnedDefaultLevel.levelId === level.id;
        }

        const isParentDefaultLevel = this.gameLimitLevels.find(item => item.isDefault && !item.owned);

        if (isParentDefaultLevel) {
            return isParentDefaultLevel.levelId === level.id;
        }

        return level.pid === this.defaultLevel;
    }

}

class LimitBuilder {
    private calculator: LimitsCalculator;
    private exchanger: BaseLimitsExchanger;
    public static properties: Set<string>;
    public level: LimitLevel;

    constructor(public facade: DefaultConfigurationFacade,
                public currency: CurrencyCode,
                public currencyService: CurrencyExchange,
                public gameLimitsConfiguration?: GameLimitsConfigurationInStorage,
                public totalBetMultiplier: number = 1,
                public customMultipliers?: CurrencyMultiplier,
                public customToEURMultiplier?: number,
                public isSmartRoundingSupported?: boolean) {

        this.calculator = getLimitsCalculator(this.facade.isLevelSupported);
        this.exchanger = getLimitsExchanger(this.customMultipliers);

        LimitBuilder.properties = new Set<string>([]);
    }

    @addProperty("winMax")
    public getWinMax(): number {
        const gameLimits = (this.gameLimitsConfiguration as any)?.[gameLimitsProp];
        const currentWinMax = this.getProperty(gameLimits as any, this.currency) as number;
        if (currentWinMax) {
            return currentWinMax;
        }

        const winMaxInEur = this.getProperty(gameLimits as any, this.defaultCurrency) as number;
        if (winMaxInEur) {
            return this.currencyService.exchange(winMaxInEur, this.defaultCurrency, this.currency);
        }

        const masterWinMax = this.getProperty(this.facade.configuration as any, this.currency) as number;
        if (masterWinMax) {
            return masterWinMax;
        }

        const masterWinMaxInEur = this.getProperty(this.facade.configuration as any, this.defaultCurrency) as number;
        if (masterWinMaxInEur) {
            return this.currencyService.exchange(masterWinMaxInEur, this.defaultCurrency, this.currency);
        }
    }

    @addProperty()
    public getCustomValue(
        property: string,
        propertySchema: PropertySchema,
        skipCurrencyMultiplierError?: boolean
    ): number {

        const gameLimits = (this.gameLimitsConfiguration as any)?.[gameLimitsProp];
        const limitsFromSchema = this.facade.configuration as any;

        const currencyGameLimits = this.getProperty(gameLimits as any, this.currency);
        if (currencyGameLimits) {
            return currencyGameLimits as number;
        }

        const convertedGameLimitsValue = this.convertValueFromDefaultCurrency(
            gameLimits as any,
            propertySchema,
            skipCurrencyMultiplierError
        );

        if (this.customMultipliers) {
            if (convertedGameLimitsValue) {
                return convertedGameLimitsValue;
            }
        }

        const currencySchemaLimits = this.getProperty(limitsFromSchema, this.currency);

        if (currencySchemaLimits) {
            return currencySchemaLimits as number;
        }

        if (!this.customMultipliers && convertedGameLimitsValue) {
            return convertedGameLimitsValue;
        }

        return this.convertValueFromDefaultCurrency(
            limitsFromSchema,
            propertySchema,
            skipCurrencyMultiplierError
        );
    }

    @addProperty()
    public getBaseCalculatedValue(property: string,
                                  stakeAll: number[],
                                  defaultTotalStake: number,
                                  propertySchema: PropertySchema): number {

        const value = this.calculator[`calculate${property}`](stakeAll, this.totalBetMultiplier, defaultTotalStake);
        return this.smartRound(value, propertySchema);
    }

    public getBetCalculatedValue(prop: string,
                                 maxBet: number,
                                 payout: number,
                                 concurrentPlayers: number,
                                 propertySchema: PropertySchema): number {

        const value = this.calculator[`calculateBets${prop}`](maxBet, payout, concurrentPlayers);
        return this.smartRound(value, propertySchema);
    }

    public getTotalExposure(bets: BetsLimits) {
        let totalExposure = 0;

        for (const betType of this.facade.definition.betTypes) {
            if (bets[betType].exposure) {
                totalExposure += bets[betType].exposure;
            }
        }

        const blockSchema = this.facade.definition.schema.properties.block;
        if (blockSchema) {
            const block = this.getCustomValue("block", blockSchema);
            return this.applyBlock(totalExposure, block);
        }

        return totalExposure;
    }

    @addProperty(stakeAllProp)
    public buildStakeAll(currency: string, skipCurrencyMultiplierError?: boolean): number[] {
        const limitType = this.facade.definition.schema.properties?.[stakeAllProp]?.limitConfigurationType;
        const constForAllCurrencies = this.isCurrencyConversionDisabled(limitType);

        const gameLimits = (this.gameLimitsConfiguration as any)?.[gameLimitsProp];
        const fromLimitsConfiguration: any = this.getProperty(gameLimits as any, currency);

        // The simplest way
        if (fromLimitsConfiguration) {
            const convertedCoins = this.exchanger.convertCoinBets(
                fromLimitsConfiguration,
                this.defaultCurrency,
                this.isSmartRoundingSupported,
                undefined,
                skipCurrencyMultiplierError
            );
            const aligned: boolean = (gameLimits as any)?.[currency]?.[alignedProp];
            if (!aligned || currency === this.defaultCurrency) {
                return this.applyStakeAllFilter(convertedCoins, currency);
            }

            // prevent infinite recursion
            if (currency === this.defaultCurrency) {
                throw new ValidationError("Cannot find stakeAll for default currency");
            }

            const defaultCurrencyStakeAll: number[] = this.buildStakeAll(this.defaultCurrency);

            const fromDefaultCurrency: number[] = this.exchanger
                .convertCoinBets(defaultCurrencyStakeAll,
                    constForAllCurrencies ? this.defaultCurrency : currency,
                    this.isSmartRoundingSupported,
                    constForAllCurrencies ? undefined : this.customToEURMultiplier,
                    skipCurrencyMultiplierError);

            return this.applyStakeAllFilter(this.getUniqueAndSort(...convertedCoins, ...fromDefaultCurrency), currency);
        }

        const stakeAllFromSchema = this.getProperty(
            this.facade.configuration as any, currency) as number[];

        const aligned: boolean = this.facade.configuration?.[currency]?.[alignedProp];

        if (stakeAllFromSchema && !aligned && !this.customMultipliers) {
            return this.applyStakeAllFilter(stakeAllFromSchema, currency);
        }

        // prevent infinite recursion
        if (currency === this.defaultCurrency) {
            throw new ValidationError("Cannot find stakeAll for default currency");
        }

        const defaultCurrencyStakeAll: number[] = this.buildStakeAll(this.defaultCurrency, skipCurrencyMultiplierError);

        const fromDefaultCurrency: number[] = this.exchanger
            .convertCoinBets(defaultCurrencyStakeAll,
                constForAllCurrencies ? this.defaultCurrency : currency,
                this.isSmartRoundingSupported,
                constForAllCurrencies ? undefined : this.customToEURMultiplier,
                skipCurrencyMultiplierError);

        if (stakeAllFromSchema && !this.customMultipliers) {
            const unique = this.getUniqueAndSort(...fromDefaultCurrency, ...stakeAllFromSchema);
            return this.applyStakeAllFilter(unique, currency);
        }
        return this.applyStakeAllFilter(fromDefaultCurrency, currency);
    }

    public getUniqueAndSort(...duplicates: number[]): number[] {
        return [...new Set(duplicates)].sort((a, b) => a - b);
    }

    public applyStakeAllFilter(stakeAll: number[], currency: string): number[] {

        const filter: {
            minTotalBet?: number,
            maxTotalBet?: number
        } = this.gameLimitsConfiguration?.filters?.[currency];

        if (!filter) {
            return stakeAll;
        }

        return stakeAll
            .filter(stake => (!filter.minTotalBet || stake * this.totalBetMultiplier >= filter.minTotalBet) &&
                (!filter.maxTotalBet || stake * this.totalBetMultiplier <= filter.maxTotalBet));
    }

    @addProperty(betsProp)
    public getBets(concurrentPlayers: number = 1): BetsLimits {
        const result: BetsLimits = {};
        const betTypes = this.facade.definition.betTypes;
        if (!betTypes) {
            return;
        }

        for (const betType of betTypes) {
            LimitBuilder.properties.add(betType);
            result[betType] = {} as any;

            const betTypeSchema = this.facade.definition.getBetTypeSchema(betType);

            const maxBet = this.getCustomValue("max", betTypeSchema.properties.max);
            const payout = this.getCustomValue("payout", betTypeSchema.properties.payout);

            for (const prop of Object.keys(betTypeSchema.properties)) {
                const propSchema = betTypeSchema.properties[prop];
                let value;

                if (propSchema.limitConfigurationType === LIMIT_CONFIGURATION_TYPE.CALCULATED) {
                    value = this.getBetCalculatedValue(prop, maxBet, payout, concurrentPlayers, propSchema);
                } else if (prop === "max") {
                    value = maxBet;
                } else {
                    value = this.getCustomValue(prop, propSchema);
                }

                result[betType][prop] = value;
            }
            LimitBuilder.properties.delete(betType);

            if (result[betType].exposure) {
                result[betType].exposure = this.applyBlock(result[betType].exposure, result[betType].block);
            }
        }

        return result;
    }

    public applyBlock(exposure?: number, block: number = 100, smartRounding?: boolean) {
        if (!exposure) {
            return;
        }

        const customExposure = exposure * block / 100;
        return this.exchanger.exchange(customExposure,
            this.defaultCurrency,
            smartRounding || this.isSmartRoundingSupported);
    }

    private get defaultCurrency() {
        return this.customMultipliers?.baseCurrency || DEFAULT_CURRENCY;
    }

    private isCurrencyConversionDisabled(limitConfigurationType: LIMIT_CONFIGURATION_TYPE) {
        return limitConfigurationType === LIMIT_CONFIGURATION_TYPE.CONST;
    }

    private convertValueFromDefaultCurrency(gameLimits: GameLimitsByCurrencyCode,
                                            propertySchema: PropertySchema,
                                            skipCurrencyMultiplierError?: boolean) {

        const defaultCurrencyLimits = this.getProperty(gameLimits, this.defaultCurrency);
        const limitType = propertySchema?.limitConfigurationType;
        const constForAllCurrencies = this.isCurrencyConversionDisabled(limitType);
        const smartRounding = propertySchema?.[SMART_ROUNDING_PROP];

        if (defaultCurrencyLimits) {
            return this.exchanger.exchange(
                defaultCurrencyLimits,
                constForAllCurrencies ? this.defaultCurrency : this.currency,
                !!smartRounding || this.isSmartRoundingSupported,
                constForAllCurrencies ? undefined : this.customToEURMultiplier,
                skipCurrencyMultiplierError
            );
        }
    }

    private smartRound(value: number, propertySchema: PropertySchema) {
        const smartRounding = propertySchema?.[SMART_ROUNDING_PROP];

        if (smartRounding) {
            return this.exchanger.roundCoinBet(value);
        }
        return value;
    }

    private getProperty(gameLimits: GameLimitsByCurrencyCode, currency: string): number | number[] {
        const currencyLimits = gameLimits?.[currency];
        const limits = getCurrencyLimits(currencyLimits, this.level) as any;

        if (!LimitBuilder.properties || !LimitBuilder.properties.size) {
            throw new ValidationError("Properties are required");
        }

        return this.safeGet<number | number[]>(limits);
    }

    private safeGet<T>(obj: any): T {
        let value = { ...obj };
        for (const property of LimitBuilder.properties) {
            value = value?.[property];
        }
        return value;
    }
}

function addProperty(property?: string) {
    return (target, key, descriptor) => {
        const originalMethod = descriptor.value;

        descriptor.value = function(...argsOfDecoratedFunc: any[]) {
            const prop = property || argsOfDecoratedFunc[0];
            try {
                LimitBuilder.properties.add(prop);

                return originalMethod.apply(this, argsOfDecoratedFunc);
            } finally {
                LimitBuilder.properties.delete(prop);
            }

        };

        return descriptor;
    };
}
