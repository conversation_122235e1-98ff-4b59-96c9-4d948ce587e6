import { CRUDServiceImpl } from "../crudService";
import { lazy } from "@skywind-group/sw-utils";
import { LimitLevel, LimitLevelDBInstance, LimitLevelModel } from "../../models/limitLevels";
import * as sequelize from "sequelize";
import { ValidationError } from "../../errors";
import { getChildIds, getParentIds } from "../entity";
import EntityCache from "../../cache/entity";
import { Models } from "../../models/models";
import { decodeId, encodeId } from "../../utils/publicid";
import { Op } from "sequelize";

export const SEARCH_FIELDS = ["title"];

const EntityGameLimitLevelModel = Models.EntityGameLimitLevelModel;
export class LimitLevelService
    extends CRUDServiceImpl<LimitLevelDBInstance, LimitLevel, LimitLevelModel> {
    private model = Models.LimitLevelModel;

    public getModel(): LimitLevelModel {
        return this.model;
    }

    protected validateCreateData(data: LimitLevel): LimitLevel {
        return {
            entityId: data.entityId,
            title: data.title
        };
    }

    protected async performCreate(cleanedData: LimitLevel,
                                  transaction: sequelize.Transaction): Promise<LimitLevelDBInstance> {
        await this.checkLabelExists(cleanedData.title, cleanedData.entityId);

        return super.performCreate(cleanedData, transaction);
    }

    protected validateUpdateData(data: Partial<LimitLevel>): Partial<LimitLevel> {
        return {
            title: data.title,
            entityId: data.entityId
        };
    }

    protected async performUpdate(instance: LimitLevelDBInstance,
                                  cleanedData: Partial<LimitLevel>,
                                  transaction: sequelize.Transaction): Promise<LimitLevelDBInstance> {
        if (instance.entityId !== cleanedData.entityId) {
            throw new ValidationError("Limit level belong to another entity");
        }

        await this.checkLabelExists(cleanedData.title, cleanedData.entityId);

        return super.performUpdate(instance, cleanedData, transaction);
    }

    private async checkLabelExists(title: string, entityId: number): Promise<void> {
        const entity = await EntityCache.findOne({ id: entityId });
        const entityIds = [...getParentIds(entity), ...getChildIds(entity), entity.id];
        const levels = await this.list({ where: { title, entityId: { [Op.in]: entityIds } } });

        if (levels.length) {
            throw new ValidationError("Level is already exists");
        }
    }

    public async validateLevelsByPID(entityId: number, levelPIDs: string[]): Promise<LimitLevel[]> {
        const levelIds = levelPIDs.map(pid => decodeId(pid));
        for (const levelId of levelIds) {
            if (!Number.isFinite(levelId)) {
                throw new ValidationError(`Levels should be public id - ${levelPIDs}`);
            }
        }

        const entity = await EntityCache.findOne({ id: entityId });

        const levelInstances = await this.list({
            where: {
                entityId: {
                    [Op.in]: [...getParentIds(entity), entity.id]
                },
                id: { [Op.in]: levelIds }
            }
        });

        const availableLevelIds = levelInstances.map(item => item.id);
        const notFoundLevels = levelIds.filter(id => !availableLevelIds.includes(id));
        if (notFoundLevels.length) {
            throw new ValidationError(
                `Some levels not found - ${notFoundLevels.map(id => encodeId(id))}`);
        }

        return levelInstances.map(instance => instance.toInfo(true));
    }

    protected async performDestroy(instance: LimitLevelDBInstance,
                                   transaction: sequelize.Transaction): Promise<void> {
        const instances = await EntityGameLimitLevelModel
            .findAll({ where: { levelId: instance.id, isDefault: false, hidden: false } });
        
        if (instances.length) {
            throw new ValidationError("Level is used in game-limits");
        }
        
        return super.performDestroy(instance, transaction);
    }
}

export const limitLevelService = lazy(() => new LimitLevelService());
