import { Lazy, lazy } from "@skywind-group/sw-utils";
import { BetsLimits } from "../../models/gameLimitsConfiguration";
import { Currencies } from "@skywind-group/sw-currency-exchange";

export const maxMinorUnit: Lazy<number> = lazy(() => {
    let max: number = 0;
    Currencies.values().forEach((currency) => {
        const minorUnits = currency?.iso?.minorUnits;
        if (max < minorUnits) {
            max = minorUnits;
        }
    });
    return max;
});

export class LimitsCalculator {

    // Naming convention is broken for easy access by field name calculate${fieldName}
    public calculatestakeMin(stakeAll: number[], totalBetMultiplier: number, defaultTotalStake: number): number {
        return stakeAll[0];
    }

    public calculatestakeMax(stakeAll: number[], totalBetMultiplier: number, defaultTotalStake: number): number {
        return stakeAll[stakeAll.length - 1];
    }

    public calculatestakeDef(stakeAll: number[], totalBetMultiplier: number, defaultTotalStake: number): number {
        const expectedCoin: number = defaultTotalStake / totalBetMultiplier;
        let lessOrEqualCoin: number;
        let greaterCoin: number;
        for (const stake of stakeAll) {
            if (stake > expectedCoin) {
                greaterCoin = stake;
                break;
            }
            lessOrEqualCoin = stake;
        }
        if (!lessOrEqualCoin) {
            return greaterCoin;
        }
        if (!greaterCoin) {
            return lessOrEqualCoin;
        }
        if (greaterCoin - expectedCoin <= expectedCoin - lessOrEqualCoin) {
            return greaterCoin;
        }
        return lessOrEqualCoin;
    }

    public calculatemaxTotalStake(stakeAll: number[],
                                  totalBetMultiplier: number,
                                  defaultTotalStake: number): number {
        const stakeMax: number = this.calculatestakeMax(stakeAll, totalBetMultiplier, defaultTotalStake);
        const currencyMultiplier: number = Math.pow(10, maxMinorUnit.get());
        return Math.round(stakeMax * totalBetMultiplier * currencyMultiplier) / currencyMultiplier;
    }

    public supportedFields(): string[] {
        return this.supportedCalculatedFields.get();
    }

    protected supportedCalculatedFields: Lazy<string[]> = lazy(() => {
        const methodsPrefix: string = "calculate";
        let props = [];
        let obj = this;

        while (obj) {
            props = props.concat(Object.getOwnPropertyNames(obj));
            obj = Object.getPrototypeOf(obj);
        }

        return props
            .filter(p => typeof this[p] === "function" && p.startsWith(methodsPrefix))
            .map(p => p.substring(methodsPrefix.length));
    });
}

export class LiveLimitsCalculator extends LimitsCalculator {

    public calculateBetspositionMax(maxBet: number, concurrentPlayers: number): number {
        const value = maxBet * concurrentPlayers;
        const currencyMultiplier: number = Math.pow(10, maxMinorUnit.get());
        return Math.round(value * currencyMultiplier) / currencyMultiplier;
    }

    public calculateBetsexposure(maxBet: number, payout: number,
                                 concurrentPlayers: number): number {
        const positionMax = this.calculateBetspositionMax(maxBet, concurrentPlayers);
        const value = positionMax * (payout || 1);

        const currencyMultiplier: number = Math.pow(10, maxMinorUnit.get());
        return Math.round(value * currencyMultiplier) / currencyMultiplier;
    }

    public calculateexposure(betsLimits: BetsLimits, concurrentPlayers: number): number {
        const betTypes = Object.keys(betsLimits);
        let totalExposure = 0;

        for (const betType of betTypes) {
            const betTypeLimits = betsLimits[betType];
            totalExposure += this.calculateBetsexposure(betTypeLimits.max, betTypeLimits.payout, concurrentPlayers);
        }

        return totalExposure;
    }
}

export function getLimitsCalculator(levelsSupported: boolean) {
    if (levelsSupported) {
        return new LiveLimitsCalculator();
    }

    return new LimitsCalculator();
}
