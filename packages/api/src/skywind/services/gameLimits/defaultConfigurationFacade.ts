import {
    SchemaConfigurationDefaultValue,
    SchemaConfigurationImpl,
    SchemaConfigurationDefaultValuesForCurrency
} from "../../models/schemaConfiguration";
import { GameLimitsByCurrencyCode, GameLimitsConfigurationInStorage } from "../../models/gameLimitsConfiguration";
import { SchemaDefinitionInstance } from "../../models/schemaDefinition";
import { getSchemaConfigurationService } from "./schemaConfiguration";
import { GameLimitsStorage } from "./gameLimitsStorage";
import { ValidationError } from "../../errors";

export interface DefaultConfigurationFacade<T = SchemaConfigurationDefaultValue> {
    configuration: T;
    definition: SchemaDefinitionInstance;

    isLevelSupported: boolean;
    defaultLevels: string[];
    
    configurationId: number;
    createdAt: Date;
    updatedAt: Date;
}

export class SchemaConfigurationFacade implements DefaultConfigurationFacade {
    constructor(private schemaConfiguration: SchemaConfigurationImpl, 
                private schemaDefinition: SchemaDefinitionInstance,
                private gameCode: string) {
    }

    get configuration(): SchemaConfigurationDefaultValue {
        return this.schemaConfiguration?.configuration;
    }
    
    get definition(): SchemaDefinitionInstance {
        return this.schemaDefinition;
    }

    get defaultLevels(): string[] {
        return [];
    }

    get isLevelSupported(): boolean {
        return false;
    }
    
    get configurationId(): number {
        return this.schemaConfiguration && this.schemaConfiguration.id;
    }

    get createdAt(): Date {
        return this.schemaConfiguration.createdAt;
    }
    
    get updatedAt(): Date {
        return this.schemaConfiguration.updatedAt;
    }
}

export class MasterLimitsConfigurationFacade
    implements DefaultConfigurationFacade<SchemaConfigurationDefaultValuesForCurrency> {
    constructor(private masterLimits: GameLimitsConfigurationInStorage,
                private schemaDefinition: SchemaDefinitionInstance,
                private gameCode: string) {
    }

    get configuration(): SchemaConfigurationDefaultValuesForCurrency {
        return this.masterLimits?.gameLimits;
    }

    get definition(): SchemaDefinitionInstance {
        return this.schemaDefinition;
    }

    get defaultLevels(): string[] {
        return this.masterLimits?.levels;
    }
    
    get isLevelSupported(): boolean {
        return true;
    }

    public configurationId: number;
    public createdAt: Date;
    public updatedAt: Date;
}

interface ConfigurationOptions {
    masterGameLimits: GameLimitsByCurrencyCode;
    levels: string[];
    isMaster?: boolean;
}

export async function getConfigurationFacade(definition: SchemaDefinitionInstance,
                                             gameCode?: string,
                                             options?: ConfigurationOptions): Promise<DefaultConfigurationFacade> {
    if (definition.levelsSupported()) {
        const masterLimits = { gameLimits: options?.masterGameLimits, levels: options?.levels };

        const masterPath = ":";
        const storage = new GameLimitsStorage(masterPath, definition.id, gameCode);
        const configuration = await storage.searchAndMerge();

        if (!(configuration || options?.isMaster)) {
            throw new ValidationError("Master configuration is not defined.");
        }

        return new MasterLimitsConfigurationFacade(configuration ? configuration : masterLimits , definition, gameCode);
    }

    const schemaConfiguration = await getSchemaConfigurationService()
        .getByDefinition(definition.id);
    return new SchemaConfigurationFacade(schemaConfiguration, definition, gameCode);
}
