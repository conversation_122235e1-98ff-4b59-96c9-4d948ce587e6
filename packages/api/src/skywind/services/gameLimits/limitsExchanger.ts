import { ValidationError } from "../../errors";
import { CurrencyMultiplier } from "../../models/currencyMultiplier";
import { Currencies } from "@skywind-group/sw-currency-exchange";

/*
    Rounding Formula
    For values between 0.01 and 0.10 no need for rounding
    For values between 0.10 and 1 round to 0.10
    For values between 1 and 10 round to 1
    For values between 10 and 100 round to 10
    For values between 100 and 1,000 round to 50
    For values between 1,000 and 10,000 round to 500
    For values between 10,000 and 100,000 round to 5,000
    */

export abstract class BaseLimitsExchanger {
    public static multiplier = 10000;

    public convertCoinBets(stakeAllInEur: number[],
                           currencyCode: string,
                           isSmartRounding = true,
                           customToEURMultiplier?: number,
                           skipCurrencyMultiplierError?: boolean): number[] {
        const currencyMultiplier: number = customToEURMultiplier ||
            this.getCurrencyMultiplier(currencyCode, skipCurrencyMultiplierError);

        if (!currencyMultiplier) {
            if (skipCurrencyMultiplierError) {
                return stakeAllInEur;
            }
            throw new ValidationError("Currency not supported: currency multiplier is missing");
        }

        // no need to convert values, need only round it to avoid values like 0.9999999
        if (currencyMultiplier === 1) {
            return stakeAllInEur.map((bet) => this.getBet(bet));
        }

        let convertedStakes: number[] = stakeAllInEur
            .map(eachBet => this.getBet(eachBet, currencyMultiplier));

        if (isSmartRounding) {
            convertedStakes = convertedStakes.map(bet => this.roundCoinBet(bet));
        }

        return [...new Set(convertedStakes)];
    }

    public exchange(
        value: any,
        currency: string,
        isSmartRounding?: boolean,
        customToEURMultiplier?: number,
        skipCurrencyMultiplierError?: boolean
    ): any {
        if (typeof value === "object") {
            if (Array.isArray(value)) {
                return value.map(elem => this.exchange(
                        elem,
                        currency,
                        isSmartRounding,
                        customToEURMultiplier,
                        skipCurrencyMultiplierError
                    )
                );
            }

            const result = {};
            for (const propName in value) {
                if (value.hasOwnProperty(propName)) {
                    result[propName] = this.exchange(
                        value[propName],
                        currency,
                        isSmartRounding,
                        customToEURMultiplier,
                        skipCurrencyMultiplierError
                    );
                }
            }
            return result;
        } else if (typeof value !== "number") {
            return value;
        } else {
            const currencyMultiplier: number = customToEURMultiplier ||
                this.getCurrencyMultiplier(currency, skipCurrencyMultiplierError);
            const result = value * currencyMultiplier;
            if (isSmartRounding && currencyMultiplier !== 1) {
                return this.roundCoinBet(result);
            }
            return result;
        }
    }

    protected getBet(bet: number, currencyMultiplier: number = 1): number {
        return Math.round(bet * currencyMultiplier * BaseLimitsExchanger.multiplier) / BaseLimitsExchanger.multiplier;
    }

    public roundCoinBet(bet: number): number {
        if (bet <= 0.1) {
            return this.getBet(bet);
        }
        const rules: { rightBound: number, roundTo: number, currencyMultiplier?: number }[] = [
            { rightBound: 1, roundTo: 0.1, currencyMultiplier: 10 },
            { rightBound: 10, roundTo: 1 },
            { rightBound: 100, roundTo: 10 }
        ];
        for (const rule of rules) {
            if (bet < rule.rightBound) {
                const rounded = Math.round(bet / rule.roundTo + Number.EPSILON) * rule.roundTo;
                if (rule.currencyMultiplier) {
                    return Math.round(rounded * rule.currencyMultiplier) / rule.currencyMultiplier;
                }
                return rounded;
            }
        }
        return this.roundToHalfOfLeftBound(bet);
    }

    protected roundToHalfOfLeftBound(bet: number) {
        const powerOfTen = Math.floor(Math.log10(bet));
        const roundTo = Math.min(5000, Math.pow(10, powerOfTen) / 2);
        return Math.round(bet / roundTo) * roundTo;
    }

    protected abstract getCurrencyMultiplier(currencyCode: string, skipError?: boolean): number;
}

export class MarketplaceLimitsExchanger extends BaseLimitsExchanger {
    constructor(public customCurrencyMultiplier: CurrencyMultiplier) {
        super();
    }

    protected getCurrencyMultiplier(currency: string, skipError?: boolean): number {
        const customBaseCurrency: string = this.customCurrencyMultiplier.baseCurrency;
        if (currency === customBaseCurrency) {
            return 1;
        }
        const result = this.customCurrencyMultiplier.multipliers
            .find(({ currencyCode }) => currencyCode === currency);

        if (!result && !skipError) {
            throw new ValidationError(
                `Currency ${currency} is not configured properly: currency multiplier is missing`);
        }

        return result && result.currencyMultiplier;
    }
}

export class LimitsExchanger extends BaseLimitsExchanger {

    protected getCurrencyMultiplier(currencyCode: string): number {
        const result = Currencies.value(currencyCode)?.toEURMultiplier;
        if (!result) {
            throw new ValidationError(`Currency ${currencyCode} is not configured properly (toEURMultiplier is empty)`);
        }
        return result;
    }
}

const limitsHelper: LimitsExchanger = new LimitsExchanger();

export function getLimitsExchanger(customMultipliers?: CurrencyMultiplier) {
    if (customMultipliers) {
        return new MarketplaceLimitsExchanger(customMultipliers);
    }
    return limitsHelper;
}
