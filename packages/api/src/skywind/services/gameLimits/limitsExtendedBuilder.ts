import { BrandEntity } from "../../entities/brand";
import { SchemaDefinitionInstance } from "../../models/schemaDefinition";
import { EntityGame, Game } from "../../entities/game";
import { SchemaConfigurationDefaultValue } from "../../models/schemaConfiguration";
import { GameLimitsStorage } from "./gameLimitsStorage";
import {
    GameLimitsConfigurationInStorage,
    GameLimitsExtended,
    GameLimitsExtendedByCurrencyCode
} from "../../models/gameLimitsConfiguration";
import { Limits } from "../../entities/gamegroup";
import { getGameGroupService } from "../gamegroup";
import { OperationForbidden, ValidationError } from "../../errors";
import { getEntitySettings } from "../settings";
import { findOneEntityGame } from "../game";
import { getNewLimitsFacade } from "./limitsFacade";
import { getSchemaDefinitionService } from "./schemaDefinition";
import { limitLevelService } from "./limitLevels";
import { LimitLevel } from "../../models/limitLevels";
import { DefaultConfigurationFacade, getConfigurationFacade } from "./defaultConfigurationFacade";
import { getGameLimitLevelService } from "./entityLimitLevels";
import { EntityGameLimitLevel } from "../../models/entityLimitLevels";
import { LIMIT_CONFIGURATION_TYPE } from "../../entities/schemaDefinition";

class LimitsExtendedBuilder {
    private currencies: string[];
    private facade: DefaultConfigurationFacade;
    private gameLimitsConfig: GameLimitsConfigurationInStorage;
    private entityGameLimitsConfig: GameLimitsConfigurationInStorage;
    private levelCustomizations: EntityGameLimitLevel[];

    constructor(private entity: BrandEntity,
                private schemaDefinition: SchemaDefinitionInstance,
                private entityGame: EntityGame,
                private gameGroupId?: number,
                currency?: string) {
        this.currencies = currency ? [currency] : entity.getCurrencies();
    }

    private async setLimitsConfigurations() {
        this.facade = await getConfigurationFacade(this.schemaDefinition, this.entityGame.game.code);

        const game: Game = this.entityGame.game;

        const gameLimitsStorage: GameLimitsStorage = new GameLimitsStorage(this.entity.path,
            this.schemaDefinition.id,
            game.code,
            this.gameGroupId, null);

        const gameLimitsHierarchy: GameLimitsConfigurationInStorage[] = await gameLimitsStorage.search();
        this.gameLimitsConfig = gameLimitsStorage.merge(gameLimitsHierarchy);
        this.entityGameLimitsConfig = gameLimitsHierarchy && gameLimitsHierarchy
            .find(gameLimit => gameLimit.entityPath === this.entity.path);

        this.levelCustomizations = await getGameLimitLevelService(this.entity)
            .getGameLimitLevels({ gameCode: game.code });
    }

    public async find(): Promise<GameLimitsExtendedByCurrencyCode> {
        await this.setLimitsConfigurations();

        const limits: GameLimitsExtendedByCurrencyCode = {};

        for (const currency of this.currencies) {
            const currencyLimits = await this.findForCurrency(currency);
            if (currencyLimits && Object.keys(currencyLimits).length > 0) {
                limits[currency] = { ...currencyLimits };
            }
        }

        return limits;
    }

    public async findForCurrency(currency: string): Promise<GameLimitsExtended> {
        const result: GameLimitsExtended = {};

        const gameLimitsCurrenciesVersion = this.entityGame.game && this.entityGame.game.features &&
            this.entityGame.game.features.gameLimitsCurrenciesVersion || 1;
        const isSmartRoundingSupported = this.entityGame.game && this.entityGame.game.features &&
            this.entityGame.game.features.isSmartRoundingSupported;

        const limits = await getNewLimitsFacade(this.entity).build(
            this.facade,
            this.entityGame.game.totalBetMultiplier,
            currency,
            this.gameLimitsConfig,
            undefined,
            undefined,
            this.levelCustomizations,
            gameLimitsCurrenciesVersion,
            isSmartRoundingSupported,
        );

        const propertyType = new PropertyType(this.facade,
            this.gameLimitsConfig,
            this.entityGameLimitsConfig,
            limits,
            currency);

        const levelPIDs = this.schemaDefinition.levelsSupported() && this.levels;

        if (levelPIDs && levelPIDs.length) {
            const levels = await limitLevelService.get().validateLevelsByPID(this.entity.id, levelPIDs);
            for (const level of levels) {
                propertyType.level = level;
                result[level.title] = this.propertiesExtendedInfo(limits[level.title], propertyType) as any;
            }
        } else {
            return this.propertiesExtendedInfo(limits, propertyType);
        }

        return result;
    }

    private propertiesExtendedInfo(limits: Limits, propertyType: PropertyType): GameLimitsExtended {
        const result: GameLimitsExtended = {};

        for (const property in limits) {
            if (!limits.hasOwnProperty(property)) {
                continue;
            }
            propertyType.pushProperty(property);
            const value = propertyType.value();

            if (this.isObject(value)) {
                result[property] = this.propertiesExtendedInfo(value as Limits, propertyType) as any;
            } else {
                result[property] = this.propertyExtendedInfo(propertyType, value);
            }
            propertyType.popProperty();
        }
        return result;
    }

    private isObject(value) {
        return typeof value === "object" && !Array.isArray(value);
    }

    private propertyExtendedInfo(propertyType: PropertyType, value: number | number[]) {
        return {
            custom: propertyType.isCustom(),
            inherited: propertyType.isInherited(),
            calculated: propertyType.isCalculated(),
            calculatedFromBase: propertyType.isCalculatedFromBaseCurrency(),
            value
        };
    }

    private get levels() {
        return this.gameLimitsConfig?.levels && this.gameLimitsConfig.levels.length
               ? this.gameLimitsConfig.levels
               : this.facade.defaultLevels;
    }
}

class PropertyType {
    private currencyMissingInSchema: boolean;
    private properties: string[] = [];
    public level: LimitLevel;

    constructor(private facade: DefaultConfigurationFacade,
                private gameLimitsConfig: GameLimitsConfigurationInStorage,
                private entityGameLimitsConfig: GameLimitsConfigurationInStorage,
                private builtLimits: Limits,
                private currency: string) {
        const schemaLimits = facade.configuration?.[currency];
        this.currencyMissingInSchema = !schemaLimits || !!(schemaLimits as SchemaConfigurationDefaultValue)?.aligned;
    }

    public pushProperty(property: string) {
        this.properties.push(property);
        return this;
    }

    public popProperty() {
        this.properties.pop();
        return this;
    }

    public isCustom(): boolean {
        return !!this.safeGet(this.getCurrencyLimits(this.entityGameLimitsConfig));
    }

    public isInherited(): boolean {
        return !this.isCustom() && this.existsInGameLimitsConfig();
    }

    private existsInGameLimitsConfig() {
        return !!this.safeGet(this.getCurrencyLimits(this.gameLimitsConfig));
    }

    public isCalculated(): boolean {
        return this.getLimitType() === LIMIT_CONFIGURATION_TYPE.CALCULATED;
    }

    private getLimitType(): LIMIT_CONFIGURATION_TYPE {
        if (this.properties.length > 1 && this.properties.includes("bets")) {
            const [, betType, prop] = this.properties;
            const betTypeSchema = this.facade.definition.getBetTypeSchema(betType);

            return betTypeSchema?.properties?.[prop]?.limitConfigurationType;
        } else {
            return this.facade.definition.schema?.properties?.[this.properties[0]]?.limitConfigurationType;
        }
    }

    public isCalculatedFromBaseCurrency(): boolean {
        const custom = this.isCustom();

        return !custom &&
            !this.existsInGameLimitsConfig() &&
            this.currencyMissingInSchema &&
            this.getLimitType() !== LIMIT_CONFIGURATION_TYPE.CONST;
    }

    public value(): number | number[] {
        const levelLimits = this.level
                            ? (this.builtLimits as any)?.[this.level.title]
                            : this.builtLimits;
        return this.safeGet(levelLimits);
    }

    private getCurrencyLimits(limits: any) {
        return this.level
               ? limits?.gameLimits?.[this.currency]?.[this.level.pid]
               : limits?.gameLimits?.[this.currency];
    }

    private safeGet(object: any): number | number[] {
        if (!this.properties || !this.properties.length) {
            throw new ValidationError("Properties are required");
        }
        let value = { ...object };
        for (const property of this.properties) {
            value = value?.[property];
        }
        return value;
    }
}

export async function buildExtendedPlayerLimitsBySchema(entity: BrandEntity,
                                                        gameCode?: string,
                                                        gameGroupName?: string,
                                                        currency?: string)
    : Promise<GameLimitsExtendedByCurrencyCode> {

    if (!gameCode) {
        throw new ValidationError("Game is required");
    }

    const entityGame = await findOneEntityGame(entity, gameCode);
    if (!entityGame.game.schemaDefinitionId) {
        throw new ValidationError("The game doesn't support new limits");
    }

    const schemaDefinition = await getSchemaDefinitionService()
        .retrieve(entityGame.game.schemaDefinitionId);
    const entitySettings = await getEntitySettings(entity.path);

    if (entityGame?.game?.features?.isMarketplaceSupported && entitySettings.isMarketplaceSupported) {
        throw new OperationForbidden("Method is not available for marketplace limits");
    }

    let gameGroupId: number;
    if (gameGroupName) {
        const gameGroup = await getGameGroupService()
            .findOne(entity, { name: gameGroupName }, true);
        gameGroupId = gameGroup.get("id");
    }

    const builder = new LimitsExtendedBuilder(entity, schemaDefinition, entityGame, gameGroupId, currency);
    return await builder.find();
}
