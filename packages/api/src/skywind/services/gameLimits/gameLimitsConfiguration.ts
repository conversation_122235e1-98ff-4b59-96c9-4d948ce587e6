import {
    CreateGameLimitsConfiguration,
    GameLimits,
    GameLimitsConfiguration,
    GameLimitsConfigurationDB,
    GameLimitsConfigurationDBInstance,
    GameLimitsConfigurationDetailedInfo,
    GameLimitsConfigurationInStorage,
    GameLimitsConfigurationModel,
    SetGameLimitsConfigurationStatus,
    UpdateGameLimitsConfiguration
} from "../../models/gameLimitsConfiguration";
import {
    GameLimitsConfigurationAlreadyExists,
    GameNotFoundError,
    InvalidConfigurationStatus,
    OperationForbidden,
    ResourceNotFoundError,
    ValidationError
} from "../../errors";
import { BaseEntity } from "../../entities/entity";
import { EntityGame } from "../../entities/game";
import { getSchemaDefinitionService } from "./schemaDefinition";
import { SchemaDefinitionInstance } from "../../models/schemaDefinition";
import { FindOptions, IncludeOptions, literal, Op, Transaction, UniqueConstraintError, WhereOptions } from "sequelize";
import { findOneEntityGame } from "../game";
import * as FilterService from "../filter";
import { PagingHelper } from "../../utils/paginghelper";
import { sequelize } from "../../storage/db";
import { getGameGroupService } from "../gamegroup";
import { getSegmentCRUDService } from "./segment";
import { Segment } from "../../models/segment";
import EntityCache from "../../cache/entity";
import { BrandEntity } from "../../entities/brand";
import { DEFAULT_CURRENCY } from "../../models/currencyMultiplier";
import { GameLimitsConfigurationValidatorService } from "./gameLimitsConfigurationValidator";
import { GameLimitsStorage } from "./gameLimitsStorage";
import { LIMITS_CONFIGURATION_STATUS, LIMITS_CONFIGURATION_STATUS_SET } from "./helper";
import { getGameLimitLevelService } from "./entityLimitLevels";
import { DefaultConfigurationFacade, getConfigurationFacade } from "./defaultConfigurationFacade";
import { Models } from "../../models/models";
import { decodeId, encodeId } from "../../utils/publicid";
import { GameLimitsPermissionType, SchemaPermissions } from "../../entities/schemaDefinition";

const SegmentModel = Models.SegmentModel;
const GameLimitsConfigurationModel = Models.GameLimitsConfigurationModel;

export const queryParamsKeys = [
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
    "gameCode",
    "schemaDefinitionId",
    "gameGroupId"
];

const sortableKeys = [
    "createdAt",
    "game_code"
];

const DEFAULT_OFFSET = 0;
const DEFAULT_LIMIT = 20;
const DEFAULT_SORT_KEY = "createdAt";

export const getGameLimitsConfigurationService = (keyEntity?: BaseEntity,
                                                  isMaster = false): GameLimitsConfigurationService => {
    return new GameLimitsConfigurationService(keyEntity, isMaster);
};

export class GameLimitsConfigurationService {

    private readonly model = GameLimitsConfigurationModel;
    private readonly schemaDefinition = getSchemaDefinitionService();
    private readonly validationService: GameLimitsConfigurationValidatorService;

    constructor(public keyEntity: BaseEntity, private isMaster = false) {
        this.validationService = new GameLimitsConfigurationValidatorService(this.keyEntity, isMaster);
    }

    public getModel(): GameLimitsConfigurationModel {
        return this.model;
    }

    public async create(entity: BaseEntity,
                        data: CreateGameLimitsConfiguration,
                        isMarketplaceRequest: boolean = false,
                        transaction?: Transaction):
        Promise<GameLimitsConfigurationDetailedInfo> {

        this.validateExternalRequest(data, isMarketplaceRequest);
        this.validationService.validateEntity(entity);
        this.validationService.validateCreation(data);

        const dataToCreate = await this.getCreateData(entity, data);

        if (!transaction) {
            const configuration = await sequelize.transaction(async (transaction: Transaction) => {
                return await this.performCreate(
                    entity, dataToCreate, transaction, data.segment, isMarketplaceRequest);
            });

            return this.retrieve(entity, configuration.get("id"), isMarketplaceRequest);
        } else {
            const configurationDB = await this.performCreate(
                entity, dataToCreate, transaction, data.segment, isMarketplaceRequest);

            return configurationDB.toDetailedInfo(undefined);
        }
    }

    public async update(entity: BaseEntity,
                        id: number,
                        data: UpdateGameLimitsConfiguration,
                        isMarketplaceRequest: boolean = false):
        Promise<GameLimitsConfigurationDetailedInfo> {

        this.validateExternalRequest(data, isMarketplaceRequest);
        this.validationService.validateEntity(entity);

        const gameConfigurationDB = await this.getInstance(id, {
            where: { entityId: entity.id },
            include: [{ association: this.getModel().associations.gameGroup }]
        });

        this.validationService.validateUpdate(data);

        const gameLimitsInfo = await this.retrieve(entity, id);
        const schemaDefinition = await this.schemaDefinition.retrieve(gameLimitsInfo.schemaDefinitionId);

        const facade = await getConfigurationFacade(schemaDefinition, gameLimitsInfo.gameCode);

        await this.validationService.validate(entity, facade, data, gameLimitsInfo);

        this.prepareUpdateData(facade, data, gameLimitsInfo);

        return this.performUpdate(entity, gameConfigurationDB, data);
    }

    public async setStatus(id: number,
                           request: SetGameLimitsConfigurationStatus,
                           transaction?: Transaction): Promise<GameLimitsConfigurationDetailedInfo> {
        if (!LIMITS_CONFIGURATION_STATUS_SET.has(request.status)) {
            return Promise.reject(new InvalidConfigurationStatus());
        }

        const configurationDBItem = await this.getInstance(id);
        const configurationInfo = configurationDBItem.toInfo();

        if (configurationInfo.gameCode !== request.gameCode) {
            return Promise.reject(new GameNotFoundError(request.gameCode));
        }

        const entity: BaseEntity = await EntityCache.findOne<BrandEntity>({ id: configurationInfo.entityId });
        this.validationService.validateEntity(entity);

        const gameLimitsStorage = new GameLimitsStorage(
            entity.path,
            configurationInfo.schemaDefinitionId,
            configurationInfo.gameCode,
            configurationInfo.gameGroupId,
            configurationInfo.segmentId);

        const limits = await gameLimitsStorage.retrieve();
        const configurationDetailedInfo = configurationDBItem.toDetailedInfo(limits);

        // TODO: decide what to do if broken configuration
        if (configurationDetailedInfo.status === LIMITS_CONFIGURATION_STATUS.BROKEN) {
            await gameLimitsStorage.delete();
        } else if (configurationDetailedInfo.status === LIMITS_CONFIGURATION_STATUS.ARCHIVED) {
            return Promise.reject(new InvalidConfigurationStatus());
        } else {
            await gameLimitsStorage.setProcessingFlag(true);
            if (configurationDBItem.segmentId) {
                await getSegmentCRUDService().update(configurationDBItem.segmentId, {
                    status: request.status
                }, transaction);
            }
            await gameLimitsStorage.setStatus(request.status, false);
        }

        return this.retrieve(entity, id, true);
    }

    public async retrieve(entity: BaseEntity,
                          id: number,
                          isMarketplaceRequest?: boolean): Promise<GameLimitsConfigurationDetailedInfo> {

        const options: FindOptions<any> = { where: { entityId: entity.id } };
        if (isMarketplaceRequest) {
            options.include = [{ association: this.getModel().associations.segment }];
        } else {
            options.include = [{ association: this.getModel().associations.gameGroup }];
        }

        const configurationDBItem = await this.getInstance(id, options);
        const configuration = configurationDBItem.toInfo();

        const gameLimitsStorage = new GameLimitsStorage(entity.path,
            configuration.schemaDefinitionId,
            configuration.gameCode,
            configuration.gameGroupId,
            configuration.segmentId);

        const limits = await gameLimitsStorage.retrieve();

        return configurationDBItem.toDetailedInfo(limits);
    }

    public async destroy(entity: BaseEntity,
                         id: number): Promise<void> {

        const instance = await this.getInstance(id, { where: { entityId: entity.id } });

        await sequelize.transaction(async (transaction: Transaction) => {
            const gameLimitsStorage = new GameLimitsStorage(
                entity.path,
                instance.schemaDefinitionId,
                instance.gameCode,
                instance.gameGroupId,
                instance.segmentId);

            await gameLimitsStorage.delete();

            await instance.destroy({ transaction });

            if (instance.segmentId) {
                await getSegmentCRUDService().destroy(instance.segmentId, transaction);
            }

            if (instance.gameCode) {
                await getGameLimitLevelService(entity).destroyByGameCodes([instance.gameCode], transaction);
            } else {
                await getGameLimitLevelService(entity)
                    .destroyBySchemaDefinitionId(instance.schemaDefinitionId, transaction);
            }
        });
    }

    public async destroyAll(entity: BaseEntity,
                            entityGames: EntityGame[],
                            transaction: Transaction): Promise<void> {

        const entityGameIds = entityGames.map(i => i.id);
        const instances = await this.getInstances({ where: { entityGameId: { [Op.in]: entityGameIds } } });
        const gameLimitsConfigurationIds = [];

        // removal from redis
        await Promise.all(instances.map((instance) => {
            gameLimitsConfigurationIds.push(instance.id);

            const gameLimitsStorage = new GameLimitsStorage(
                entity.path,
                instance.schemaDefinitionId,
                instance.gameCode,
                instance.gameGroupId,
                instance.segmentId);

            return gameLimitsStorage.delete();
        }));
        await this.getModel()
            .destroy({ where: { id: { [Op.in]: gameLimitsConfigurationIds } }, transaction });
        await SegmentModel.destroy({ where: { entityGameId: { [Op.in]: entityGameIds } }, transaction });
        await getGameLimitLevelService(entity).destroyByGameCodes(entityGames.map(i => i.game.code), transaction);
    }

    public async list(entity: BaseEntity,
                      options: WhereOptions<any> = {},
                      includeSegments: boolean = false,
                      returnAll: boolean = false): Promise<GameLimitsConfigurationDetailedInfo[]> {

        // TODO: to backwards compatibility
        if (options?.["sortBy"] === "created_at") {
            options["sortBy"] = "createdAt";
        }
        const sortBy = FilterService.getSortKey(options, sortableKeys, DEFAULT_SORT_KEY);
        const sortOrder = FilterService.valueFromQuery(options, "sortOrder") || "ASC";
        const offset = FilterService.valueFromQuery(options, "offset") as number || DEFAULT_OFFSET;
        const limit = FilterService.valueFromQuery(options, "limit") || DEFAULT_LIMIT;

        options["entityId"] = entity.id;

        const model = this.getModel();
        const include: IncludeOptions[] = [{ association: model.associations.gameGroup }];
        if (includeSegments) {
            include.push({ association: model.associations.segment });
            if (!options["segmentId"]) {
                options["segmentId"] = { [Op.ne]: null };
            }
        }

        const findOptions: FindOptions<any> = {
            where: options,
            order: literal(`"${sortBy}" ${sortOrder}`),
            offset,
            include
        };

        if (!returnAll) {
            findOptions.limit = limit;
        }

        return PagingHelper.findAsyncAndCountAll(
            model,
            findOptions,
            async (configuration: GameLimitsConfigurationDBInstance) => {
                const gameLimitsStorage = new GameLimitsStorage(
                    entity.path,
                    configuration.schemaDefinitionId,
                    configuration.gameCode,
                    configuration.gameGroupId,
                    configuration.segmentId);

                const limits = await gameLimitsStorage.retrieve();
                return configuration.toDetailedInfo(limits);
            }
        );
    }

    public async performSetStatus(gameLimitsStorage: GameLimitsStorage,
                                  segmentId: number,
                                  status: LIMITS_CONFIGURATION_STATUS,
                                  transaction?: Transaction) {
        await gameLimitsStorage.setProcessingFlag(true);
        if (segmentId) {
            await getSegmentCRUDService().update(segmentId, {
                status: status
            }, transaction);
        }
        await gameLimitsStorage.setStatus(status, false);
    }

    public async findOneGameAndSchema(entity: BaseEntity,
                                      gameCode?: string,
                                      schemaDefinitionId?: number): Promise<[EntityGame, SchemaDefinitionInstance]> {

        let gameInfo: EntityGame;
        let schemaDefinition: SchemaDefinitionInstance;

        if (gameCode) {
            gameInfo = await findOneEntityGame(entity, gameCode);
            if (!gameInfo.game.schemaDefinitionId) {
                throw new ValidationError("The game doesn't support new limits");
            }
        }

        if (schemaDefinitionId || gameInfo) {
            const schemaId = schemaDefinitionId || gameInfo.game.schemaDefinitionId;
            schemaDefinition = await this.schemaDefinition.retrieve(schemaId);
        }

        if (gameInfo && schemaDefinition && gameInfo.game && gameInfo.game.schemaDefinitionId !== schemaDefinition.id) {
            throw new ValidationError(
                `Game ${gameCode} and schema definition ${schemaDefinition.name} are not compatible`
            );
        }

        return [gameInfo, schemaDefinition];
    }

    private async getCreateData(entity: BaseEntity,
                                data: CreateGameLimitsConfiguration): Promise<GameLimitsConfiguration> {

        const dataToCreate: GameLimitsConfiguration = {
            entityId: entity.id,
            gameLimits: data.gameLimits,
            filters: data.filters,
            title: data.title,
            description: data.description,
            externalBrandId: data.externalBrandId,
            status: data.status
        };

        const [gameInfo, schemaDefinition] = await this.findOneGameAndSchema(
            entity, data.gameCode, data.schemaDefinitionId);

        if (gameInfo) {
            dataToCreate.entityGameId = gameInfo.id;
            dataToCreate.gameCode = data.gameCode;
        }

        dataToCreate.schemaDefinitionId = data.schemaDefinitionId || gameInfo.game.schemaDefinitionId;

        if (data.gameGroupName) {
            const gameGroup = await getGameGroupService()
                .findOne(entity, { name: data.gameGroupName }, true);

            dataToCreate.gameGroupId = gameGroup.get("id");
        }

        const facade = await getConfigurationFacade(schemaDefinition, data.gameCode, {
            masterGameLimits: data.gameLimits,
            levels: data.levels,
            isMaster: entity.isMaster()
        });

        if (facade.configurationId) {
            dataToCreate.schemaConfigurationId = facade.configuration.id;
        }

        if (facade.isLevelSupported && data.levels) {
            dataToCreate.levels = data.levels;

            if (data.defaultLevel) {
                dataToCreate.defaultLevel = data.defaultLevel;
            }
        }

        await this.validationService.validate(entity, facade, dataToCreate);

        return dataToCreate;
    }

    private prepareUpdateData(facade: DefaultConfigurationFacade,
                              gameLimitsConfig: UpdateGameLimitsConfiguration,
                              existingGameLimitsConfig: GameLimitsConfiguration) {
        const levels: string[] = gameLimitsConfig?.levels ||
            existingGameLimitsConfig?.levels ||
            facade.defaultLevels;

        for (const currency of Object.keys(gameLimitsConfig.gameLimits || [])) {
            if (facade.isLevelSupported) {
                for (const level of levels) {
                    this.setAdminValuesForUpdate(
                        facade.definition.permissions,
                        currency,
                        gameLimitsConfig.gameLimits[currency][level],
                        (existingGameLimitsConfig?.gameLimits as any)?.[currency]?.[level]
                    );
                }

            } else {
                this.setAdminValuesForUpdate(
                    facade.definition.permissions,
                    currency,
                    gameLimitsConfig.gameLimits[currency],
                    (existingGameLimitsConfig?.gameLimits as any)?.[currency]
                );
            }
        }
    }

    private setAdminValuesForUpdate(schemaPermissions: SchemaPermissions,
                                    currency: string,
                                    gameLimits: GameLimits = {},
                                    existingLimits: GameLimits = {}) {
        const isMasterEntity = this.keyEntity.isMaster();
        const properties = new Set([...Object.keys(existingLimits), ...Object.keys(gameLimits)]);

        for (const gameLimitsProperty of properties.values()) {

            const cleanedCurrency = currency === DEFAULT_CURRENCY ? "default" : currency;
            const propertyPermission = schemaPermissions?.[cleanedCurrency]?.[gameLimitsProperty] ||
                schemaPermissions?.default?.[gameLimitsProperty];
            const noPermission = !isMasterEntity && propertyPermission !== GameLimitsPermissionType.ENTITY;

            const existingValue = (existingLimits as any)?.[gameLimitsProperty];
            const newValue = gameLimits[gameLimitsProperty];
            if (typeof newValue === "undefined" && existingLimits && !isMasterEntity) {
                gameLimits[gameLimitsProperty] = existingValue;
                continue;
            }

            const valueChanged = existingValue ? existingValue !== newValue : true;
            if (noPermission && valueChanged) {
                throw new OperationForbidden(`Not allowed to modify ${gameLimitsProperty} for currency ` +
                    cleanedCurrency);
            }
        }
    }

    private async performCreate(entity: BaseEntity,
                                dataToCreate: GameLimitsConfiguration,
                                transaction: Transaction,
                                segmentToCreate?: Segment,
                                isMarketplaceRequest: boolean = false): Promise<GameLimitsConfigurationDBInstance> {

        await this.createLevelsRelation(entity, {
            gameCode: dataToCreate.gameCode,
            gameLimits: dataToCreate.gameLimits,
            schemaDefinitionId: dataToCreate.schemaDefinitionId
        }, transaction);

        if (segmentToCreate && isMarketplaceRequest) {
            segmentToCreate.entityGameId = dataToCreate.entityGameId;
            segmentToCreate.status = dataToCreate.status;
            const segment = (await getSegmentCRUDService().create(segmentToCreate, transaction)).toInfo();
            dataToCreate.segmentId = segment.id;
        }

        try {
            dataToCreate.status = dataToCreate.status || LIMITS_CONFIGURATION_STATUS.ACTIVE;
            const dbData: GameLimitsConfigurationDB = {
                schemaDefinitionId: dataToCreate.schemaDefinitionId,
                schemaConfigurationId: dataToCreate.schemaConfigurationId,
                entityId: dataToCreate.entityId,
                entityGameId: dataToCreate.entityGameId,
                gameCode: dataToCreate.gameCode,
                gameGroupId: dataToCreate.gameGroupId,
                segmentId: dataToCreate.segmentId,
                title: dataToCreate.title,
                description: dataToCreate.description,
                externalBrandId: dataToCreate.externalBrandId,
                status: dataToCreate.status
            };

            const configurationDB = await this.getModel().create(dbData, { transaction });

            const gameLimitsStorage = new GameLimitsStorage(
                entity.path,
                dataToCreate.schemaDefinitionId,
                dataToCreate.gameCode,
                dataToCreate.gameGroupId,
                dataToCreate.segmentId);

            await gameLimitsStorage.save(dataToCreate as GameLimitsConfigurationInStorage);
            return configurationDB;
        } catch (err) {
            if (err instanceof UniqueConstraintError) {
                return Promise.reject(new GameLimitsConfigurationAlreadyExists());
            }

            return Promise.reject(err);
        }
    }

    private async createLevelsRelation(entity: BaseEntity,
                                       data: {
                                           gameLimits: any,
                                           gameCode: string,
                                           schemaDefinitionId: number
                                       },
                                       transaction: Transaction): Promise<void> {
        const schemaDefinition = await this.schemaDefinition.retrieve(data.schemaDefinitionId);
        if (schemaDefinition.levelsSupported() && data.gameLimits) {
            const service = getGameLimitLevelService(entity, true);
            if (data.gameCode) {
                await service.destroyByGameCodes([data.gameCode], transaction);
            } else {
                await service.destroyBySchemaDefinitionId(schemaDefinition.id, transaction);
            }

            const levelPIDs = [];

            for (const levelLimits of Object.values(data.gameLimits)) {
                levelPIDs.push(...Object.keys(levelLimits));
            }

            for (const pid of [...new Set(levelPIDs.filter(levelPid => levelPid !== "aligned"))]) {
                await service.create({
                    gameCode: data.gameCode,
                    schemaConfigurationId: schemaDefinition.id,
                    levelId: decodeId(pid)
                }, transaction);
            }
        }
    }

    private validateExternalRequest(data: UpdateGameLimitsConfiguration,
                                    isMarketplaceRequest: boolean) {
        if (!isMarketplaceRequest) {
            const propertiesAllowedForMP = ["segment", "externalBrandId", "status", "baseCurrency"];

            for (const property of propertiesAllowedForMP) {
                if (property in data) {
                    throw new OperationForbidden(`${property} is not allowed`);
                }
            }
        }
    }

    private async performUpdate(entity: BaseEntity,
                                gameConfigurationDB: GameLimitsConfigurationDBInstance,
                                data: UpdateGameLimitsConfiguration): Promise<GameLimitsConfigurationDetailedInfo> {

        await sequelize.transaction(async (transaction: Transaction) => {
            const gameConfiguration = gameConfigurationDB.toInfo();
            const gameLimitsStorage = new GameLimitsStorage(
                entity.path,
                gameConfiguration.schemaDefinitionId,
                gameConfiguration.gameCode,
                gameConfiguration.gameGroupId,
                gameConfiguration.segmentId);

            await gameLimitsStorage.setProcessingFlag(true);

            await this.createLevelsRelation(entity,
                {
                    gameLimits: data.gameLimits,
                    gameCode: gameConfiguration.gameCode,
                    schemaDefinitionId: gameConfiguration.schemaDefinitionId
                },
                transaction);

            if (data.title || data.description) {
                const dbUpdateData: Partial<UpdateGameLimitsConfiguration> = {};
                if (data.title) {
                    dbUpdateData.title = data.title;
                }
                if (data.description) {
                    dbUpdateData.description = data.description;
                }

                await gameConfigurationDB.update(dbUpdateData, {
                    where: { id: gameConfiguration.id, entityId: entity.id },
                    transaction
                });
            }

            const nullableFields: (keyof UpdateGameLimitsConfiguration)[] = ["gameLimits", "filters", "levels"];
            // processing flag is set to false in save method
            if (nullableFields.some(field => field in data) || data.defaultLevel) {
                await gameLimitsStorage.save(data);
            } else {
                await gameLimitsStorage.setProcessingFlag(false);
            }
        });

        return this.retrieve(entity, gameConfigurationDB.id);
    }

    private async getInstance(id?: number,
                              options: FindOptions<any> = {},
                              raiseErrorIfNotFound: boolean = true): Promise<GameLimitsConfigurationDBInstance> {

        const instance = await this.getModel().findOne({
            ...options,
            where: { ...options.where, id }
        });
        if (raiseErrorIfNotFound && !instance) {
            return Promise.reject(
                new ResourceNotFoundError(`Object not found by ID (${encodeId(id)})`)
            );
        }

        return instance;
    }

    private async getInstances(options: FindOptions<any> = {},
                               raiseErrorIfNotFound: boolean = false): Promise<GameLimitsConfigurationDBInstance[]> {

        const instances = await this.getModel().findAll({
            ...options,
            where: { ...options.where }
        });
        if (raiseErrorIfNotFound && !instances.length) {
            return Promise.reject(new ResourceNotFoundError(`Object not found by search (${options})`));
        }

        return instances;
    }

}
