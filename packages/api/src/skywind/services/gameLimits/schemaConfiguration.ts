import { CRUDServiceImpl } from "../crudService";
import { lazy } from "@skywind-group/sw-utils";
import * as Errors from "../../errors";
import { ValidationError } from "../../errors";
import { Transaction, UniqueConstraintError } from "sequelize";
import {
    SchemaConfiguration,
    SchemaConfigurationImpl,
    SchemaConfigurationDefaultValue,
    SchemaConfigurationModel
} from "../../models/schemaConfiguration";
import { getSchemaDefinitionService, SchemaValidation } from "./schemaDefinition";
import {
    SchemaDefinitionInstance
} from "../../models/schemaDefinition";
import { RoomGameLimits } from "../../models/gameLimitsConfiguration";
import { Cache } from "../../cache/cache";
import { sequelize as db } from "../../storage/db";
import { BaseLimitsValidator } from "./helper";
import { DEFAULT_CURRENCY } from "../../models/currencyMultiplier";
import EntityCache from "../../cache/entity";
import { limitLevelService } from "./limitLevels";
import { Models } from "../../models/models";
import { encodeId } from "../../utils/publicid";
import { LIMIT_CONFIGURATION_TYPE, PropertySchema, SchemaDefinition } from "../../entities/schemaDefinition";

const schemaConfigurationService = lazy(() => new SchemaConfigurationService());
const definitionService = getSchemaDefinitionService();

export const getSchemaConfigurationService = (): SchemaConfigurationService => {
    return schemaConfigurationService.get();
};

const validator: SchemaValidation = new SchemaValidation();

const GameLimitsConfigurationModel = Models.GameLimitsConfigurationModel;
const SchemaConfigurationModel = Models.SchemaConfigurationModel;

class SchemaConfigurationService
    extends CRUDServiceImpl<SchemaConfigurationImpl, SchemaConfiguration, SchemaConfigurationModel> {

    private readonly commonValidator = new BaseLimitsValidator();
    private readonly levelService = limitLevelService.get();

    private cache: Cache<string, any> = new Cache("schema-configurations",
        async (key: string, definitionId: number, entityId: number, raiseError: boolean) => {
            const config = await this.model.findOne({
                where: {
                    schemaDefinitionId: definitionId,
                    entityId
                }
            });
            if (!config && raiseError) {
                throw new ValidationError(`Configuration not found for definition ${encodeId(definitionId)}`);
            }
            return config;
        });

    constructor(private model = SchemaConfigurationModel) {
        super();
    }

    public getModel(): SchemaConfigurationModel {
        return this.model;
    }

    public async create(data: SchemaConfiguration): Promise<SchemaConfigurationImpl> {
        const definition = await definitionService.retrieve(data.schemaDefinitionId);
        await this.validateByDefinition(definition, data);
        try {
            return await super.create(data);
        } catch (err) {
            if (err instanceof UniqueConstraintError) {
                return Promise.reject(new Errors.SchemaConfigurationAlreadyExists(definition.name, data.entityId));
            }
            return Promise.reject(err);
        }
    }

    public async getByDefinition(definitionId: number,
                                 entityId?: number,
                                 raiseError: boolean = false): Promise<SchemaConfigurationImpl> {
        if (!entityId) {
            const master = await EntityCache.findOne({ path: ":" });
            entityId = master.id;
        }
        const key = this.createCacheKey(entityId, definitionId);
        return this.cache.find(key, definitionId, entityId, raiseError);
    }

    public async update(id: number, data: Partial<SchemaConfiguration>): Promise<SchemaConfigurationImpl> {

        await this.checkGameLimitsConfiguration(id);

        const instance: SchemaConfiguration = await this.getInstance(id);
        if (data.configuration) {
            instance.configuration = data.configuration;
            const definition = await definitionService.retrieve(instance.schemaDefinitionId);
            await this.validateByDefinition(definition, instance);
        }

        const result = await super.update(id, data);
        this.cache.reset(this.createCacheKey(instance.entityId, instance.schemaDefinitionId));
        return result;
    }

    public async destroy(id: number): Promise<void> {
        await this.checkGameLimitsConfiguration(id);
        const instance = await this.getInstance(id);

        await db.transaction(async (t: Transaction) => {
            return this.performDestroy(instance, t);
        });
        this.cache.reset(this.createCacheKey(instance.entityId, instance.schemaDefinitionId));
    }

    public resetCache() {
        this.cache.reset();
    }

    protected validateUpdateData(data: Partial<SchemaConfiguration>): Partial<SchemaConfiguration> {
        const result: Partial<SchemaConfiguration> = {};
        if (data.name) {
            result.name = data.name;
        }
        if (data.configuration) {
            result.configuration = data.configuration;
        }
        return result;
    }

    private async validateByDefinition(definition: SchemaDefinitionInstance,
                                       configuration: SchemaConfiguration): Promise<void> {
        if (!configuration.configuration[DEFAULT_CURRENCY]) {
            throw new ValidationError("Missing data for default currency");
        }
        if (configuration.configuration[DEFAULT_CURRENCY].aligned !== undefined) {
            throw new ValidationError("Aligned flag is not supported for default currency");
        }

        if (definition.levelsSupported()) {
            throw new ValidationError("Schema configuration is forbidden for live definition");
        }

        for (const currency of Object.keys(configuration.configuration)) {
            const isDefaultCurrency: boolean = currency === DEFAULT_CURRENCY;
            const currencyConfig: any = configuration.configuration[currency];
            if (definition.levelsSupported()) {
                const levelPIDs = Object.keys(currencyConfig).filter(key => key !== "aligned");
                await this.levelService.validateLevelsByPID(configuration.entityId, levelPIDs);

                for (const level of levelPIDs) {
                    this.validateFields(currencyConfig[level], definition, isDefaultCurrency);
                }
            } else {
                this.validateFields(currencyConfig, definition, isDefaultCurrency);
                if (!isDefaultCurrency && typeof currencyConfig.aligned !== "boolean") {
                    throw new ValidationError(`Aligned flag is missing for ${currency}`);
                }
            }
        }
    }

    public validateFields(config: SchemaConfigurationDefaultValue, definition: SchemaDefinitionInstance,
                          defaultCurrency: boolean,
                          isMaster: boolean = false) {
        validator.validateByDefinition(definition, config, defaultCurrency, isMaster);
        if (!defaultCurrency) {
            this.validateConstFields(config, definition);
        }
        this.validateThatCalculatedFieldsNotPresent(config, definition);

        this.validateThatFixedFieldsNotPresent(config, definition);
        this.validateKnownFields(definition, config);
    }

    private async checkGameLimitsConfiguration(schemaConfigurationId: number): Promise<void> {
        const gameLimitsConfiguration = await GameLimitsConfigurationModel.findOne({
            where: { schemaConfigurationId: schemaConfigurationId }
        });
        if (gameLimitsConfiguration) {
            throw new ValidationError("Schema configuration linked with game limits configuration");
        }
    }

    private validateKnownFields(definition: SchemaDefinitionInstance,
                                defaultCurrencyConfig: SchemaConfigurationDefaultValue): void {
        this.commonValidator.validateStakeAll(defaultCurrencyConfig);
        if (defaultCurrencyConfig.coins && defaultCurrencyConfig.defaultCoin &&
            !defaultCurrencyConfig.coins.includes(defaultCurrencyConfig.defaultCoin)) {
            throw new ValidationError("Default coin should be in coins array");
        }
        this.commonValidator.validateAlertBlockSettings(definition, defaultCurrencyConfig as RoomGameLimits);
    }

    private createCacheKey(entityId: number, schemaDefinitionId: number): string {
        return `${entityId}:${schemaDefinitionId}`;
    }

    private validateThatCalculatedFieldsNotPresent(config: SchemaConfigurationDefaultValue,
                                                   definition: SchemaDefinition): void {
        const schema = definition.schema;
        const excessiveFields: Set<string> = new Set<string>();
        if (schema) {
            for (const prop in schema.properties) {
                if (schema.properties.hasOwnProperty(prop)) {
                    const def: PropertySchema = schema.properties[prop];
                    if (def.limitConfigurationType === LIMIT_CONFIGURATION_TYPE.CALCULATED &&
                        config[prop] !== undefined) {
                        excessiveFields.add(prop);
                    }
                }
            }
        }
        if (excessiveFields.size) {
            throw new ValidationError(
                `It has no sense to configure fields ${[...excessiveFields]}, because they will be calculated`
            );
        }
    }

    private validateConstFields(config: SchemaConfigurationDefaultValue,
                                definition: SchemaDefinition): void {
        const schema = definition.schema;
        if (schema) {
            for (const prop in schema.properties) {
                if (schema.properties.hasOwnProperty(prop)) {
                    const def: PropertySchema = schema.properties[prop];
                    if (def.limitConfigurationType === LIMIT_CONFIGURATION_TYPE.CONST && config[prop] !== undefined) {
                        throw new ValidationError(
                            `Field ${prop} is const field and can be defined only for default currency`
                        );
                    }
                }
            }
        }
    }

    private validateThatFixedFieldsNotPresent(config: SchemaConfigurationDefaultValue,
                                              definition: SchemaDefinition): void {
        const schema = definition.schema;
        if (schema) {
            for (const prop in schema.properties) {
                if (schema.properties.hasOwnProperty(prop)) {
                    const def: PropertySchema = schema.properties[prop];
                    if (def.limitConfigurationType === LIMIT_CONFIGURATION_TYPE.FIXED &&
                        config["aligned"] !== undefined &&
                        config["aligned"] === true) {
                        throw new ValidationError(
                            `Field ${prop} is fixed field and can not be aligned`
                        );
                    }
                }
            }
        }
    }
}
