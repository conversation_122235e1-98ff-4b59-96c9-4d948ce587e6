import * as redis from "../storage/redis";
import * as path from "path";
import config from "../config";
import * as Errors from "../errors";
import { UserType } from "../entities/user";
import { lazy, redis as redisUtils, logging } from "@skywind-group/sw-utils";

const uuid = require("uuid");

export function getAuthSessionService() {
    return container.get();
}

export interface AuthUserService {
    createSession(userId: number, userType: UserType, denyBoSessionKeyLogin?: boolean): Promise<string>;

    checkSession(userId: number, sessionId: string): Promise<boolean>;

    removeOneUserSession(userId: number, sessionId: string): Promise<void>;

    removeAllUserSessions(userId: number): Promise<void>;
}

const logger = logging.logger("authSession");

export class AuthSessionServiceImpl implements AuthUserService {
    private readonly createSessionProc = new redisUtils.RedisProc(logger,
        path.resolve(__dirname, "../../../resources/lua/AuthSessionManager.lua"),
        path.resolve(__dirname, "../../../resources/lua/createSession.lua"));

    private readonly checkSessionProc = new redisUtils.RedisProc(logger,
        path.resolve(__dirname, "../../../resources/lua/AuthSessionManager.lua"),
        path.resolve(__dirname, "../../../resources/lua/checkSession.lua"));

    private isDenyBoSemultaneousMode(userType: UserType, denyBoSimultaneousLogin?: boolean) {
        return denyBoSimultaneousLogin && userType === UserType.BO;
    }

    public async createSession(userId: number,
                               userType: UserType,
                               denyBoSimultaneousLogin: boolean = false): Promise<string> {
        return redis.usingDb<string>(async (db) => {
            const sessionKey = buildSessionKey(userId);

            if (this.isDenyBoSemultaneousMode(userType, denyBoSimultaneousLogin)) {
                await db.del(sessionKey);
            }

            const sessionId = uuid.v4();
            await this.createSessionProc.exec(db, [sessionKey], [sessionId, Date.now(), config.accessToken.expiresIn]);

            return sessionId;
        });
    }

    public async checkSession(userId: number, sessionId: string): Promise<boolean> {
        // check if sessionId exists in redis
        return await redis.usingDb<boolean>(async (db) => {
            const result = await this.checkSessionProc.exec(
                db,
                [buildSessionKey(userId)],
                [sessionId, Date.now().toString(), config.accessToken.expiresIn.toString()]);

            if (result) {
                return true;
            }

            return Promise.reject(new Errors.AccessSessionExpired(userId, sessionId));
        });
    }

    public async removeOneUserSession(userId: number, sessionId: string): Promise<void> {
        // delete sessionId from redis skip list
        await redis.usingDb<void>(async (db) => {
            await db.zrem(buildSessionKey(userId), sessionId);
        });
    }

    public async removeAllUserSessions(userId: number): Promise<void> {
        await redis.usingDb<void>(async (db) => {
            await db.del(buildSessionKey(userId));
        });
    }
}

export class DisabledAuthSessionServiceImpl implements AuthUserService {
    public async createSession(userId: number,
                               userType: UserType,
                               denyBoSimultaneousLogin: boolean = false): Promise<string> {
        return uuid.v4();
    }

    public async checkSession(userId: number, sessionId: string): Promise<boolean> {
        return true;
    }

    public async removeOneUserSession(userId: number, sessionId: string): Promise<void> {
        // Do nothing
    }

    public async removeAllUserSessions(userId: number): Promise<void> {
        // Do nothing
    }
}

const container = lazy(
    () => config.userSessionCheck.isEnabled ?
          new AuthSessionServiceImpl() :
          new DisabledAuthSessionServiceImpl()
);

function buildSessionKeyVersion1(userId: number) {
    return `authSession:user:${userId}`;
}

export function buildSessionKey(userId: number) {
    return `authSession:v2:user:${userId}`;
}
