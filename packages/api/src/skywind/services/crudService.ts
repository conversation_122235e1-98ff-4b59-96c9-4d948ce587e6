import { FindOptions, Transaction, Model, ModelStatic, CreationAttributes } from "sequelize";
import { sequelize as db } from "../storage/db";
import { ResourceNotFoundError } from "../errors";
import { encodeId } from "../utils/publicid";

export type ID = number | string;

export interface CRUDService<T extends Model<A>, A, M extends ModelStatic<T>> {
    getModel(): M;

    create(data: CreationAttributes<T>): Promise<T>;

    retrieve(id: ID, options?: FindOptions): Promise<T>;

    update(id: ID, data: Partial<A>): Promise<T>;

    destroy(id: ID): Promise<void>;

    list(options?: FindOptions): Promise<T[]>;
}

export abstract class CRUDServiceImpl<T extends Model<A>, A, M extends ModelStatic<T>>
    implements CRUDService<T, A, M> {
    public abstract getModel(): M;
    protected modelName: string = "Object";

    public async create(data: CreationAttributes<T>, transaction?: Transaction): Promise<T> {
        const cleanedData = this.validateCreateData(data);

        if (transaction) {
            return this.performCreate(cleanedData, transaction);
        }

        return db.transaction(async (t: Transaction) => {
            return this.performCreate(cleanedData, t);
        });
    }

    protected validateCreateData(data: CreationAttributes<T>): CreationAttributes<T> {
        return data;
    }

    protected async performCreate(cleanedData: CreationAttributes<T>, transaction: Transaction): Promise<T> {
        return this.getModel().create(cleanedData, { transaction });
    }

    public async update(id: ID, data: Partial<A>, transaction?: Transaction): Promise<T> {
        const cleanedData = this.validateUpdateData(data);

        const instance = await this.getInstance(id);

        if (transaction) {
            return this.performUpdate(instance, cleanedData, transaction);
        }

        return db.transaction(async (t: Transaction) => {
            return this.performUpdate(instance, cleanedData, t);
        });
    }

    protected validateUpdateData(data: Partial<A>): Partial<A> {
        return data;
    }

    protected async performUpdate(instance: T,
                                  cleanedData: Partial<A>,
                                  transaction: Transaction): Promise<any> {
        return instance.update(cleanedData, { transaction });
    }

    public async retrieve(id: ID, options?: FindOptions): Promise<T> {
        return this.getInstance(id, true, options);
    }

    public async destroy(id: ID, transaction?: Transaction): Promise<void> {
        const instance = await this.getInstance(id);

        if (transaction) {
            return this.performDestroy(instance, transaction);
        }

        return db.transaction(async (t: Transaction) => {
            return this.performDestroy(instance, t);
        });
    }

    protected async performDestroy(instance: T, transaction: Transaction): Promise<void> {
        return instance.destroy({ transaction });
    }

    protected async getInstance(id: ID,
                                raiseErrorIfNotFound: boolean = true,
                                options?: FindOptions): Promise<T> {
        const instance = await this.getModel().findByPk(id, options);
        if (raiseErrorIfNotFound && !instance) {
            return Promise.reject(
                new ResourceNotFoundError(`${this.modelName} not found by ID (${encodeId(id)})`)
            );
        }

        return instance;
    }

    public async list(options?: FindOptions): Promise<T[]> {
        return this.getModel().findAll(options);
    }

}
