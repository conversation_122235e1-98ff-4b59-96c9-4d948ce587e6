import { Models } from "../models/models";
import { literal, Op, WhereOptions } from "sequelize";
import { BaseEntity } from "../entities/entity";
import * as UserService from "./user/user";
import getUserService from "./user/user";
import { User } from "../entities/user";
import * as Errors from "../errors";
import * as FilterService from "./filter";
import { NotificationInfo, NotificationReceiverInfo, SendData } from "../entities/notification";

export const queryParamsKeysReceived = [
    "unread",
    "status",
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
];

export const queryParamsKeysPosted = [
    "status",
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
];

const sortableKeysReceived = ["status", "unread", "ts"];
const sortableKeysPosted = ["status", "ts"];
const DEFAULT_SORT_KEY = "ts";
const NotificationModel = Models.NotificationsModel;
const NotificationReceiversModel = Models.NotificationReceiverModel;

async function getReceivers(entity: BaseEntity, data: SendData): Promise<number[]> {
    let userIds: number[] = data.receiversId || [];
    if (data.allSiblings) {
        userIds = userIds.concat(await UserService.getAllSiblings(entity));
    }
    if (data.recursively) {
        userIds = userIds.concat(await UserService.getAllSiblingsAndSubUsers(entity));
    }
    return userIds.filter((value, index, a) => a.indexOf(value) === index);
}

export async function sendNotification(entity: BaseEntity, userName: string, data: SendData): Promise<void> {
    const service = getUserService(entity);
    const user: User = await service.findOne(userName);
    const receivers: Array<number> = await getReceivers(entity, data);
    const selfIndex = receivers.indexOf(user.id);
    if (selfIndex !== -1) {
        receivers.splice(selfIndex, 1);
    }
    if (receivers.length === 0) {
        return Promise.reject(new Errors.NoReceiversForNotification());
    }
    try {
        const notificationInstance = await NotificationModel.create({
            message: data.message,
            author: user.id,
            ts: new Date(),
            lifetime: data.lifetime ? new Date(data.lifetime) : undefined,
        });

        const notificationId: number = notificationInstance.get("id");
        await NotificationReceiversModel.bulkCreate(receivers.map(receiver => ({
                receiver: receiver,
                notificationId: notificationId
            })
        ));
    } catch (err) {
        return Promise.reject(err);
    }
}

export async function getNotifications(userId: number, query: WhereOptions<any>): Promise<NotificationReceiverInfo[]> {
    let sortBy = FilterService.getSortKey(query, sortableKeysReceived, DEFAULT_SORT_KEY);
    const sortOrder = FilterService.valueFromQuery(query, "sortOrder") || "ASC";
    if (sortBy === "ts") {
        sortBy = "notification.ts";
    }
    query["receiver"] = userId;
    const notifications = await NotificationReceiversModel.findAll({
        where: query,
        offset: FilterService.valueFromQuery(query, "offset"),
        limit: FilterService.valueFromQuery(query, "limit"),
        order: literal(`"${sortBy}" ${sortOrder}`),
        include: [
            {
                association: NotificationReceiversModel.associations.notification,
                as: "notification"
            },
        ]
    });
    return notifications.map(item => item.toInfo());
}

export async function getPostedNotifications(userId: number, query: WhereOptions<any>): Promise<NotificationInfo[]> {
    const sortBy = FilterService.getSortKey(query, sortableKeysPosted, DEFAULT_SORT_KEY);
    const sortOrder = FilterService.valueFromQuery(query, "sortOrder") || "ASC";
    query["author"] = userId;
    const notifications = await NotificationModel.findAll({
        where: query,
        offset: FilterService.valueFromQuery(query, "offset"),
        limit: FilterService.valueFromQuery(query, "limit"),
        order: literal(`"${sortBy}" ${sortOrder}`),
    });
    return notifications.map(notification => notification.toInfo());
}

export async function notificationBulkAction(userId: number, data: any): Promise<void> {
    const ids: number[] = data.id.sort();
    delete data.id;
    try {
        await NotificationReceiversModel.update(data, {
            where: {
                receiver: userId,
                id: { [Op.in]: ids }
            }
        });
    } catch (err) {
        return Promise.reject(new Errors.BulkActionDbError());
    }
}

export async function changeStatus(userId: number, id: number, status: string): Promise<NotificationInfo> {
    const notificationInstance = await NotificationModel.findOne({ where: { id: id, author: userId } });
    if (!notificationInstance) {
        return Promise.reject(new Errors.NotificationNotFound());
    }
    const notification: NotificationInfo = notificationInstance.toInfo();
    await NotificationReceiversModel.update({ status: status }, { where: { notificationId: id } });
    await NotificationModel.update({ status: status }, { where: { id: id } });
    notification.status = status;
    return notification;
}
