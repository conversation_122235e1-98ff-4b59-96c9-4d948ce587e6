import { Models } from "../models/models";

import * as Errors from "../errors";
import { getIpLocationService } from "../utils/iplocation";
import { getParentIds } from "./entity";
import { BaseEntity, WhiteList } from "../entities/entity";
import { Op } from "sequelize";

const EntityInfoModel = Models.EntityInfoModel;

export function validateIpList(list: string[], key?: string): string[] {
    const prefix = key ? `${key} ` : "";
    const errors: string[] = [];
    if (!Array.isArray(list)) {
        errors.push(prefix + "is not an array");
    } else {
        for (const ip of list) {
            if (!getIpLocationService().validateWhitelistIP(ip)) {
                errors.push(prefix + `${ip} is not valid`);
            }
        }
    }
    return errors;
}

export default abstract class IpWhiteListService {

    protected constructor(public readonly entity: BaseEntity, private readonly type: string) {
    }

    public async get(): Promise<WhiteList> {
        const parents = await this.getParents();
        const ownData = await this.getData();
        if (parents.length === 0 && ownData === null) {
            return Promise.reject(new Errors.WhitelistNotFound());
        }
        const result: WhiteList = {};
        if (parents.length > 0) {
            result.parent = parents;
        }
        if (ownData !== null) {
            result.own = ownData;
        }
        return result;
    }

    public async createOrUpdate(whitelist: string[], merge = true): Promise<string[]> {
        const data = await this.getData();
        if (!data) {
            whitelist = whitelist.filter((item, index, arr) => arr.indexOf(item) === index);
            await EntityInfoModel.create({
                entityId: this.entity.id,
                type: this.type,
                data: whitelist,
            });
            return whitelist;
        }
        whitelist = merge ? data.concat(whitelist) : whitelist;
        whitelist = whitelist.filter((item, index, arr) => arr.indexOf(item) === index);
        await EntityInfoModel.update(
            {
                data: whitelist,
                updatedAt: new Date(),
            },
            {
                where: {
                    entityId: this.entity.id,
                    type: this.type,
                }
            });
        return whitelist;
    }

    public async remove(whitelist: string[]): Promise<string[]> {
        const data = await this.getData();
        if (!data) {
            return Promise.reject(new Errors.WhitelistNotFound());
        }
        const parents = new Set(await this.getParents());
        const own = new Set(data);
        if (whitelist.some(ip => parents.has(ip) && !own.has(ip))) {
            throw new Errors.CannotDeleteIpFromParent();
        }

        const newWhitelist = data.filter(x => whitelist.indexOf(x) === -1);
        await EntityInfoModel.update(
            {
                data: newWhitelist,
                updatedAt: new Date(),
            },
            {
                where: {
                    entityId: this.entity.id,
                    type: this.type,
                }
            });
        return newWhitelist;
    }

    private async getData(): Promise<string[] | null> {
        const entityInfo = await EntityInfoModel.findOne({
            where: {
                entityId: this.entity.id,
                type: this.type,
            },
            attributes: ["data"],
        });
        return entityInfo ? entityInfo.get("data") : null;
    }

    private async getParents(): Promise<string[]> {
        const parentIds = getParentIds(this.entity);
        const parents = await EntityInfoModel.findAll({
            where: {
                entityId: { [Op.in]: [...parentIds] },
                type: this.type,
            },
            attributes: ["data"],
        });
        return parents
            .map(item => item.get("data"))
            .reduce((result, item) => result.concat(item), [])
            .filter((ip, index, data) => data.indexOf(ip) === index);
    }
}
