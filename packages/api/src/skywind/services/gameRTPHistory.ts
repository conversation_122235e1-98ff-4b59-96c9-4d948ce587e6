import { GameRtpHistoryModel } from "../models/gameRtpHistory";
import { EntityGame, Game, GameRTP, RtpConfigurator } from "../entities/game";
import { lazy, Lazy } from "@skywind-group/sw-utils";
import { EntitySettings } from "../entities/settings";
import { PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import { BindOrReplacements, FindOptions, Op, WhereOptions, literal } from "sequelize";
import { BaseEntity } from "../entities/entity";
import * as FilterService from "./filter";
import { sequelize as db } from "../storage/db";
import { findAllEntityGames } from "./game";
import { EntityGamesNotFoundError, ValidationError } from "../errors";
import { MAX_INT_VALUE, SUPER_ADMIN_ID } from "../utils/common";
import { isPermitted } from "./security";
import { UserPermissions } from "../entities/user";
import { Models } from "../models/models";

const gameRtpHistoryModel = Models.GameRtpHistoryModel;

export interface GameRTPReport {
    id: number;
    entityId: number;
    entityTitle: string;
    inheritedFromEntityId?: number;
    inheritedFromEntityTitle?: string;
    inherited?: boolean;
    gameId: number;
    gameCode: string;
    gameTitle: string;
    rtp: number;
    rtpDeduction: number;
    finalRTP: number;
    ts: Date;
}

export interface GameRTPReportEx {
    records: GameRTPReport[];
    count: number;
}

interface ReportReplacements {
    entityId: number;
    gameCodes?: string;
    tsFrom?: string;
    tsTo?: string;
    offset?: number;
    limit?: number;
    rtpDeductionGte?: number;
}

interface SearchGameRTPHistoryReportOptions {
    gameCodes?: string;
    tsFrom?: string;
    tsTo?: string;
    offset?: string;
    limit?: string;
    rtpDeductionGte?: number;
}

interface RTPService {
    rtpDeductionSupportedForGame(features: GameRTP): boolean;

    rtpTheoretical(gameFeatures: GameRTP, gameSettings?: RtpConfigurator): number;

    rtpDeduction(gameSettings: RtpConfigurator): number;

    rtpDeductionSupportedForPlayMode(playMode: PlayMode, features: GameRTP,
                                     entitySettings: EntitySettings, gameSettings: RtpConfigurator): boolean;

    rtpFinal(gameFeatures: GameRTP, gameSettings: RtpConfigurator): number;
}

// TODO: remove support of rtp deduction in entity settings
class RTPServiceImpl implements RTPService {
    public rtpDeductionSupportedForGame(features: GameRTP): boolean {
        return Boolean(
            features.supportsRtpConfigurator &&
            (features.baseRTP || features.baseRTPRange)
        );
    }

    public deductionEnabled(settings: RtpConfigurator): boolean {
        return !!settings;
    }

    public playModeSupported(playmode: PlayMode, entitySettings: EntitySettings) {
        return playmode === PlayMode.REAL || (playmode === PlayMode.FUN && entitySettings.rtpDeductionFunModeEnabled);
    }

    public rtpDeductionSupportedForPlayMode(playMode: PlayMode, features: GameRTP,
                                            entitySettings: EntitySettings, gameSettings: RtpConfigurator): boolean {

        const playModeSupported = this.playModeSupported(playMode, entitySettings);
        return this.rtpDeductionSupportedForGame(features) && this.deductionEnabled(gameSettings) &&
            playModeSupported;
    }

    public rtpTheoretical(gameFeatures: GameRTP, gameSettings?: RtpConfigurator): number {
        return gameSettings?.rtp ||
            gameFeatures.baseRTP ||
            gameFeatures?.baseRTPRange?.max ||
            0;
    }

    public rtpDeduction(gameSettings: RtpConfigurator): number {
        return gameSettings?.rtpDeduction || 0;
    }

    public rtpFinal(gameFeatures: GameRTP, gameSettings: RtpConfigurator): number {
        const resultRtpTheoretical = this.rtpTheoretical(gameFeatures, gameSettings);
        const resultRtpDeduction = this.rtpDeduction(gameSettings);
        const isAvailableRtpDeduction = this.rtpDeductionSupportedForGame(gameFeatures);
        if (isAvailableRtpDeduction) {
            return resultRtpTheoretical - resultRtpDeduction;
        }
        return resultRtpTheoretical;
    }
}

export const rtpService: Lazy<RTPService> = lazy(() => new RTPServiceImpl());

export interface GameRTPHistory {
    logEntityGame(newEntityGame: EntityGame, entityGame?: EntityGame): void;

    logGame(newGame: Game, game?: Game): void;

    findAll(entity?: BaseEntity, filter?: WhereOptions): Promise<GameRtpHistoryModel[]>;
}

export class GameRTPHistoryImpl implements GameRTPHistory {
    public readonly rtpFields = ["baseRTP", "baseRTPRange", "jpRTP", "featuresRTP", "supportsRtpConfigurator"];
    private readonly theoreticalRtpFields = ["baseRTP", "baseRTPRange"];
    public readonly sortBy = "ts";
    public readonly sortOrder = "ASC";

    private rtpService: RTPService;

    constructor(private model = gameRtpHistoryModel) {
        this.rtpService = rtpService.get();
    }

    private async create(game: Game, rtpInfo: GameRTP, entityGame?: EntityGame) {
        const rtpConfigurator = entityGame?.settings?.rtpConfigurator;

        if ((!rtpConfigurator || !Object.keys(rtpConfigurator).length) &&
            (!rtpInfo || !Object.keys(rtpInfo).length)) {
            return;
        }

        return this.model.create({
            entityId: entityGame?.entityId,
            gameId: game.id,
            gameCode: game.code,
            rtpConfigurator,
            rtpInfo
        });
    }

    public logEntityGame(newEntityGame: EntityGame, existingEntityGame?: EntityGame) {
        const rtpConfigurator: RtpConfigurator = {
            rtp: newEntityGame?.settings?.rtpConfigurator?.rtp,
            rtpDeduction: newEntityGame?.settings?.rtpConfigurator?.rtpDeduction
        };

        let hasChanged = false;
        let newGameWithDeduction = false;
        if (existingEntityGame) {
            const { rtp, rtpDeduction }: RtpConfigurator = existingEntityGame?.settings?.rtpConfigurator || {};
            hasChanged = rtp !== rtpConfigurator.rtp || rtpDeduction !== rtpConfigurator.rtpDeduction;
        } else {
            const { rtp, rtpDeduction }: RtpConfigurator = newEntityGame?.settings?.rtpConfigurator || {};
            newGameWithDeduction = rtp !== undefined || rtpDeduction !== undefined;
        }

        if (newGameWithDeduction || hasChanged) {
            return this.create(newEntityGame.game, this.getRTPInfo(newEntityGame.game), newEntityGame);
        }
    }

    private getRTPInfo(game: Game): GameRTP {
        const rtpInfo: GameRTP = {};

        for (const rtpField of this.rtpFields) {
            rtpInfo[rtpField] = game.features[rtpField];
        }

        return rtpInfo;
    }

    public logGame(newGame: Game, existingGame?: Game) {
        const existingRTPInfo: GameRTP = {};
        const rtpInfo: GameRTP = {};

        for (const rtpField of this.rtpFields) {
            rtpInfo[rtpField] = newGame.features[rtpField];
            if (existingGame) {
                existingRTPInfo[rtpField] = existingGame.features[rtpField];
            }
        }
        const hasChanged = existingGame && JSON.stringify(existingRTPInfo) !== JSON.stringify(rtpInfo);
        const newGameWithTheoreticalInfo =
            !existingGame && this.theoreticalRtpFields.some(f => rtpInfo[f] !== undefined);

        if (newGameWithTheoreticalInfo || hasChanged) {
            return this.create(newGame, rtpInfo);
        }
    }

    public async findAll(entity?: BaseEntity, filter: WhereOptions = {}): Promise<GameRtpHistoryModel[]> {

        const sortOrder = FilterService.valueFromQuery(filter, "sortOrder") || this.sortOrder;
        if (entity && !filter["entityId"]) {
            filter["entityId"] = entity.id;
        }

        const query: FindOptions<any> = {
            where: filter,
            order: [[this.sortBy, sortOrder]],
        };
        if ("limit" in filter) {
            query.limit = FilterService.valueFromQuery(filter, "limit") || FilterService.DEFAULT_LIMIT;
        }
        if ("offset" in filter) {
            query.offset = FilterService.valueFromQuery(filter, "offset") || FilterService.DEFAULT_OFFSET;
        }
        // TODO: As I understand it is deprecated
        if ("order" in filter && typeof filter.order === "string") {
            query.order = literal(filter.order);
            filter.order = undefined;
        }

        return await this.model.findAll(query);
    }
}

export const gameRTPHistoryService: Lazy<GameRTPHistory> = lazy(() => new GameRTPHistoryImpl());

export interface GameRTPHistoryReport {
    findAll(entity: BaseEntity,
            options?: SearchGameRTPHistoryReportOptions,
            userPermissions?: UserPermissions,
            detailedViewPermissions?: string[]): Promise<GameRTPReportEx>;
}

export class GameRTPHistoryReportImpl implements GameRTPHistoryReport {
    private rtpService = rtpService.get();
    private maxLimit = 10000;
    private maxOffset = MAX_INT_VALUE;

    public async findAll(entity: BaseEntity,
                         options: SearchGameRTPHistoryReportOptions = {},
                         userPermissions?: UserPermissions,
                         detailedViewPermissions?: string[]): Promise<GameRTPReportEx> {
        const { gameCodes, tsFrom, tsTo, limit, offset, rtpDeductionGte } = options;
        const replacements: ReportReplacements & BindOrReplacements = {
            entityId: entity.id
        };

        const sqlParams: string[] = ["p_entity_id => :entityId"];
        this.validatePagination(+limit, +offset);
        GameRTPHistoryReportImpl.validateTs(tsFrom, tsTo);

        if (gameCodes && gameCodes.length) {
            const gameCodesList: string[] = gameCodes
                .split(",")
                .map(gameCode => gameCode.trim().toLowerCase());

            await this.validateGameCodes(entity, gameCodesList);

            sqlParams.push("p_game_codes => :gameCodes::VARCHAR[]");
            replacements.gameCodes = `{${gameCodesList.join(",")}}`;
        }
        if (tsFrom) {
            sqlParams.push("p_ts_from => :tsFrom::timestamp");
            replacements.tsFrom = tsFrom;
        }
        if (tsTo) {
            sqlParams.push("p_ts_till => :tsTo::timestamp");
            replacements.tsTo = tsTo;
        }
        if (limit) {
            sqlParams.push("p_limit => :limit::integer");
            replacements.limit = +limit;
        }
        if (offset) {
            sqlParams.push("p_offset => :offset::integer");
            replacements.offset = +offset;
        }
        if (rtpDeductionGte !== undefined) {
            sqlParams.push("p_deduction_gte => :rtpDeductionGte::numeric");
            replacements.rtpDeductionGte = +rtpDeductionGte;
        }

        const recordsData = await db.query(
            GameRTPHistoryReportImpl.getSQLForReport(sqlParams),
            { replacements, raw: true });

        if (!recordsData || !recordsData[0].length) {
            return { records: [], count: 0 };
        }

        const isDetailedViewPermitted = userPermissions && Array.isArray(detailedViewPermissions) &&
            detailedViewPermissions.length && isPermitted(detailedViewPermissions, userPermissions);

        return {
            records: recordsData[0].map(record => this.mapRecord(entity, record, isDetailedViewPermitted)),
            count: recordsData[0][0]["total_rows"],
        };
    }

    private static getSQLForReport(params: string[]): string {
        return `SELECT *
                FROM fnc_list_game_rtp_history(${params})`;
    }

    private mapRecord(entity: BaseEntity, item: any, isDetailedViewPermitted?: boolean): GameRTPReport {
        const rtpInfo: GameRTP = item.rtp_info;
        const rtpDeductionInfo: RtpConfigurator = item.rtp_deduction;

        const rtp = +this.rtpService.rtpTheoretical(rtpInfo, rtpDeductionInfo) || null;
        const rtpDeduction = this.rtpService.rtpDeduction(rtpDeductionInfo) || 0;
        const finalRTP = this.rtpService.rtpFinal(rtpInfo, rtpDeductionInfo);

        const inheritedFromEntityId = item.entity_id === null
                                      ? SUPER_ADMIN_ID
                                      : item.entity_id; // null means changes in games table
        const inheritedFromEntityTitle = item.entity_id === null || entity.title === null ||
                                         entity.id === item.entity_id ?
                                         "" :
                                         item.entity_title;

        return {
            id: item.id,
            entityId: entity.id,
            entityTitle: entity.title || "",
            inheritedFromEntityId: isDetailedViewPermitted ? inheritedFromEntityId : undefined,
            inheritedFromEntityTitle: isDetailedViewPermitted ? inheritedFromEntityTitle : undefined,
            inherited: isDetailedViewPermitted ? undefined : !!inheritedFromEntityId,
            gameId: item.game_id,
            gameCode: item.game_code,
            gameTitle: item.game_title,
            rtp,
            finalRTP,
            rtpDeduction,
            ts: item.ts
        };
    }

    private async validateGameCodes(entity: BaseEntity, gameCodes: string[]) {

        const queries = new Map();

        queries.set("game", { code: { [Op.in]: gameCodes } });

        const entityGames: EntityGame[] = await findAllEntityGames(entity, queries);
        const availableGameCodes = entityGames.map(entityGame => entityGame.game.code);

        const notFoundGames = gameCodes.filter(gameCode => !availableGameCodes.includes(gameCode));

        if (notFoundGames && notFoundGames.length) {
            throw new EntityGamesNotFoundError(notFoundGames);
        }
    }

    private static validateTs(tsFrom?: string, tsTo?: string) {
        if (tsFrom && typeof tsFrom === "string" && isNaN(Date.parse(tsFrom))) {
            throw new ValidationError("start date parameter should be a datetime");
        }

        if (tsTo && typeof tsTo === "string" && isNaN(Date.parse(tsTo))) {
            throw new ValidationError("end date parameter should be a datetime");
        }

        if (tsFrom && tsTo && (new Date(tsFrom).getTime() > new Date(tsTo).getTime())) {
            throw new ValidationError("end date should be greater than start date");
        }
    }

    private validatePagination(limit: number, offset: number) {
        if (limit !== undefined && (typeof limit !== "number" || limit < 0 || limit > this.maxLimit)) {
            throw new ValidationError(`Limit must be a number from 0 to ${this.maxLimit}`);
        }

        if (offset !== undefined && (typeof offset !== "number" || offset < 0 || offset > this.maxOffset)) {
            throw new ValidationError(`Offset must be a number from 0 to ${this.maxOffset}`);
        }
    }
}

export const gameRTPReportService: Lazy<GameRTPHistoryReport> = lazy(() => new GameRTPHistoryReportImpl());
