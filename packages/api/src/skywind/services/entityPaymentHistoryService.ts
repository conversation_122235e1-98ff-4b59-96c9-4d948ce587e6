import { BaseEntity } from "../entities/entity";
import { FindOptions, literal, Op, WhereOptions } from "sequelize";
import { PagingHelper } from "../utils/paginghelper";
import * as FilterService from "./filter";
import { EntityPaymentHistoryInfo } from "../entities/entityPaymentHistory";
import { Currencies, Currency } from "@skywind-group/sw-currency-exchange";
import * as Errors from "../errors";
import { Models } from "../models/models";

const EntityPaymentHistoryModel = Models.EntityPaymentHistoryModel;
const EntityModel = Models.EntityModel;

const sortableKeys = ["ts", "amount"];
const DEFAULT_SORT_KEY = "ts";
export const queryParamsKeys = [
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
    "isTest",
    "currency",
    "amount",
    "ts",
    "toEntityId",
    "fromEntityId",
    "type"
];

enum FINANCE_FETCH_TYPE {
    DEBITS = 0,
    CREDITS = 1,
    ALL = 2
}

export async function findEntityCredits(entity: BaseEntity,
                                        query: WhereOptions<any>): Promise<EntityPaymentHistoryInfo[]> {
    if (query["toEntityId"]) {
        query["to"] = query["toEntityId"][Op.eq].toString();
        delete query["toEntityId"];
    }

    query["from"] = entity.id.toString();
    return getEntityPaymentHistoryItems(query, FINANCE_FETCH_TYPE.CREDITS);
}

export async function findEntityDebits(entity: BaseEntity,
                                       query: WhereOptions<any>): Promise<EntityPaymentHistoryInfo[]> {
    if (query["fromEntityId"]) {
        query["from"] = query["fromEntityId"][Op.eq].toString();
        delete query["fromEntityId"];
    }

    query["to"] = entity.id.toString();
    return getEntityPaymentHistoryItems(query, FINANCE_FETCH_TYPE.DEBITS);
}

/**
 * Finds both debits and credits
 */
export async function findEntityFinance(entity: BaseEntity,
                                        query: any): Promise<EntityPaymentHistoryInfo[]> {
    if (query["toEntityId"] && query["fromEntityId"]) {
        throw new Errors.ValidationError("Can't specify both fromEntityId and toEntityId search params");
    }

    if (query["type"] && query["type"][Op.eq] === "debit") {
        delete query["type"];
        delete query["toEntityId"];
        return findEntityDebits(entity, query);
    } else if (query["type"] && query["type"][Op.eq] === "credit") {
        delete query["type"];
        delete query["fromEntityId"];
        return findEntityCredits(entity, query);
    }

    query[Op.or] = [
        { from: entity.id.toString() },
        { to: entity.id.toString() }
    ];

    if (query["toEntityId"]) {
        query["to"] = query["toEntityId"][Op.eq].toString();
        delete query["toEntityId"];
    }

    if (query["fromEntityId"]) {
        query["from"] = query["fromEntityId"][Op.eq].toString();
        delete query["fromEntityId"];
    }

    return getEntityPaymentHistoryItems(query, FINANCE_FETCH_TYPE.ALL);
}

async function getEntityPaymentHistoryItems(query: WhereOptions<any>,
                                            fetchType: FINANCE_FETCH_TYPE): Promise<EntityPaymentHistoryInfo[]> {
    validateAndDecorateQuery(query);

    /**
     * As we cannot associate EntityPaymentHistoryModel and EntityModel,
     * we have to fetch entities info with a separate call.
     */
    const entityIdsToFetch = [];

    const entityPaymentHistoryItems = await PagingHelper.findAndCountAll(EntityPaymentHistoryModel,
        queryToFindOptions(query),
        entityPaymentInfo => {

            const currency: Currency = Currencies.get(entityPaymentInfo.currency);
            const item: any = {
                amount: currency.toMajorUnits(+entityPaymentInfo.amount),
                currency: entityPaymentInfo.currency,
                ts: entityPaymentInfo.ts,
                isTest: entityPaymentInfo.isTest,
                toEntityId: undefined,
                fromEntityId: undefined
            };
            if (entityPaymentInfo.initiatorName) {
                item.initiatorName = entityPaymentInfo.initiatorName;
            }

            switch (fetchType) {
                case FINANCE_FETCH_TYPE.DEBITS:
                    entityIdsToFetch.push(+entityPaymentInfo.from);
                    item.fromEntityId = +entityPaymentInfo.from;
                    break;
                case FINANCE_FETCH_TYPE.CREDITS:
                    entityIdsToFetch.push(+entityPaymentInfo.to);
                    item.toEntityId = +entityPaymentInfo.to;
                    break;
                default:
                    entityIdsToFetch.push(+entityPaymentInfo.from, +entityPaymentInfo.to);
                    item.fromEntityId = +entityPaymentInfo.from;
                    item.toEntityId = +entityPaymentInfo.to;
                    break;
            }
            return item;
        });

    return fetchEntitiesDataAndDecorateHistory(entityIdsToFetch, entityPaymentHistoryItems, fetchType);
}

async function fetchEntitiesDataAndDecorateHistory(entityIdsToFetch: number[],
                                                   entityPaymentHistoryItems,
                                                   fetchType: FINANCE_FETCH_TYPE): Promise<EntityPaymentHistoryInfo[]> {
    const dbEntities = await EntityModel.findAll({
        where: { id: { [Op.in]: entityIdsToFetch } },
        attributes: ["id", "name", "title", "path"] });

    const entitiesMap = new Map();

    dbEntities.forEach(dbEntity => {
        entitiesMap.set(+dbEntity.get("id"), {
            id: +dbEntity.get("id"),
            name: dbEntity.get("name"),
            title: dbEntity.get("title"),
            path: dbEntity.get("path")
        });
    });

    switch (fetchType) {
        case FINANCE_FETCH_TYPE.DEBITS:
            entityPaymentHistoryItems.forEach(item => {
                item.fromEntityInfo = entitiesMap.get(item.fromEntityId);
                delete item.fromEntityId;
            });
            break;
        case FINANCE_FETCH_TYPE.CREDITS:
            entityPaymentHistoryItems.forEach(item => {
                item.toEntityInfo = entitiesMap.get(item.toEntityId);
                delete item.toEntityId;
            });
            break;
        default:
            entityPaymentHistoryItems.forEach(item => {
                item.fromEntityInfo = entitiesMap.get(item.fromEntityId);
                item.toEntityInfo = entitiesMap.get(item.toEntityId);
                delete item.fromEntityId;
                delete item.toEntityId;
            });
            break;
    }

    return entityPaymentHistoryItems;
}

function validateAndDecorateQuery(query: WhereOptions<any>) {
    query["transferType"] = "ent-ent";
    if (query["amount"]) {
        // ensure that currency is specified in case of amount param is present
        if (!query["currency"] || !query["currency"][Op.eq]) {
            throw new Errors.ValidationError("Single currency should be specified for querying with amount");
        }
        const currency: Currency = Currencies.get(query["currency"][Op.eq]);
        Object.keys(query["amount"]).forEach(amountKey =>
            query["amount"][amountKey] = currency.toMinorUnits(query["amount"][amountKey]));
    }
}

function queryToFindOptions(query: WhereOptions<any>): FindOptions<any> {
    const sortBy = FilterService.getSortKey(query, sortableKeys, DEFAULT_SORT_KEY);
    const sortOrder = FilterService.valueFromQuery(query, "sortOrder") || "ASC";

    return {
        where: query,
        offset: FilterService.valueFromQuery(query, "offset") || FilterService.DEFAULT_OFFSET,
        limit: FilterService.valueFromQuery(query, "limit") || FilterService.DEFAULT_LIMIT,
        order: literal(`"${sortBy}" ${sortOrder}`),
    };
}
