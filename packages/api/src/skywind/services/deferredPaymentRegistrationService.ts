import { DeferredPaymentFacade } from "@skywind-group/sw-management-deferredpayment";
import { PlayServiceFactory } from "@skywind-group/sw-management-gameprovider-core";
import { OperatorInfo, OperatorInfoRepository } from "@skywind-group/sw-management-gameprovider";
import { IWalletFacade } from "@skywind-group/sw-management-wallet";
import EntityCache from "../cache/entity";
import EntitySettingsService from "./settings";
import { PlayService } from "@skywind-group/sw-management-playservice";
import {
    DeferredPayment,
    DeferredPaymentMethod,
    DeferredPaymentStatus,
    DeferredPaymentRegistration,
    RegistrationTransactionNotFound
} from "@skywind-group/sw-deferred-payment";
import { PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import { EntitySettings } from "../entities/settings";
import * as merchantTestPlayersCache from "../cache/testPlayers";
import { ENTITY_TYPE } from "../entities/entity";
import { getBrandPlayerService } from "./brandPlayer";
import { BrandEntity } from "../entities/brand";

export interface DeferredPaymentRegistrationService {
    register(registration: DeferredPaymentRegistration): Promise<DeferredPayment[]>;
}

export class DeferredPaymentRegistrationServiceImpl implements DeferredPaymentRegistrationService {
    constructor(private readonly deferredPaymentFacade: DeferredPaymentFacade,
                private readonly playServiceFactory: PlayServiceFactory,
                private readonly operatorInfoRepository: OperatorInfoRepository,
                private readonly walletFacade: IWalletFacade) {
    }

     public async register(registration: DeferredPaymentRegistration): Promise<DeferredPayment[]> {
        let payments: DeferredPayment[] = await this.find(registration);
        if (!payments || !payments.length) {
            for (const payment of registration.payments) {
                // There can be cases when the brand is not a test brand and a test player cannot be found on our side
                // Phantom sends the "isTest" parameter inside the payment, use it if it's present
                const isTestPayment = payment.isTest || await this.isTestPayment(payment.brandId, payment.playerCode);
                if (isTestPayment) {
                    payment.isTest = isTestPayment;
                }

                if (!payment.paymentMethod) {
                    const { bonusPaymentMethod } = await this.getSettings(payment.brandId);
                    payment.paymentMethod = bonusPaymentMethod || DeferredPaymentMethod.MANUAL;
                }

                if (payment.paymentMethod === DeferredPaymentMethod.BONUS) {
                    payment.transactionId = await this.walletFacade.generateTransactionId();
                }
            }
            payments = await this.deferredPaymentFacade.register(registration);
        }

        const uncompletedBonusPayments = payments
            .filter(p => p.paymentMethod === DeferredPaymentMethod.BONUS)
            .filter((p: any) => p.status !== DeferredPaymentStatus.COMPLETED);
        await Promise.all(uncompletedBonusPayments.map(p => this.commitBonusDeferredPayment(registration, p)));

        return payments;
    }

    private async find(registration) {
        try {
            return await this.deferredPaymentFacade.find(registration.sourceTransactionId, registration.source);
        } catch (err) {
            if (err instanceof RegistrationTransactionNotFound) {
                return undefined;
            }
            throw err;
        }
    }

    private async commitBonusDeferredPayment(registration: DeferredPaymentRegistration,
                                             payment: DeferredPayment): Promise<void> {
        const operatorInfo: OperatorInfo = await this.operatorInfoRepository.findById(+payment.brandId);
        const playService: PlayService = await this.playServiceFactory.create(operatorInfo, PlayMode.REAL, false);

        const oldPromoId = `${registration.sourceTransactionId}_${registration.source}`;
        const { newDeferredPaymentPromoId } = await this.getSettings(payment.brandId);
        const promoId = newDeferredPaymentPromoId ? (payment as any).promoId || oldPromoId : oldPromoId;

        await playService.commitOfflineDeferredPayment(promoId, {
            ...payment,
            sourceTransactionId: registration.sourceTransactionId
        } as any);
        await this.deferredPaymentFacade.complete(payment);
    }

    private async getSettings(brandId: number): Promise<EntitySettings> {
        const entity = await EntityCache.findOne({ id: +brandId }, undefined, true);
        const entitySettingsService = new EntitySettingsService(entity);
        return entitySettingsService.get();
    }

    private async isTestPayment(brandId: number, playerCode: string): Promise<boolean> {
        const brand = await EntityCache.findOne({ id: +brandId }, undefined, true);
        if (brand.isTest) {
            return brand.isTest;
        }

        if (brand.type === ENTITY_TYPE.MERCHANT) {
            const player = await merchantTestPlayersCache.findOne(brand, playerCode);
            return !!player;
        } else {
            try {
                const player = await getBrandPlayerService().findOne(brand as BrandEntity, { code: playerCode });
                return player ? player.isTest : false;
            } catch (err) {
                return false;
            }
        }
    }
}
