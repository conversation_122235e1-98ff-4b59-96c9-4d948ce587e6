import { EntityGame, PlayerGameURLInfo } from "../../entities/game";
import { BrandEntity } from "../../entities/brand";
import { EntitySettings } from "../../entities/settings";
import { getEntityDomainService } from "../entityDomainService";
import { DOMAIN_TYPE } from "../../utils/common";
import { generatePlayerLoginToken, generateStartGameToken } from "../../utils/token";
import * as Errors from "../../errors";
import { URLSearchParams } from "url";
import { getDeploymentGroupService } from "../deploymentGroup";
import { getGameClientVersionService } from "../gameVersionService";
import { MerchantStartGameTokenData, PlayMode, StartGameTokenData } from "@skywind-group/sw-wallet-adapter-core";
import { UrlPlaceholders, UrlPlaceholdersParams } from "./urlPlaceholders";
import { getDynamicDomainHost } from "./getDynamicDomainHost";
import { validateGameCountryRestrictions } from "../../utils/validateCountriesRestrictions";
import { CountrySource } from "../../utils/countrySource";
import config from "../../config";
import { sendPlayerBlockingAnalytics } from "../playerBlockingAnalyticsService";
import { GameUrlStrategy } from "./gameUrlStrategy";
import { ClientPayload } from "./getGameURLInfo";
import { createHash } from "../../utils/hash";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { Models } from "../../models/models";
import { encodeId } from "../../utils/publicid";
import { DynamicDomain } from "../../entities/domain";
import { EntityStaticDomainPoolService } from "../entityStaticDomainPool";
import * as EntityJurisdictionCache from "../../cache/entityJurisdiction";

import logger from "../../utils/logger";
import { DomainAnalyticsData, sendDomainAnalytics } from "../domainAnalyticsService";

const appendQuery = require("append-query");
const uuid = require("uuid");

const LobbyModel = Models.LobbyModel;

const log = logger("get-game-url");

function removeUndefinedFromUrlQueryParams(gameUrl: string): string {
    const url = new URL(gameUrl);

    const keysWithUndefinedValue = [];
    url.searchParams.forEach((val, key) => {
        if (val === "undefined") {
            keysWithUndefinedValue.push(key);
        }
    });
    keysWithUndefinedValue.forEach((key) => {
        url.searchParams.delete(key);
    });

    return url.toJSON();
}

function deleteQueryParamsFromUrl(url: string, paramsToDelete: string[]): string {
    const urlParts = url.split("?");
    const params = new URLSearchParams(urlParts[1]);

    paramsToDelete.forEach(param => params.delete(param));
    return urlParts[0] + "?" + params.toString();
}

export interface InnerStartGameURLInfo {
    urlParams: any;
    tokenData: InnerStartGameTokenData;
}

export type InnerStartGameTokenData = (StartGameTokenData | MerchantStartGameTokenData) & {
    gameGroup?: string;
    sessionId?: string;
};

function appendToQuery(url, params: Record<string, any>): string {
    return url + Object.entries(params).map(entries => entries.map(encodeURIComponent).join("=")).join("&");
}

export abstract class BaseGameUrlStrategy implements GameUrlStrategy {
    private readonly ITG_GAME_PROVIDER_CODE = "ITG";
    protected launchInsideLobby: boolean;
    private readonly entityStaticDomainPoolService: EntityStaticDomainPoolService;

    protected constructor(protected readonly entityGame: EntityGame,
                          protected readonly brand: BrandEntity,
                          protected readonly entitySettings: EntitySettings,
                          isLobby: boolean) {
        this.entityStaticDomainPoolService = new EntityStaticDomainPoolService(this.brand);
        this.launchInsideLobby = !isLobby && this.entityGame.isLiveGame() && this.entitySettings.launchGameInsideLobby;
    }
    
    public async getUrl({ lobbySessionId, isExternalLogin, ...rest }: ClientPayload): Promise<PlayerGameURLInfo> {
        if (this.brand.underMaintenance()) {
            return this.getMaintenanceUrl();
        }
        
        let playMode = rest.playmode || rest.playMode;
        if (playMode === PlayMode.FUN) {
            this.launchInsideLobby = false;
        }
        const lobbyId = await this.getDefaultLobbyId();
        if (!lobbyId) {
            this.launchInsideLobby = false;
        }

        const payload: ClientPayload = this.launchInsideLobby ? {
            isExternalLogin: this.brand.isMerchant,
            isLiveLobby: true,
            ...rest,
        } : rest;
        payload.isLive = this.entityGame.isLiveGame();

        const game = this.entityGame.game;
        const providerGameCode = game.providerGameCode;
        const externalGameId = this.entityGame.externalGameId || providerGameCode || undefined;
        const { urlParams, tokenData: startGameTokenData } = await this.getTokenData(payload);
        const dynamicDomain = await getEntityDomainService(DOMAIN_TYPE.DYNAMIC)
            .get(this.brand, startGameTokenData?.playerCode) as DynamicDomain;


        if (startGameTokenData) {
            if (startGameTokenData.playmode === PlayMode.PLAY_MONEY) {
                playMode = PlayMode.PLAY_MONEY;
            }

            startGameTokenData.envId = dynamicDomain ? dynamicDomain.environment : undefined;
            if (isExternalLogin) {
                startGameTokenData.isExternalLogin = isExternalLogin;
            }
            if (lobbySessionId) {
                startGameTokenData.lobbySessionId = lobbySessionId;
            } else if (this.launchInsideLobby) {
                startGameTokenData.lobbySessionId = startGameTokenData.lobbySessionId
                    || startGameTokenData.sessionId
                    || uuid.v4();
            }

            if (this.entitySettings.env) {
                startGameTokenData.env = this.entitySettings.env;
            }
            if (this.entitySettings.markPlayersAsTest) {
                startGameTokenData.test = true;
            }

            startGameTokenData.externalGameId = externalGameId;
            startGameTokenData.licenseeId = game.features?.licenseeId || config.itgLicenseeId;
        }

        const currency = this.getCurrency(startGameTokenData);

        if (Currencies.value(currency)?.funBonus) {
            playMode = PlayMode.FUN_BONUS;
            startGameTokenData.playmode = playMode;
            if (urlParams.playmode) {
                urlParams.playmode = playMode;
            }
        }
        if (playMode !== PlayMode.BNS && currency === "BNS") {
            return Promise.reject(new Errors.CurrencyNotFoundError(currency));
        }
        if (playMode !== PlayMode.FUN && currency === "FUN") {
            return Promise.reject(new Errors.FunCurrencyPlayedNotInFunModeError());
        }

        await this.validateBonusCoinsAvailable(startGameTokenData, playMode);

        const jurisdiction = await EntityJurisdictionCache.findOne(this.brand);

        if (playMode !== PlayMode.FUN) {
            let countrySource: CountrySource;
            try {
                countrySource = await this.getCountrySource(startGameTokenData, payload.ip, game.isLiveGame());
                await validateGameCountryRestrictions(
                    this.entityGame,
                    this.brand,
                    this.entitySettings,
                    currency,
                    countrySource,
                    jurisdiction
                );
            } catch (err) {
                await sendPlayerBlockingAnalytics(err, countrySource, {
                    initiator: "game-get-url",
                    playerCode: startGameTokenData ? startGameTokenData.playerCode : undefined,
                    currencyCode: currency,
                    ip: payload.ip,
                    operatorCountryCode: startGameTokenData ? startGameTokenData.operatorCountry : undefined,
                    brandId: this.brand.id,
                    gameCode: game.code
                });
                return Promise.reject(err);
            }
        }

        await this.decorateTokenWithDeploymentRoute(startGameTokenData);

        const lobby = urlParams?.lobby || payload.lobby;
        const cashier = urlParams?.cashier || payload.cashier;

        this.decorateTokenWithLobbyAndCashierHashes(startGameTokenData, lobby, cashier);

        const expiresIn = this.entityGame.settings?.startGameTokenExpiresIn;
        const startGameToken = startGameTokenData
                               ? await generateStartGameToken(startGameTokenData, expiresIn)
                               : undefined;

        const dynamicDomainHost = await getDynamicDomainHost(this.entitySettings, dynamicDomain, payload.ip);
        const staticDomainHost = await this.getStaticDomainHost(playMode);
        const language = this.getLanguage(startGameTokenData, payload);
        const gameProviderCode = game.gameProvider?.code;
        const siteCode = gameProviderCode ? this.entitySettings.gameProviderSiteCodes?.[gameProviderCode] : undefined;

        const params: UrlPlaceholdersParams = {
            dynamicDomain: dynamicDomainHost,
            startGameToken,
            staticDomain: staticDomainHost,
            playMode,
            language,
            currency,
            lobby,
            cashier,
            externalGameId,
            providerGameCode,
            siteCode
        };
        let gameUrl = UrlPlaceholders.replace(game.url, params);
        gameUrl = await this.replaceClientVersionWithDeploymentGroup(gameUrl);

        const historyUrlInfo = this.getHistoryUrlInfo(urlParams);

        const { swmpHomeButton, ...queryParams } = UrlPlaceholders.mergeAndReplace([
            this.entitySettings.urlParams,
            this.entityGame.urlParams,
            { language, lobby, cashier },
            urlParams,
            historyUrlInfo
        ], params, { parametersWithUrl: ["lobby", "merch_login_url"] });

        // For ITG games, history_url and history2_url params are not needed, so remove them (SWS-37022)
        if (gameProviderCode === this.ITG_GAME_PROVIDER_CODE) {
            delete queryParams["history_url"];
            delete queryParams["history2_url"];
        }

        if (playMode === PlayMode.PLAY_MONEY && Currencies.value(currency)?.isSocial) {
            queryParams["history"] = 0;
        }

        if (jurisdiction) {
            const paramsToDelete = ["sound_popup", "history2_url"];
            for (const param of paramsToDelete) {
                queryParams[param] = undefined;
            }
            gameUrl = deleteQueryParamsFromUrl(gameUrl, paramsToDelete);
        }

        this.disableCashierAndLobby(queryParams);
        this.addCustomGameSplash(queryParams);
        this.disableBalancePing(queryParams);

        gameUrl = removeUndefinedFromUrlQueryParams(gameUrl);

        UrlPlaceholders.findSocketVersion(gameUrl, queryParams);

        gameUrl = appendQuery(gameUrl, { ...queryParams, language }, { removeNull: true });

        const domainAnalyticsData: DomainAnalyticsData = {
            type: config.analytics.domains.type,
            ts: Date.now(),
            brandId: this.brand.id,
            gameCode: game.code,
            playerCode: startGameTokenData?.playerCode,
            initiator: "game-get-url",
            staticDomain: staticDomainHost,
            dynamicDomain: dynamicDomainHost,
            ehubDomain: this.extractDomain(queryParams.phantom_version_host as string)
        };

        if (this.launchInsideLobby && startGameTokenData) {
            const playerToken = await generatePlayerLoginToken({
                ...startGameTokenData,
                isExternalTerminal: this.brand.isMerchant
            });
            const lobbyDomain = await this.getLobbyDomain();
            const gameUrlParams = new URLSearchParams(gameUrl);
            const url = appendToQuery(`https://${lobbyId}${lobbyDomain}/${game.code}?`, {
                isLive: true,
                lobbyId,
                language,
                token: playerToken,
                lobby: gameUrlParams.get("lobby"),
                url: gameUrl,
                showDesktopHomeButton: !!+gameUrlParams.get("showDesktopHomeButton"),
                swmpHomeButton: !!+swmpHomeButton
            });
            await sendDomainAnalytics({
                ...domainAnalyticsData,
                lobbyDomain
            });
            return {
                url,
                token: playerToken,
                currency,
                staticDomainHost,
                dynamicDomainHost
            };
        }

        await sendDomainAnalytics(domainAnalyticsData);

        return {
            url: gameUrl,
            token: startGameToken,
            currency,
            staticDomainHost,
            dynamicDomainHost
        };
    }

    private extractDomain(url: string): string | undefined {
        if (!url) {
            return;
        }
        try {
            const parsedUrl = new URL(url);
            return parsedUrl.hostname;
        } catch (err) {
            log.error(err, "Failed to extract domain from url. Invalid url.");
        }
    }

    private async getLobbyDomain() {
        let domain = await this.entityStaticDomainPoolService.pickLobbyDomain();
        if (!domain) {
            domain = config.lobbies.domainTemplate;
        }
        if (domain.startsWith(".")) {
            return `${domain}/play`;
        }
        if (!domain.startsWith("-")) {
            domain = `-${domain}`;
        }
        return `${domain}/#/direct-launch`;
    }

    private async getStaticDomainHost(playMode: PlayMode): Promise<string | undefined> {
        if (playMode === PlayMode.FUN && this.entityGame.game.clientFeatures?.funModeDomain) {
            return this.entityGame.game.clientFeatures?.funModeDomain;
        }
        if (this.entityGame.domain) {
            return this.entityGame.domain;
        }
        const pickedStaticDomain = await this.entityStaticDomainPoolService.pickStaticDomain();
        const staticDomain = pickedStaticDomain || await getEntityDomainService(DOMAIN_TYPE.STATIC).get(this.brand);

        return staticDomain?.domain;
    }

    protected abstract validateBonusCoinsAvailable(tokenData: StartGameTokenData | undefined,
                                                   playMode: PlayMode): Promise<void>;

    protected abstract getTokenData(payload: ClientPayload): Promise<InnerStartGameURLInfo>;

    protected abstract getCurrency(tokenData: StartGameTokenData | undefined): string;

    protected abstract getLanguage(tokenData: StartGameTokenData | undefined, payload: ClientPayload): string;

    protected abstract getCountrySource(tokenData: StartGameTokenData | undefined,
                                        ip?: string,
                                        isLiveGame?: boolean): Promise<CountrySource>;

    private getHistoryUrlInfo(adapterUrlParams: Record<string, unknown>) {
        const game = this.entityGame.game;
        let historyUrl: string;
        const key = game.historyRenderType >= 3 ? "history2_url" : "history_url";
        const swHistoryUrl = this.entityGame.urlParams?.[key] || this.entitySettings.urlParams?.[key];
        if (this.entitySettings.skywindHistory) {
            historyUrl = swHistoryUrl;
        } else {
            historyUrl = adapterUrlParams?.[key] || swHistoryUrl;
        }
        // For our ITG game provider, we need to send the itg_history_url parameter
        // Add the history url as itg_history_url only if it is received from the adapter,
        // otherwise don't change anything
        if (game.gameProvider?.code === this.ITG_GAME_PROVIDER_CODE && adapterUrlParams?.history_url) {
            return { itg_history_url: historyUrl };
        }
        return { history_url: historyUrl };
    }

    private getMaintenanceUrl(): PlayerGameURLInfo {
        if (!this.entitySettings.maintenanceUrl) {
            throw new Errors.MaintenanceUrlNotDefinedError();
        }
        return {
            url: this.entitySettings.maintenanceUrl
        };
    }

    private addCustomGameSplash(queryParams: Record<string, any>): void {
        const gameCode = this.entityGame.game.code;
        if (this.entitySettings.gameSplashes && this.entitySettings.gameSplashes[gameCode]) {
            queryParams["splash"] = this.entitySettings.gameSplashes[gameCode];
        }
    }

    // applies custom business logic to game url params
    private disableCashierAndLobby(queryParams: Record<string, any>): void {
        // if there is urlParams.cashier=0 (hide cashier button for mobiles)  present in entity settings
        // then we want to ensure it will stay in url
        if (this.entitySettings.urlParams) {
            if ((this.entitySettings.urlParams as any).cashier === 0) {
                queryParams.cashier = 0;
            }

            if ((this.entitySettings.urlParams as any).lobby === 0) {
                queryParams.lobby = undefined;
                queryParams.lobbyUrl = undefined;
            }
        }
    }

    private disableBalancePing(queryParams: Record<string, any>): void {
        if (queryParams.disableBalancePing) {
            queryParams.disableBalancePing = null;

            if (queryParams.modules) {
                queryParams.modules = queryParams.modules
                    .split(",")
                    .filter(module => module !== "balance")
                    .join(",");
            }

            queryParams.balance_ping = null;
            queryParams.balance_idle = null;
        }
    }

    private async decorateTokenWithDeploymentRoute(token: StartGameTokenData | undefined) {
        if (!token) {
            return;
        }

        const gameGroupId = this.entityGame.game.deploymentGroupId;
        const entityGroupId = this.brand.deploymentGroupId;

        const deploymentGroupService = getDeploymentGroupService();
        token.deployment = await deploymentGroupService.buildDeploymentGroupPath(entityGroupId, gameGroupId);
    }

    private decorateTokenWithLobbyAndCashierHashes(token: StartGameTokenData | undefined,
                                                   lobby: string, cashier: string) {
        if (!token) {
            return;
        }

        const isHashingEnabled = this.entitySettings.hashLobbyAndCashierEnabled;
        const lobbyToHash = lobby || this.entitySettings.urlParams?.lobby;
        if (isHashingEnabled && lobbyToHash) {
            const data = UrlPlaceholders.encodeParameterWithUrl(UrlPlaceholders.decodeParameter(lobbyToHash));
            if (typeof data !== "boolean") {
                token.lb = createHash(data);
                log.info(`Calculate lobby hash [${token.lb}] from ${data}`);
            }
        }

        const cashierToHash = cashier || this.entitySettings.urlParams?.cashier;
        if (isHashingEnabled && cashierToHash !== undefined) {
            const data = UrlPlaceholders.encodeParameterWithUrl(UrlPlaceholders.decodeParameter(cashierToHash));
            if (typeof data !== "boolean") {
                token.csh = createHash(data);
                log.info(`Calculate cashier hash [${token.csh}] from ${data}`);
            }
        }
    }

    private async replaceClientVersionWithDeploymentGroup(url: string): Promise<string> {
        if (!url.includes(UrlPlaceholders.KEY_CLIENT_VERSION)) {
            return url;
        }
        const version = await getGameClientVersionService().getGameClientVersion(this.brand, this.entityGame);
        return url.replace(UrlPlaceholders.CLIENT_VERSION, version);
    }

    private async getDefaultLobbyId(): Promise<string> {
        if (this.launchInsideLobby) {
            const lobby = await LobbyModel.findOne({
                where: {
                    brandId: this.brand.id,
                    isDefault: true
                }
            });
            if (lobby) {
                return encodeId(+lobby.get("id"));
            }
        }
    }
}
