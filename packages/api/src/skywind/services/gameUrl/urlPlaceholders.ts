import * as Errors from "../../errors";
import { URLSearchParams } from "url";

export interface UrlPlaceholdersParams {
    dynamicDomain?: string;
    staticDomain?: string;
    startGameToken?: string;
    playMode?: string;
    language?: string;
    currency?: string;
    lobby?: string;
    cashier?: string;
    externalGameId?: string;
    providerGameCode?: string;
    siteCode?: string;
    externalGameUrl?: string;
    wrapperLauncherVersion?: string;
}

type QueryValue = string | number | boolean;
type Query = Record<string, QueryValue>;

export class UrlPlaceholders {
    public static KEY_CLIENT_VERSION = "{clientVersion}";
    public static KEY_SOCKET_VERSION = "{socketVersion}";
    public static REGEXP = /{.*?}/;

    public static readonly STATIC_DOMAIN = new RegExp("{staticDomain}", "g");
    public static readonly DYNAMIC_DOMAIN = new RegExp("{dynamicDomain}", "g");
    public static readonly SITE_CODE = new RegExp("{siteCode}", "g");
    public static readonly START_GAME_TOKEN = new RegExp("{startGameToken}", "g");
    public static readonly PLAYMODE = new RegExp("{playmode}", "g");
    public static readonly LANGUAGE = new RegExp("{lang}", "g");
    public static readonly CURRENCY = new RegExp("{currency}", "g");
    public static readonly LOBBY = new RegExp("{lobby}", "g");
    public static readonly CASHIER = new RegExp("{cashier}", "g");
    public static readonly LAUNCHER_TOKEN = new RegExp("{launcherToken}", "g");
    public static readonly CLIENT_VERSION = new RegExp(UrlPlaceholders.KEY_CLIENT_VERSION, "g");
    private static readonly EXTERNAL_GAME_ID = new RegExp("{externalGameId}", "g");
    private static readonly PROVIDER_GAME_CODE = new RegExp("{providerGameCode}", "g");
    private static readonly EXTERNAL_GAME_URL = new RegExp("{externalGameUrl}", "g");
    private static readonly WRAPPER_LAUNCHER_VERSION = new RegExp("{wrapperVersion}", "g");

    public static replace(value: string, params: UrlPlaceholdersParams) {
        if (value && value.toString().match(UrlPlaceholders.REGEXP)) {
            value = value
                .replace(UrlPlaceholders.START_GAME_TOKEN, params.startGameToken || "")
                .replace(UrlPlaceholders.PLAYMODE, params.playMode)
                .replace(UrlPlaceholders.LANGUAGE, params.language)
                .replace(UrlPlaceholders.CURRENCY, params.currency)
                .replace(UrlPlaceholders.LOBBY, params.lobby)
                .replace(UrlPlaceholders.CASHIER, params.cashier)
                .replace(UrlPlaceholders.EXTERNAL_GAME_URL, params.externalGameUrl)
                .replace(UrlPlaceholders.WRAPPER_LAUNCHER_VERSION, params.wrapperLauncherVersion);

            if (value.match(UrlPlaceholders.STATIC_DOMAIN)) {
                if (!params.staticDomain) {
                    throw new Errors.StaticDomainNotDefined();
                }
                value = value.replace(UrlPlaceholders.STATIC_DOMAIN, params.staticDomain);
            }

            if (value.match(UrlPlaceholders.DYNAMIC_DOMAIN)) {
                if (!params.dynamicDomain) {
                    throw new Errors.DynamicDomainNotDefined();
                }
                value = value.replace(UrlPlaceholders.DYNAMIC_DOMAIN, params.dynamicDomain);
            }

            if (value.match(UrlPlaceholders.SITE_CODE)) {
                if (!params.siteCode) {
                    throw new Errors.SiteCodeNotDefined();
                }
                value = value.replace(UrlPlaceholders.SITE_CODE, params.siteCode);
            }

            if (value.match(UrlPlaceholders.EXTERNAL_GAME_ID)) {
                if (!params.externalGameId) {
                    throw new Errors.ExternalGameIdNotDefined();
                }
                value = value.replace(UrlPlaceholders.EXTERNAL_GAME_ID, params.externalGameId);
            }

            if (value.match(UrlPlaceholders.PROVIDER_GAME_CODE)) {
                if (!params.providerGameCode) {
                    throw new Errors.ProviderGameCodeNotDefined();
                }
                value = value.replace(UrlPlaceholders.PROVIDER_GAME_CODE, params.providerGameCode);
            }

            value = UrlPlaceholders.encodeITGParameter(value, params.dynamicDomain);
        }
        return UrlPlaceholders.decodeParameter(value);
    }

    private static encodeITGParameter(url: string, dynamicDomain: string): string {
        const ITG_GS_PARAM_REGEXP = /(?<=gs=).[^&]*/;
        const ITG_DYNAMIC_DOMAIN = new RegExp("{itgDynamicDomain}", "g");

        const matchArray = url.match(ITG_GS_PARAM_REGEXP);
        let itgGSUrl = matchArray && matchArray[0];
        if (itgGSUrl && url.match(ITG_DYNAMIC_DOMAIN)) {
            itgGSUrl = itgGSUrl.replace(ITG_DYNAMIC_DOMAIN, dynamicDomain);
            url = url.replace(
                ITG_GS_PARAM_REGEXP,
                UrlPlaceholders.convertToITGGSEncodedFormat(itgGSUrl)
            );
        }
        return url;
    }

    private static convertToITGGSEncodedFormat(url: string): string {
        const rotNum = 13;
        const base64URL = Buffer.from(url).toString("base64");
        let encodedURL = "";
        for (const char of base64URL) {
            const code = char.charCodeAt(0);
            if (65 <= code && code <= 77) {
                encodedURL += String.fromCharCode(code + rotNum);
            } else if (78 <= code && code <= 90) {
                encodedURL += String.fromCharCode(code - rotNum);
            } else {
                encodedURL += String.fromCharCode(code);
            }
        }
        return encodedURL;
    }

    public static decodeParameter(parameter: string) {
        while (parameter && parameter !== decodeURIComponent(parameter)) {
            parameter = decodeURIComponent(parameter);
        }
        return parameter;
    }

    public static mergeAndReplace(paramsToMerge: Record<string, any>[],
                                  urlParams: UrlPlaceholdersParams,
                                  { parametersWithUrl }: { parametersWithUrl?: string | string[] } = {}) {
        const result: Query = {};
        for (const params of paramsToMerge) {
            if (!params) {
                continue;
            }
            for (const field of Object.keys(params)) {
                const value = UrlPlaceholders.replace(params[field], urlParams);
                if (value === undefined) {
                    continue;
                }
                if (field === "modules") {
                    if (result[field] === undefined || typeof result[field] === "string") {
                        result[field] = UrlPlaceholders.getModuleValue(result[field]?.toString(), value);
                    } else {
                        result[field] = "";
                    }
                } else {
                    result[field] = result[field] = value;
                }
            }
        }
        if (parametersWithUrl) {
            const names = Array.isArray(parametersWithUrl) ? parametersWithUrl : [parametersWithUrl];
            for (const name of names) {
                result[name] = UrlPlaceholders.encodeParameterWithUrl(result[name]);
            }
        }
        return result;
    }

    public static encodeParameterWithUrl(param: QueryValue) {
        if (param && typeof param === "string") {
            try {
                const url = new URL(param);
                const searchParams = new URLSearchParams(url.searchParams);
                for (const name of Array.from(url.searchParams.keys())) {
                    url.searchParams.delete(name);
                }
                for (const [key, value] of searchParams) {
                    url.searchParams.append(key, value.trim());
                }
                let href = url.href;
                if (!param.includes("/?") && url.pathname === "/" && url.search) {
                    href = url.origin + url.search;
                }
                if (!param.endsWith("/") && href.endsWith("/")) {
                    return href.slice(0, -1);
                }
                return href;
            } catch (e) {
                // no-op
            }
        }
        return param;
    }

    private static getModuleValue(result: string, values: string): string {
        let uniqueValues: Set<string> = new Set((values || "").split(","));
        if (!result) {
            return [...uniqueValues].join(",");
        }
        uniqueValues = new Set([
            ...uniqueValues,
            ...result.split(",")
        ]);
        return [...uniqueValues].join(",");
    }

    public static findSocketVersion(url: string, queryParams: Record<string, any>): void {
        if (url.includes(UrlPlaceholders.KEY_SOCKET_VERSION)) {
            queryParams["socketVersion"] = null;
        }
        if (queryParams["socketVersion"]) {
            queryParams.sio = queryParams["socketVersion"];
            queryParams["socketVersion"] = null;
        }
    }
}
