import { BrandEntity } from "../../entities/brand";
import * as Errors from "../../errors";
import { EntitySettings } from "../../entities/settings";
import * as EntityJurisdictionCache from "../../cache/entityJurisdiction";

export class EntityHelper {
    public static getAvailableLanguage(brand: BrandEntity, language: string, defaultLanguage: string): string {
        // Check if the language is available and returns this language else returns default
        return brand.languageExists(language) ? language : defaultLanguage;
    }

    public static getAvailableCurrency(brand: BrandEntity, currency: string): string {
        if (!currency) {
            return brand.defaultCurrency;
        }
        if (brand.currencyExists(currency)) {
            return currency;
        }
        throw new Errors.CurrencyNotFoundError(currency);
    }

    public static async getDefaultCountry(
        brand: BrandEntity,
        entitySettings: EntitySettings
    ): Promise<string | undefined | null> {
        const jurisdiction = await EntityJurisdictionCache.findOne(brand);
        if (entitySettings.useCountriesFromJurisdiction) {
            return jurisdiction?.defaultCountry;
        } else {
            return brand.defaultCountry || jurisdiction?.defaultCountry;
        }
    }
}
