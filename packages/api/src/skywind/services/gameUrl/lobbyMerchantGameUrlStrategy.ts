import { EntityGame } from "../../entities/game";
import { BrandEntity } from "../../entities/brand";
import { Merchant } from "../../entities/merchant";
import { PlayerShortLoginTokenData } from "../../utils/token";
import { MerchantGameUrlStrategy } from "./merchantGameUrlStrategy";
import { MerchantGameInitRequest } from "@skywind-group/sw-wallet-adapter-core";
import { EntityHelper } from "./entityHelper";
import { EntitySettings } from "../../entities/settings";
import { InnerStartGameURLInfo } from "./baseGameUrlStrategy";

export class LobbyMerchantGameUrlStrategy extends MerchantGameUrlStrategy {
    constructor(entityGame: EntityGame,
                brand: BrandEntity,
                entitySettings: EntitySettings,
                isLobby: boolean,
                merchant: Merchant,
                private readonly player: PlayerShortLoginTokenData) {
        super(entityGame, brand, entitySettings, isLobby, merchant);
    }

    protected async getTokenData(payload: MerchantGameInitRequest): Promise<InnerStartGameURLInfo> {
        const { urlParams, tokenData } = await this.createGameUrl(payload);
        if (tokenData) {
            const requestCurrency = this.player?.currency || tokenData.currency;
            const requestCountry = tokenData.country || this.player?.country;

            tokenData.operatorCountry = this.validateOperatorCountry(tokenData.operatorCountry || requestCountry);
            tokenData.country = await this.validateCountry(requestCountry, payload.ip);
            tokenData.currency = EntityHelper.getAvailableCurrency(this.brand, requestCurrency);
            tokenData.externalGameId = this.entityGame.externalGameId || undefined;

            if (this.player) {
                tokenData.language = tokenData.language || this.player.language;
                tokenData.gameGroup = this.player.gameGroup;
                tokenData.test = this.player.isTest;
                tokenData.playerCode = this.player.code;
            }
        }
        return { urlParams, tokenData };
    }
}
