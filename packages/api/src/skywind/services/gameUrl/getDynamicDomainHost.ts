import { DomainRouting } from "@skywind-group/sw-domain-routing";
import { EntitySettings } from "../../entities/settings";
import { getIpLocationService } from "../../utils/iplocation";
import logger from "../../utils/logger";
import { DynamicDomain } from "../../entities/domain";

const domainRouting = new DomainRouting();
const log = logger("games-url");

export async function getDynamicDomainHost(settings: EntitySettings, dynamicDomain: DynamicDomain, ip: string) {
    const defaultDynamicDomain: string = dynamicDomain && dynamicDomain.domain;
    if (!ip || !settings.dynamicRoutingEnabled || !dynamicDomain) {
        return defaultDynamicDomain;
    }

    const ipLocation = await getIpLocationService().getLocationByIp(ip);
    if (!ipLocation || !ipLocation.regionCode) {
        return defaultDynamicDomain;
    }

    let dynamicRoute;
    try {
        dynamicRoute = await domainRouting.get({
            countryCode: ipLocation.countryCode,
            regionCode: ipLocation.regionCode,
            environment: dynamicDomain.environment
        });
    } catch (err) {
        log.error(err, "Failed to contact dynamic routing system. Use default domain");
    }

    if (dynamicRoute) {
        log.info("Use dynamic domain routing: ip=%s, country=%s, region=%s, env=%s, domain=%s",
            ip, ipLocation.countryCode, ipLocation.regionCode, dynamicDomain.environment, dynamicRoute);
    }

    return dynamicRoute || defaultDynamicDomain;
}
