import {
    MerchantPlayerGameGroupAttributes,
    MerchantPlayerGameGroupDBInstance
} from "../models/merchantPlayerGameGroup";
import { lazy } from "@skywind-group/sw-utils";
import { GameGroupInfo, MerchantPlayerGameGroupInfo } from "../entities/merchantPlayerGameGroup";
import { GameGroupModel } from "../models/gamegroup";
import { GameGroupNotFound } from "../errors";
import { Models } from "../models/models";

const MerchantPlayerGameGroupModel = Models.MerchantPlayerGameGroupModel;
const ggModel = Models.GameGroupModel;

export interface MerchantPlayerGameGroupService {
    getAll(merchantId: number): Promise<MerchantPlayerGameGroupInfo[]>;
    getSingle(merchantId: number, playerCode: string): Promise<GameGroupInfo>;
    assign(merchantId: number, playerCode: string, gameGroup: string);
    unassign(merchantId: number, playerCode: string, gameGroup: string);
}

const service = lazy<MerchantPlayerGameGroupService>(() => new MerchantPlayerGameGroupServiceImpl());
export const getMerchantPlayerGameGroupService = () => service.get();

export class MerchantPlayerGameGroupImpl {
    private id?: number;
    private merchantId: number;
    private playerCode: string;
    private gameGroupId: number;
    private gameGroup: GameGroupModel;

    constructor(item?: MerchantPlayerGameGroupDBInstance) {
        this.id = item.get("id");
        this.merchantId = item.get("merchantId");
        this.playerCode = item.get("playerCode");
        this.gameGroupId = item.get("gameGroupId");
        this.gameGroup = item.get("gameGroup");
    }

    public toInfo(): MerchantPlayerGameGroupInfo {
        return {
            playerCode: this.playerCode,
            gameGroup: this.gameGroup.get("name") as any
        };
    }

    public toGameGroupInfo(): GameGroupInfo {
        return {
            gameGroup: this.gameGroup.get("name") as any
        };
    }

    public toDBAttributes(): MerchantPlayerGameGroupAttributes {
        return {
            id: this.id,
            merchantId: this.merchantId,
            playerCode: this.playerCode,
            gameGroupId: this.gameGroupId
        };
    }

    public setId(value: number): MerchantPlayerGameGroupImpl {
        this.id = value;
        return this;
    }

    public getId(): number {
        return this.id;
    }

    public setMerchantId(value: number): MerchantPlayerGameGroupImpl {
        this.merchantId = value;
        return this;
    }

    public getMerchantId(): number {
        return this.merchantId;
    }

    public setPlayerCode(value: string): MerchantPlayerGameGroupImpl {
        this.playerCode = value;
        return this;
    }

    public getPlayerCode(): string {
        return this.playerCode;
    }

    public setGameGroupId(value: number): MerchantPlayerGameGroupImpl {
        this.gameGroupId = value;
        return this;
    }

    public getGameGroupId(): number {
        return this.gameGroupId;
    }
}

class MerchantPlayerGameGroupServiceImpl implements MerchantPlayerGameGroupService {
    public async getAll(merchantId: number): Promise<MerchantPlayerGameGroupInfo[]> {
        const dbInstances: MerchantPlayerGameGroupDBInstance[] =
            await MerchantPlayerGameGroupModel.findAll({
            where: {
                merchantId
            },
            include: [
                {
                    model: ggModel,
                    attributes: ["id", "name"],
                    as: "gameGroup",
                }
            ],
        });

        return dbInstances.map(item => new MerchantPlayerGameGroupImpl(item).toInfo());
    }

    public async getSingle(merchantId: number, playerCode: string):
        Promise<GameGroupInfo> {
        const dbInstance: MerchantPlayerGameGroupDBInstance
            = await MerchantPlayerGameGroupModel.findOne({ where: {
                merchantId,
                playerCode
            },
            include: [
                {
                    model: ggModel,
                    attributes: ["id", "name"],
                    as: "gameGroup",
                }
            ],
        });
        if (!dbInstance) {
            return;
        }
        return new MerchantPlayerGameGroupImpl(dbInstance).toGameGroupInfo();
    }

    public async assign(merchantId: number, playerCode: string, gameGroup: string) {
        const dbInstance: MerchantPlayerGameGroupDBInstance
            = await MerchantPlayerGameGroupModel.findOne({ where: {
                merchantId,
                playerCode
            }
        });

        const group = await this.getGameGroupByName(merchantId, gameGroup);

        if (dbInstance) {
            await dbInstance.update({
                gameGroupId: group.get("id")
            });
        } else {
            await MerchantPlayerGameGroupModel.create({
                merchantId,
                playerCode,
                gameGroupId: group.get("id")
            });
        }
    }

    public async unassign(merchantId: number, playerCode: string, gameGroup: string) {
        const group = await this.getGameGroupByName(merchantId, gameGroup);
        const dbInstance: MerchantPlayerGameGroupDBInstance
            = await MerchantPlayerGameGroupModel.findOne({ where: {
                merchantId,
                playerCode,
                gameGroupId: group.get("id")
            }
        });

        if (dbInstance) {
            await dbInstance.destroy();
        }
    }

    private async getGameGroupByName(brandId: number, gameGroup: string): Promise<GameGroupModel> {
        const group = await ggModel.findOne({
            where: {
                name: gameGroup,
                brandId
            }
        });

        if (!group) {
            throw new GameGroupNotFound();
        }

        return group;
    }
}
