import { ChildEntity, EntityInfo, WithBalances } from "../entities/entity";
import { INSUFFICIENT_BALANCE, ITransaction, MAX_CAPACITY_REACHED } from "@skywind-group/sw-wallet";
import { WalletErrors, WalletFacade } from "@skywind-group/sw-management-wallet";
import * as Errors from "../errors";
import { validateMaintenance } from "./entity";

export default class EntityFinance {
    constructor(public entity: ChildEntity) {

    }

    public async credit(currency: string,
                        amount: number,
                        initiatorName?: string
    ): Promise<EntityInfo & WithBalances> {
        this.validation(amount);
        const parent = this.entity.getParent();
        const transaction: ITransaction = await WalletFacade.startTransaction();
        if (initiatorName) {
            transaction.operation.params = { initiatorName: initiatorName };
        }
        await parent.wallet.debit(transaction, currency, amount);
        await this.entity.wallet.credit(transaction, currency, amount);

        await this.commitChanges(transaction);

        return this.entity.toInfoWithBalances();
    }

    public async debit(currency: string,
                       amount: number,
                       initiatorName?: string
    ): Promise<EntityInfo & WithBalances> {
        this.validation(amount);
        const parent = this.entity.getParent();
        const transaction: ITransaction = await WalletFacade.startTransaction();
        if (initiatorName) {
            transaction.operation.params = { initiatorName: initiatorName };
        }
        await this.entity.wallet.debit(transaction, currency, amount);
        await parent.wallet.credit(transaction, currency, amount);

        await this.commitChanges(transaction);

        return this.entity.toInfoWithBalances();
    }

    public validation(amount) {
        if (amount < 0) {
            throw new Errors.AmountIsNegativeError();
        }

        validateMaintenance(this.entity);
        validateMaintenance(this.entity.getParent());
    }

    public async commitChanges(transaction) {
        try {
            await transaction.commit();
        } catch (err) {
            if (err === INSUFFICIENT_BALANCE) {
                return Promise.reject(new WalletErrors.InsufficientEntityBalanceError());
            }
            if (err === MAX_CAPACITY_REACHED) {
                return Promise.reject(new Errors.MaxCapacityReached());
            }
            return Promise.reject(err);
        }
    }
}
