import config from "../config";
import { RolesAccessTokenService } from "./accessToken";
import * as TokenUtils from "../utils/token";
import { token } from "@skywind-group/sw-utils";
import { TokenIsMissing } from "../errors";

/**
 * Authentication/authorization proxy for backend services
 *
 */
export class AuthGateway {
    private readonly rolesTokenService = new RolesAccessTokenService();

    /**
     * Find which service to route the request
     * @param url
     */
    public findRoute(url: string): string {
        const paths = url.split("/");
        const app = paths[0] || paths[1];
        return config.authGateway.routes[app];
    }

    /**
     * Resolve the context path of the routed request
     * @param url
     */
    public resolvePath(url: string): string {
        const paths = url.split("/");
        paths.splice(1, 1);
        return paths.join("/");
    }

    /**
     * Rewrite the toket:
     *  - verify role based token
     *  - create old-fashion token with permissions lists
     * @param accessToken
     */
    public async rewriteAccessToken(accessToken: string): Promise<string> {
        if (accessToken) {
            const info = await this.rolesTokenService.verify(accessToken);
            const accessTokenData = token.parse<TokenUtils.AccessTokenData>(accessToken);
            accessTokenData.grantedPermissions = info.permissions.grantedPermissions;
            delete accessTokenData["iat"];
            delete accessTokenData["exp"];
            delete accessTokenData["iss"];
            return await TokenUtils.generateAccessToken(accessTokenData);
        } else {
            throw new TokenIsMissing();
        }
    }
}

export default new AuthGateway();
