import { literal, QueryTypes, WhereOptions } from "sequelize";
import { BlockedPlayerInfo } from "../entities/player";
import { BrandEntity } from "../entities/brand";
import * as Errors from "../errors";
import { PagingHelper } from "../utils/paginghelper";
import * as FilterService from "./filter";
import { Audit } from "../entities/audit";
import { AuditImpl } from "./audit";
import { getBrandPlayerService, PlayerImpl } from "./brandPlayer";
import * as BlockedPlayersServiceDB from "../models/merchantBlockedPlayer";
import { sequelize as db } from "../storage/db";
import { STATUS_NORMAL, STATUS_SUSPENDED } from "../entities/payment";
import { createPlayerSessionFacade } from "./player/playerSessionFacade";
import { PlayerSessionFacade } from "./player/playerSessionFacade";
import { Models } from "../models/models";

const AuditModel = Models.AuditModel;
const PlayerModel = Models.PlayerModel;
const MerchantBlockedPlayerModel = Models.MerchantBlockedPlayerModel;

const sortableKeys = ["code", "status"];
const DEFAULT_SORT_KEY = "code";

export const queryParamsKeys = [
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
    "code",
    "status",
    "isTest"
];

export default function getBlockPlayerService(brand: BrandEntity): BlockPlayerService {
    const playerSessionService = createPlayerSessionFacade();
    if (brand.isMerchant) {
        return new BlockMerchantPlayerService(brand, playerSessionService);
    } else {
        return new BlockInternalPlayerService(brand, playerSessionService);
    }
}

export interface BlockPlayerService {
    block(playerCode: string, reason?: string): Promise<BlockedPlayerInfo>;
    unblock(playerCode: string): Promise<BlockedPlayerInfo>;
    find(query?: WhereOptions<any>): Promise<BlockedPlayerInfo[]>;
    getPlayerInfo(playerCode: string, fetchAudit?: boolean): Promise<BlockedPlayerInfo>;
}

class BlockMerchantPlayerService implements BlockPlayerService {
    constructor(public brand: BrandEntity,
                private playerSessionService: PlayerSessionFacade = createPlayerSessionFacade()) {
    }

    public async block(playerCode: string, reason: string): Promise<BlockedPlayerInfo> {
        await BlockedPlayersServiceDB.upsertMerchantBlockedPlayer({
            brandId: this.brand.id,
            code: playerCode,
            isBlocked: true
        });

        await this.playerSessionService.kill({brandId: this.brand.id, playerCode, reason});

        return this.toBlockedPlayerInfo(playerCode, STATUS_SUSPENDED);
    }

    public async unblock(playerCode: string): Promise<BlockedPlayerInfo> {
        const result = await MerchantBlockedPlayerModel.update({ isBlocked: false } as any, {
            where: {
                brandId: this.brand.id,
                code: playerCode
            }
        });

        if (this.isFail(result)) {
            return Promise.reject(new Errors.PlayerNotFoundError());
        }

        return this.toBlockedPlayerInfo(playerCode, STATUS_NORMAL);
    }

    private isFail(result) {
        return result[0] === 0;
    }

    public async find(query?: WhereOptions<any>): Promise<BlockedPlayerInfo[]> {
        const updatedQuery = Object.assign({}, query, {
            isBlocked: true,
            brandId: this.brand.id
        });

        const sortBy = FilterService.getSortKey(updatedQuery, sortableKeys, DEFAULT_SORT_KEY);
        const sortOrder = FilterService.valueFromQuery(updatedQuery, "sortOrder") || "ASC";

        return PagingHelper.findAndCountAll(MerchantBlockedPlayerModel, {
            where: updatedQuery,
            offset: FilterService.valueFromQuery(updatedQuery, "offset"),
            limit: FilterService.valueFromQuery(updatedQuery, "limit"),
            order: literal(`"${sortBy}" ${sortOrder}`),
        }, player => (this.toBlockedPlayerInfo(player.code, STATUS_SUSPENDED)));
    }

    public async getPlayerInfo(playerCode: string, fetchAudit?: boolean): Promise<BlockedPlayerInfo> {
        const player = await MerchantBlockedPlayerModel.findOne(
            { where: { brandId: this.brand.id, code: playerCode } });

        if (!player) {
            return Promise.reject(new Errors.PlayerNotFoundError());
        }
        const playerInfo = this.toBlockedPlayerInfo(playerCode, player.status);

        if (fetchAudit) {
            playerInfo.auditData = await fetchAuditByCode(this.brand.id, playerCode);
        }
        return playerInfo;
    }

    private toBlockedPlayerInfo(playerCode: string, status: string): BlockedPlayerInfo {
        return { code: playerCode, brandId: this.brand.id, status };
    }
}

class BlockInternalPlayerService implements BlockPlayerService {
    constructor(public brand: BrandEntity,
                private playerSessionService: PlayerSessionFacade = createPlayerSessionFacade()
    ) {
    }

    public async block(playerCode: string, reason: string): Promise<BlockedPlayerInfo> {
        const info = await this.changePlayerStatus(playerCode, STATUS_SUSPENDED);
        await this.playerSessionService.kill({brandId: this.brand.id, playerCode, reason});
        return info;
    }

    public unblock(playerCode: string): Promise<BlockedPlayerInfo> {
        return this.changePlayerStatus(playerCode, STATUS_NORMAL);
    }

    private async changePlayerStatus(playerCode: string, status: string): Promise<BlockedPlayerInfo> {
        const player = await this.brand.findPlayer({
            code: playerCode
        });

        player.status = status;
        await player.save();

        return this.toBlockedPlayerInfo(playerCode, status);
    }

    public async find(query?: WhereOptions<any>): Promise<BlockedPlayerInfo[]> {
        const updatedQuery = Object.assign({}, query, {
            status: STATUS_SUSPENDED,
            brandId: this.brand.id
        });

        const sortBy = FilterService.getSortKey(updatedQuery, sortableKeys, DEFAULT_SORT_KEY);
        const sortOrder = FilterService.valueFromQuery(updatedQuery, "sortOrder") || "ASC";

        return PagingHelper.findAsyncAndCountAll(PlayerModel, {
            where: updatedQuery,
            offset: FilterService.valueFromQuery(updatedQuery, "offset"),
            limit: FilterService.valueFromQuery(updatedQuery, "limit"),
            order: literal(`"${sortBy}" ${sortOrder}`),
        }, async (player) => {
            const info = await (new PlayerImpl(player, this.brand)).toInfo();
            return this.toBlockedPlayerInfo(info.code, STATUS_SUSPENDED);
        });
    }

    public async getPlayerInfo(playerCode: string, fetchAudit?: boolean): Promise<BlockedPlayerInfo> {
        const player = await getBrandPlayerService().findOneExtended(this.brand, { code: playerCode }, true);
        return player.toInfo() as any;
    }

    private toBlockedPlayerInfo(playerCode: string, status: string): BlockedPlayerInfo {
        return { code: playerCode, brandId: this.brand.id, status };
    }
}

export async function isMerchantPlayerBlocked(brandId: number, playerCode: string): Promise<boolean> {
    const player = await MerchantBlockedPlayerModel.findOne(
        { where: { brandId, code: playerCode, isBlocked: true } });
    return !!player;
}

export const fetchAuditByCode = async (entityId: number, code: string): Promise<Audit[]>  => {
    const auditDBItems = await db.query(`SELECT * FROM audits where entity_id = ${entityId} and ` +
        `history @> '{"parameters": {"code": "${code}"}}'`,
        { model: AuditModel, type: QueryTypes.SELECT, mapToModel: true });

    return auditDBItems.map(dbItem => new AuditImpl(dbItem).toInfo());
};
