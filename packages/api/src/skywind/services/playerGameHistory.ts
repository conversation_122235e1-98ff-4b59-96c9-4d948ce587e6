import * as HistoryService from "../history/gameHistory";
import { appendGameNameToRounds, GameHistorySpinDetails } from "../history/gameHistory";
import { EventHistoryExtraDetails, GameVersionDetails, RoundHistory } from "../entities/gameHistory";
import { verifyGameToken } from "./playService";
import { Op, WhereOptions } from "sequelize";
import { parseFilter } from "./filter";
import EntityCache from "../cache/entity";
import { BrandEntity } from "../entities/brand";
import { GameHistorySpin, getSpinHistoryByRound } from "../history/spinHistory";
import { findOne } from "./entity";
import { findAllEntityGameInfos } from "./game";
import { GAME_TYPES } from "../utils/common";
import { verifyReplayToken } from "../utils/token";
import { decodeId } from "../utils/publicid";

export async function getPlayerRoundsHistory(reqQuery: any): Promise<RoundHistory[]> {
    const gameTokenData = await verifyGameToken(reqQuery.gameToken);
    removeEmptyRoundsFromQuery(reqQuery);
    const query: WhereOptions<any> = parseFilter(reqQuery, ["roundId", "ts", "sortOrder", "limit", "offset"]);
    query["playerCode"] = gameTokenData.playerCode;

    if (reqQuery.roundsForLiveGames === "true") {
        const brand = await findOne({ id: gameTokenData.brandId });
        const queries = new Map();
        queries.set("game", { type: GAME_TYPES.live });
        const games = await findAllEntityGameInfos(brand, queries, true);
        query["gameCode"] = { [Op.in]: games.map(game => game.code) };
        const rounds = await HistoryService.findGameHistoryEntries(gameTokenData.brandId, query);

        return appendGameNameToRounds(rounds, games);
    } else {
        query["gameCode"] = gameTokenData.gameCode;

        return HistoryService.findGameHistoryEntries(gameTokenData.brandId, query);
    }
}

export async function getPlayerRoundEventsHistory(reqQuery: any, roundId: number): Promise<GameHistorySpin[]> {
    const gameTokenData = await verifyGameToken(reqQuery.gameToken);
    const entity = await EntityCache.findOne({ id: gameTokenData.brandId });
    return getSpinHistoryByRound(entity as BrandEntity,
        roundId,
        reqQuery,
        gameTokenData.playerCode);
}

export async function getPlayerEventDetails(gameToken: string,
                                            roundId: number,
                                            eventId: number): Promise<EventHistoryExtraDetails> {
    const gameTokenData = await verifyGameToken(gameToken);
    const entity = await EntityCache.findOne({ id: gameTokenData.brandId });
    return HistoryService.getGameHistoryDetails(entity, roundId, eventId, {
        playerCode: gameTokenData.playerCode,
        addJurisdictionSettings: true
    });
}

function removeEmptyRoundsFromQuery(reqQuery: any) {
    if (!reqQuery) {
        return;
    }
    for (const ind of Object.keys(reqQuery)) {
        if (!ind.startsWith("roundId")) {
            continue;
        }

        if (!(Number(reqQuery[ind]) > 0)) {
            delete reqQuery[ind];
        } else {
            reqQuery[ind] = parseInt(reqQuery[ind], 10);
        }
    }
}

export async function getPlayerEventsHistory(reqQuery: any): Promise<GameHistorySpinDetails[]> {
    const gameTokenData = await verifyGameToken(reqQuery.gameToken);
    const entity = await EntityCache.findOne({ id: gameTokenData.brandId });
    return HistoryService.getEvents(entity, {
        ...reqQuery,
        gameCode: gameTokenData.gameCode,
        playerCode: gameTokenData.playerCode
    });
}

export async function getReplayHistory(reqQuery: any): Promise<GameHistorySpinDetails[]> {
    const { brandId, roundId, gameCode } = await verifyReplayToken(reqQuery.replayToken);
    const entity = await EntityCache.findOne({ id: decodeId(brandId) });
    return HistoryService.getEvents(
        entity,
        {
            ...reqQuery,
            gameCode,
            roundId: decodeId(roundId),
        },
        { isReplayMode: true }
    );
}

export async function getGameVersion(reqQuery: any): Promise<GameVersionDetails> {
    const gameTokenData = await verifyGameToken(reqQuery.gameToken);

    return HistoryService.getGameVersion(gameTokenData.gameCode, reqQuery.gameVersion, gameTokenData.brandId);
}

export async function getReplayGameVersion({ replayToken, gameVersion }: any): Promise<GameVersionDetails> {
    const { brandId, currency, gameCode } = await verifyReplayToken(replayToken);

    return HistoryService.getGameVersionForReplayMode(
        gameCode,
        gameVersion,
        decodeId(brandId),
        currency
    );
}
