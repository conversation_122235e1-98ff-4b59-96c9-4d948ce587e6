import { BaseEntity, ChildEntity } from "../entities/entity";
import { getChildEntities } from "./entity";
import { sequelize as db } from "../storage/db";
import { Transaction } from "sequelize";
import { COUNTRIES } from "../utils/common";
import { getEntitySettings } from "./settings";
import * as Errors from "../errors";
import { validateCountryRestrictions } from "../utils/validateEntityCountries";

interface CountryInfo {
    displayName: string;
    code: string;
}

export default class EntityCountryService {
    constructor(public entity: BaseEntity) {
    }

    public getList(): Array<CountryInfo> {
        const entityCountries: Array<string> = this.entity.getCountries();

        return entityCountries.map(c => {
            return { displayName: COUNTRIES[c], code: c };
        });
    }

    public async add(codeOrCodes: string | string[]): Promise<BaseEntity> {
        const countries = Array.isArray(codeOrCodes) ? codeOrCodes : [codeOrCodes];
        const newCountries = countries.filter(code => !this.entity.countryExists(code));
        if (newCountries.length) {
            const entitySettings = await getEntitySettings(this.entity.path);
            if (entitySettings.useCountriesFromJurisdiction) {
                throw new Errors.ValidationError("countries can be updated only for useCountriesFromJurisdiction=false");
            }
            const parent = (this.entity as ChildEntity).getParent();
            for (const code of newCountries) {
                validateCountryRestrictions(code, parent, undefined, entitySettings, "Country");
                this.entity.addCountry(code);
            }
            return this.entity.save();
        }
        return this.entity;
    }

    public async remove(codeOrCodes: string | string[], force?: boolean): Promise<BaseEntity> {
        return db.transaction(async (transaction: Transaction): Promise<any> => {
            const countries = Array.isArray(codeOrCodes) ? codeOrCodes : [codeOrCodes];

            if (force) {
                const children = getChildEntities(this.entity)
                    .filter(entity => !!countries.find(country => entity.countryExists(country)));

                const reversedChildren = children.reverse();

                reversedChildren.forEach(entity => countries.forEach(country => entity.removeCountry(country)));

                for (const child of reversedChildren) {
                    await child.save(transaction);
                }
            }

            countries.forEach(country => this.entity.removeCountry(country));

            return this.entity.save(transaction);
        });
    }
}
