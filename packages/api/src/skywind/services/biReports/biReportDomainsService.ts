import { BiReportDomains, CreateBiReportDomains, UpdateBiReportDomains } from "../../entities/biReportDomains";
import { BiReportDomainsDBInstance } from "../../models/biReportDomains";
import { BiReportDomainsImpl } from "./biReportDomains";
import { literal, Transaction, WhereOptions } from "sequelize";
import * as FilterService from "../filter";
import { PagingHelper } from "../../utils/paginghelper";
import { sequelize as db } from "../../storage/db";
import { ApplicationLock, ApplicationLockId } from "../../utils/applicationLock";
import config from "../../config";
import {
    CreateReportDomainsError,
    DeleteReportDomainsError,
    ResourceNotFoundError,
    ValidationError
} from "../../errors";
import { Models } from "../../models/models";

const sortableKeys = ["id", "createdAt", "updatedAt"];
const DEFAULT_SORT_KEY = "createdAt";

export const queryParamsKeys = [
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
];

const BiReportDomainsModel = Models.BiReportDomainsModel;

// TODO: maybe make it common
export interface TransactionOptions {
    transaction?: Transaction;
    lock?: any;
}

export interface DeleteBiReportDomainsOptions {
    forceDelete?: boolean;
}

export interface BiReportDomainsService {
    createOne(data: CreateBiReportDomains): Promise<BiReportDomains>;

    getOne(id: number, options?: TransactionOptions): Promise<BiReportDomains>;

    getMany(query: WhereOptions<any>, options?: TransactionOptions): Promise<BiReportDomains[]>;

    updateOne(id: number, data: UpdateBiReportDomains, options?: TransactionOptions): Promise<BiReportDomains>;

    selectOne(id: number): Promise<BiReportDomains>;

    deleteOne(id: number, options: DeleteBiReportDomainsOptions): Promise<void>;
}

export class BiReportDomainsServiceImpl implements BiReportDomainsService {
    private readonly limit = config.bi.domainsListLimit;

    public async createOne(data: CreateBiReportDomains): Promise<BiReportDomains> {
        try {
            return await db.transaction(async (transaction) => {
                const instance = await BiReportDomainsModel.create(data, { transaction });
                return new BiReportDomainsImpl(instance);
            });
        } catch (error) {
            throw new CreateReportDomainsError(error.message);
        }
    }

    public async getOne(id: number, options: TransactionOptions = {}): Promise<BiReportDomains> {
        const instance = await BiReportDomainsModel.findByPk(id, options);

        if (!instance) {
            throw new ResourceNotFoundError(`Report domains not found by id ${id}`);
        }
        return new BiReportDomainsImpl(instance);
    }

    public async getMany(query: WhereOptions = {},
                         options: TransactionOptions = {}): Promise<BiReportDomains[]> {
        const sortBy: string = FilterService.getSortKey(query, sortableKeys, DEFAULT_SORT_KEY);
        const sortOrder: string = FilterService.valueFromQuery(query, "sortOrder") || "DESC";

        return PagingHelper.findAndCountAll<BiReportDomains, BiReportDomainsDBInstance, BiReportDomains>(
            BiReportDomainsModel,
            {
                where: query as any,
                offset: FilterService.valueFromQuery(query, "offset"),
                limit: FilterService.valueFromQuery<any>(query, "limit") || this.limit,
                order: literal(`"${sortBy}" ${sortOrder}`),
                lock: options.lock,
                transaction: options.transaction
            },
            (instance) => {
                return new BiReportDomainsImpl(instance);
            });
    }

    public async updateOne(id: number,
                           data: UpdateBiReportDomains,
                           options: TransactionOptions = {}): Promise<BiReportDomains> {
        const biReportDomains = await this.getOne(id);
        biReportDomains.trustServerUrl = data.trustServerUrl || biReportDomains.trustServerUrl;
        biReportDomains.baseUrl = data.baseUrl || biReportDomains.baseUrl;
        biReportDomains.updatedAt = new Date();

        return await db.transaction(async (transaction) => {
            await biReportDomains.save(transaction);
            return biReportDomains;
        });
    }

    /**
     *  Selects only one a set of the domains to be used for generating BI reports.
     *  Concurrent requests to perform this operation will be put on hold until unlock.
     */
    public async selectOne(id: number): Promise<BiReportDomains> {
        return await db.transaction(async (transaction) => {
            await ApplicationLock.lock(transaction, ApplicationLockId.SELECT_BI_REPORT_DOMAINS);

            const instance = await BiReportDomainsModel.findOne({
                where: { isSelected: true },
                limit: 1,
                transaction,
                lock: transaction.LOCK.SHARE
            });

            if (instance) {
                const selectedBiReportDomains = new BiReportDomainsImpl(instance);
                selectedBiReportDomains.isSelected = false; // unselect current domains
                await selectedBiReportDomains.save(transaction);
            }
            const biReportDomainsToSelect = await this.getOne(id, {
                transaction,
                lock: transaction.LOCK.SHARE
            });
            biReportDomainsToSelect.isSelected = true; // select another domains
            await biReportDomainsToSelect.save(transaction);

            return biReportDomainsToSelect;
        });
    }

    public async deleteOne(id: number, options: DeleteBiReportDomainsOptions = {}) {
        const biReportDomains = await this.getOne(id);

        if (biReportDomains.isSelected && !options.forceDelete) {
            throw new ValidationError("Forbid to delete selected bi report domains");
        }

        try {
            return await db.transaction(async (transaction) => {
                await BiReportDomainsModel.destroy({ where: { id }, transaction });
            });
        } catch (error) {
            throw new DeleteReportDomainsError(error.message);
        }
    }
}

const biReportDomainsService = new BiReportDomainsServiceImpl();

export function getBiReportDomainsService(): BiReportDomainsService {
    return biReportDomainsService;
}
