import { generatePlayerLoginToken } from "../utils/token";
import { EmailData } from "../utils/emails";
import { Player, PlayerInfo } from "../entities/player";
import * as Errors from "../errors";
import { BaseEntity } from "../entities/entity";
import * as SecurityService from "./security";
import { Captcharized, SECURITY_AUTH_TYPE } from "./security";
import config from "../config";
import { getBrandPlayerService, PlayerImpl } from "./brandPlayer";
import { getEntitySettings } from "./settings";
import { EntitySettings } from "../entities/settings";
import { createPlayerSessionFacade } from "./player/playerSessionFacade";
import { getEmailService } from "./email";
import { getBrandPlayerValidator } from "./brandPlayerValidator";
import { Models } from "../models/models";

const PlayerModel = Models.PlayerModel;
const PlayerResetPasswordModel = Models.PlayerPasswordReset;

const resetPasswordMailBody = `
{{username}},<br />
To reset your password, please use the following link: <a href="{{redirectToUrl}}{{guid}}">click to reset</a>.
It will expire in {{minutes}} minutes — after that you'll need to request a new one. <br />
If you didn't request this change, please let us know by replying to this email.`;

const resetPasswordConfig: EmailData = {
    fromEmail: "Skywind <<EMAIL>>",
    subject: "Player Password Reset",
    htmlPart: resetPasswordMailBody,
};

export interface LoginData extends Captcharized {
    code: string;
    password: string;
    userId?: number;
    force?: boolean; // skip exists session
}

export interface LoginInfo {
    code?: string;
    token?: string;
    isPasswordTemp?: boolean;
    csrfToken?: string;
    img?: string;
}

export async function createPlayerLoginInfo(player: Player,
                                            additionalData: object = {},
                                            tokenConfig?: any): Promise<LoginInfo> {
    const sessionId = require("uuid").v4();

    const token: string = await generatePlayerLoginToken({
        playerCode: player.code,
        brandId: player.brandId,
        sessionId: sessionId,
        gameGroupId: player.gamegroupId,
        gameGroup: player.gamegroupName,
        currency: player.currency,
        test: player.isTest,
        ...additionalData,
    }, tokenConfig);

    await createPlayerSessionFacade().create(
        {
            brandId: player.brandId,
            playerCode: player.code,
            playerId: player.id,
            sessionId: sessionId
        }
    );

    return {
        code: player.code,
        token: token,
        isPasswordTemp: !!player.isPasswordTemp,
    };
}

async function removePreviousRequests(player: Player): Promise<void> {
    await PlayerResetPasswordModel.destroy({
        where: { playerId: player.id },
    });
}

export async function resetPlayerPassword(brand: BaseEntity, playerData: any): Promise<void> {
    const playerInstance = await PlayerModel.findOne({
            where: {
                brandId: brand.id,
                email: playerData.email.toLowerCase(),
            },
        }
    );
    if (!playerInstance) {
        return Promise.reject(new Errors.PlayerNotFoundError());
    }

    const player: Player = new PlayerImpl(playerInstance);

    getBrandPlayerValidator().validatePlayerSuspended(player);
    await removePreviousRequests(player);
    const expiredAt = new Date();
    expiredAt.setSeconds(expiredAt.getSeconds() + config.playerResetPassword.expiresIn);

    const resetPasswordInfo = await PlayerResetPasswordModel.create({
        playerId: player.id,
        expiredAt: expiredAt,
    });

    await getEmailService().sendEmail([player.email], resetPasswordConfig, {
        username: player.firstName || "Player",
        guid: resetPasswordInfo.get("guid"),
        redirectToUrl: playerData.redirectTo || config.playerResetPassword.redirectTo,
        minutes: Math.round(config.playerResetPassword.expiresIn / 60)
    });

    return;
}

export async function playerPasswordConfirm(brand: BaseEntity, playerData: any): Promise<void> {
    const settings: EntitySettings = await getEntitySettings(brand.path);
    const passwordIsValid = await getBrandPlayerService().validatePlayerPassword(settings, playerData.password);
    await getBrandPlayerService().validationPasswordError(passwordIsValid);

    const resetPasswordInstance = await PlayerResetPasswordModel.findOne({
        where: { guid: playerData.guid },
    });
    if (!resetPasswordInstance) {
        return Promise.reject(new Errors.ResetPasswordConfirmError());
    }
    await PlayerResetPasswordModel.destroy({
        where: { guid: playerData.guid },
    });

    const now = new Date();
    if (resetPasswordInstance.get("expiredAt") < now) {
        return Promise.reject(new Errors.ResetPasswordLinkExpired());
    }

    const updatePasswordResult = await getBrandPlayerService().changePlayerPassword(
        brand,
        playerData.password,
        { isPasswordTemp: false },
        { id: resetPasswordInstance.get("playerId") }
    );

    if (!updatePasswordResult) {
        return Promise.reject(new Errors.PlayerNotFoundError());
    }
    return;
}

const DIGITS = "123456789";
const LETTERS = "abcdefghijklmnpqrstuvwxyz";

/*
 * change symbols in template by:
 * 0 - any digit
 * A - any GIG letter
 * a - any small letter
 * */
function generateTemporaryPassword(template: string): string {
    return template
        .split("")
        .map(replaceToRandomCharacter)
        .join("");
}

function replaceToRandomCharacter(character: string): string {
    switch (character) {
        case "0":
            return DIGITS[DIGITS.length * Math.random() | 0];
        case "a":
            return LETTERS[LETTERS.length * Math.random() | 0];
        case "A":
            return LETTERS[LETTERS.length * Math.random() | 0].toUpperCase();
        default:
            return character;
    }
}

export async function setTemporaryPassword(brand: BaseEntity, playerCode: string): Promise<any> {
    await getBrandPlayerService().getPlayer(brand.id, playerCode);

    const temporaryPassword: string = generateTemporaryPassword("AAaa0000A");

    const updatePasswordResult = await getBrandPlayerService().changePlayerPassword(
        brand,
        temporaryPassword,
        { isPasswordTemp: true },
        { code: playerCode }
    );

    if (!updatePasswordResult) {
        return Promise.reject(new Errors.PlayerNotFoundError());
    }
    return { temporaryPassword: temporaryPassword };
}

export async function updatePlayerPassword(brand: BaseEntity, playerCode: string, playerData: any,
                                           force?: boolean): Promise<PlayerInfo> {

    const player: Player = await getBrandPlayerService().getPlayer(brand.id, playerCode);

    if (!player.password && !force) {
        return Promise.reject(new Errors.PlayerCreatedWithoutPassword());
    }
    if (force && player.password) {
        return Promise.reject(new Errors.PasswordAlreadyExists());
    }

    await SecurityService.checkBlockedPasswordChange(brand.key, playerCode, SECURITY_AUTH_TYPE.PLAYER);
    if (playerData.newPassword !== playerData.confirmPassword) {
        return Promise.reject(new Errors.PasswordNotConfirm());
    }
    const settings: EntitySettings = await getEntitySettings(brand.path);
    const passwordIsValid = await getBrandPlayerService().validatePlayerPassword(settings, playerData.newPassword);
    await getBrandPlayerService().validationPasswordError(passwordIsValid);

    if (playerData.oldPassword) {
        const encryptedOldPassword: string = await SecurityService.encryptPassword(player.salt, playerData.oldPassword);
        if (encryptedOldPassword !== player.password) {
            await SecurityService.incrementPasswordChangeFail(brand.key, playerCode, SECURITY_AUTH_TYPE.PLAYER);
            return Promise.reject(new Errors.PasswordDoesntMatch());
        }
    }

    if (playerData.oldPassword === playerData.newPassword) {
        return Promise.reject(new Errors.PasswordNotUnique());
    }

    const updatePasswordResult = await getBrandPlayerService().changePlayerPassword(
        brand,
        playerData.newPassword,
        { isPasswordTemp: settings?.isPlayerPasswordChangeEnabled },
        { code: playerCode }
    );

    if (!updatePasswordResult) {
        return Promise.reject(new Errors.PasswordHasNotChanged());
    }
    return player.toInfo();
}

export async function checkPlayerCodeIsTaken(brand: BaseEntity, playerCode): Promise<void> {
    const playerInstance = await PlayerModel.findOne({
            where: {
                brandId: brand.id,
                code: playerCode
            },
        }
    );
    if (playerInstance) {
        return Promise.reject(new Errors.PlayerCodeIsTaken());
    }
}

export async function checkPlayerMailIsTaken(brand: BaseEntity, email: string): Promise<void> {
    const playerInstance = await PlayerModel.findOne({
            where: {
                brandId: brand.id,
                email
            },
        }
    );
    if (playerInstance) {
        return Promise.reject(new Errors.EmailAlreadyExistError());
    }
}
