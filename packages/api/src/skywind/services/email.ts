import { lazy } from "@skywind-group/sw-utils";
import { EmailData, getDefaultEmailService } from "../utils/emails";

export interface TemplateOptions {
    [field: string]: string | number;
}

export interface EmailService {
    sendEmail(recipients: string[], emailContent: EmailData, options?: TemplateOptions);
}

class EmailServiceImpl implements EmailService {

    public sendEmail(recipients: string[], emailContent: EmailData, options: TemplateOptions  = {}) {
        const sender = getDefaultEmailService();
        if (!emailContent.fromEmail) {
            emailContent.fromEmail = sender.getFromEmail();
        }
        if (!emailContent.fromName) {
            emailContent.fromName = emailContent.fromEmail;
        }

        for (const [key, value] of Object.entries(options)) {
            const regExpTemplate = new RegExp(`\{\{${key}}}`, "gm");
            emailContent.htmlPart = emailContent.htmlPart.replace(regExpTemplate, `${value}`);
        }

        return sender.sendEmail(recipients, emailContent);
    }
}

const emailService = lazy<EmailService>(() => new EmailServiceImpl());

export function getEmailService(): EmailService {
    return emailService.get();
}
