import { kafka, lazy, logging } from "@skywind-group/sw-utils";
import config from "../config";
import * as Errors from "../errors";
import { CountrySource } from "../utils/countrySource";

export interface PlayerBlockingAnalyticsParams {
    readonly brandId?: number;
    readonly playerCode?: string;
    readonly gameCode?: string;
    readonly ip?: string;
    readonly currencyCode?: string;
    readonly initiator: "game-get-url" | "game-start" | "player-login";
    readonly operatorCountryCode?: string;
    readonly reason?: string;
}

export interface PlayerBlockingAnalyticsData {
    readonly type?: string;
    readonly ts?: number;
    readonly reason?: string;
    readonly brandId?: number;
    readonly playerCode?: string;
    readonly gameCode?: string;
    readonly ip?: string;
    readonly countryCode?: string;
    readonly currencyCode?: string;
    readonly initiator: "game-get-url" | "game-start" | "player-login";
    readonly ipCountryCode?: string;
    readonly operatorCountryCode?: string;
    readonly countrySource?: "operator" | "ip";
}

class PlayerBlockingAnalyticsService {
    private kafkaWriter: kafka.KafkaWriter;
    private readonly logger = logging.logger("analytics:player-blocking");

    public async send(data: PlayerBlockingAnalyticsData): Promise<void> {
        if (config.analytics.on && config.analytics.playerBlocking.on) {
            if (!this.kafkaWriter) {
                this.kafkaWriter = await kafka.createWriter(config.analytics.kafka, this.logger);
            }
            return this.kafkaWriter.sendMessages([JSON.stringify(data)]);
        }
    }
}

function getReason(err: any): string | null {
    if (err instanceof Errors.IpLocationError) {
        return "ip-lookup-error";
    }
    if (err instanceof Errors.UnknownIpAddress) {
        return "unknown-ip-address";
    }
    if (err instanceof Errors.ValidationError) {
        return "misconfigured-restricted-countries";
    }
    if (err instanceof Errors.CurrencyIsRestricted) {
        return "currency-restricted";
    }
    if (err instanceof Errors.CountryIsRestricted) {
        return "country-restricted";
    }
    return null;
}

const service = lazy(() => new PlayerBlockingAnalyticsService());

export async function sendPlayerBlockingAnalytics(
    err: any,
    countrySource: CountrySource | undefined,
    data: PlayerBlockingAnalyticsParams
): Promise<void> {
    const reason = getReason(err);
    if (reason) {
        return service.get().send({
            type: config.analytics.playerBlocking.type,
            ts: Date.now(),
            reason,
            countryCode: countrySource?.code,
            countrySource: countrySource?.source === "operator" ? "operator" : "ip",
            ipCountryCode: countrySource?.ipCountryCode,
            ...data,
        });
    }
}
