import { BaseEntity } from "../entities/entity";
import * as MerchantTypesCache from "../cache/merchantTypes";
import { lazy } from "@skywind-group/sw-utils";
import { MerchantTypeDBInstance } from "../models/merchantType";
import { MerchantTypeExists, MerchantTypeNotFound, ValidationError } from "../errors";
import { Models } from "../models/models";
import { MerchantType, MerchantTypeUpdate, SchemaObject } from "../entities/merchantType";

const MerchantTypeModel = Models.MerchantTypeModel;

export class MerchantTypeImpl implements MerchantType {
    public type: string;
    public url?: string;
    public mainDomainUrl?: string;
    public schema: SchemaObject;

    constructor(item?: MerchantTypeDBInstance) {
        if (!item) {
            return;
        }

        this.type = item.get("type");
        this.url = item.get("url");
        this.mainDomainUrl = item.get("mainDomainUrl");
        this.schema = item.get("schema");
    }

    public get isInternalAdapter(): boolean {
        return !this.url;
    }

}

interface EntityMerchantTypesService {
    getList(entity: BaseEntity): Promise<MerchantType[]>;

    findOne(entity: BaseEntity, type: string): Promise<MerchantType>;

    add(entity: BaseEntity, merchantTypesOrType: string | string[]): Promise<BaseEntity>;

    remove(entity: BaseEntity, merchantTypesOrType: string | string[]): Promise<BaseEntity>;
}

const containerEntityMerchantType = lazy<EntityMerchantTypesService>(() => new EntityMerchantTypeServiceImpl());

export function getEntityMerchantTypeService(): EntityMerchantTypesService {
    return containerEntityMerchantType.get();
}

export class EntityMerchantTypeServiceImpl implements EntityMerchantTypesService {

    public async getList(entity: BaseEntity): Promise<MerchantType[]> {
        const availableMerchantTypes: Array<string> = await entity.getMerchantTypes();
        const allMerchantTypes: MerchantType[] = await MerchantTypesCache.findAll();

        return allMerchantTypes.filter(typeInfo => availableMerchantTypes.includes(typeInfo.type));
    }

    public async findOne(entity: BaseEntity, type: string): Promise<MerchantType> {
        const availableMerchantTypes: Array<string> = await entity.getMerchantTypes();
        const merchantType: MerchantType = await MerchantTypesCache.findOne(type);

        if (!merchantType || !availableMerchantTypes.includes(type)) {
            return Promise.reject(new MerchantTypeNotFound(type));
        }

        return merchantType;
    }

    public async add(entity: BaseEntity, merchantTypesOrType: string | string[]): Promise<BaseEntity> {
        await entity.addMerchantType(merchantTypesOrType);
        return entity.save();
    }

    public async remove(entity: BaseEntity, merchantTypesOrType: string | string[]): Promise<BaseEntity> {
        await entity.removeMerchantType(merchantTypesOrType);
        return entity.save();
    }
}

export interface MerchantTypeService {
    list(): Promise<MerchantType[]>;

    create(merchantTypeInfo: MerchantType): Promise<MerchantType>;

    update(merchantType: string, merchantTypeInfo: MerchantTypeUpdate): Promise<MerchantType>;
}

const containerMerchantType = lazy<MerchantTypeService>(() => new MerchantTypeServiceImpl());

export function getMerchantTypeService(): MerchantTypeService {
    return containerMerchantType.get();
}

export default class MerchantTypeServiceImpl implements MerchantTypeService {
    public async list(): Promise<MerchantType[]> {
        return MerchantTypesCache.findAll();
    }

    public async create(createData: MerchantType): Promise<MerchantType> {
        try {
            const merchantType = await MerchantTypesCache.findOne(createData.type);
            if (merchantType) {
                return Promise.reject(new MerchantTypeExists(createData.type));
            }

            this.validate(createData, true);

            const newType = await MerchantTypeModel.create({
                type: createData.type,
                url: createData.url,
                mainDomainUrl: createData.mainDomainUrl,
                schema: createData.schema
            });
            return new MerchantTypeImpl(newType);
        } finally {
            MerchantTypesCache.reset();
        }

    }

    public async update(type: string, updateData: MerchantTypeUpdate): Promise<MerchantType> {
        try {
            const merchantType: MerchantTypeImpl = await MerchantTypesCache.findOne(type);
            if (!merchantType) {
                return Promise.reject(new MerchantTypeNotFound(type));
            }

            this.validate(updateData);

            merchantType.url = updateData.url === null ? null : updateData.url || merchantType.url;
            merchantType.mainDomainUrl = updateData.mainDomainUrl === null ?
                                         null :
                                         updateData.mainDomainUrl || merchantType.mainDomainUrl;
            merchantType.schema = updateData.schema || merchantType.schema;

            await MerchantTypeModel.update(merchantType, { where: { type } });
            return merchantType;
        } finally {
            MerchantTypesCache.reset();
        }
    }

    private validate(data: MerchantTypeUpdate, isCreate?: boolean) {
        if (data.schema && typeof data.schema !== "object") {
            throw new ValidationError("Schema must be an object");
        }
        if (isCreate && !data.schema) {
            throw new ValidationError("Schema is required");
        }
    }
}
