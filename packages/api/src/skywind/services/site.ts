import { Site, CreateData } from "../entities/site";
import { PlayerGameURLInfo } from "../entities/game";
import EntityCache from "../cache/entity";
import * as UrlManager from "./urlManager";
import { Models } from "../models/models";

export class SiteService {
    public static model = Models.SiteModel;

    /**
     * list - get list of all existing sites
     *
     * @returns {Promise<Site[]>}
     */
    public static async list(): Promise<Site[]> {
        const items = await this.model.findAll();
        return items.map(site => site.toInfo());
    }

    /**
     * create - function to create new site
     *
     * @param data - {
     *                  name: string;
     *                  description?: string;
     *                  type: string;
     *               }
     *
     * @returns {Promise<Site>}
     */
    public static async create(data: CreateData): Promise<Site> {
        const site = await this.model.create(data);
        return site.toInfo();
    }

    public static async getFunGameURLForSite(brandId: number, gameCode: string, ip, platform): Promise<PlayerGameURLInfo> {
        const entity = await EntityCache.findOne({ id: brandId });
        return UrlManager.getAnonymousGameURL({ keyEntity: entity, gameCode, ip });
    }
}
