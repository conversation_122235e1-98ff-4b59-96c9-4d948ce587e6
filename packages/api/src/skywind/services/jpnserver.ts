import { keepalive } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import config from "../config";
import * as Errors from "../errors";
import logger from "../utils/logger";
import {
    JackpotAudit,
    JackpotInstance,
    JackpotTicker,
    JackpotType,
    RegisterJackpotInstanceRequest
} from "../entities/jackpot";
import { X_ACCESS_TOKEN } from "../utils/common";
import { measure } from "../utils/measures";

const log = logger("jpn-server");

export class JPNServer {
    private static JACKPOT_INSTANCES_URL = "/jackpots/";
    private static JACKPOT_TYPES_URL = "/types/";
    private static JACKPOT_TICKER_URL = "/ticker/";
    private static JACKPOT_AUDITS_URL = "/audits/";

    constructor(private baseUrl: string, private baseTickerUrl: string) {
    }

    @measure({ name: "JPNServer.getJackpots", isAsync: true })
    public getJackpots(jackpotIds: string[], token: string): Promise<JackpotInstance[]> {
        const req = {
            ids: jackpotIds.join()
        };
        return this.get<JackpotInstance[]>(JPNServer.JACKPOT_INSTANCES_URL, req, token);
    }

    @measure({ name: "JPNServer.registerJackpotInstance", isAsync: true })
    public registerJackpotInstance(req: RegisterJackpotInstanceRequest, token: string): Promise<JackpotInstance> {
        return this.post<JackpotInstance>(JPNServer.JACKPOT_INSTANCES_URL, req, token);
    }

    @measure({ name: "JPNServer.updateJackpotInstance", isAsync: true })
    public updateJackpotInstance(id: string,
                                 req: RegisterJackpotInstanceRequest,
                                 token: string): Promise<JackpotInstance> {
        return this.patch<JackpotInstance>(`${JPNServer.JACKPOT_INSTANCES_URL}${id}`, req, token);
    }

    @measure({ name: "JPNServer.getJackpotTypes", isAsync: true })
    public getJackpotTypes(jackpotTypes: string[], token: string): Promise<JackpotType[]> {
        const req = {
            names: jackpotTypes.join()
        };
        return this.get<JackpotType[]>(JPNServer.JACKPOT_TYPES_URL, req, token);
    }

    @measure({ name: "JPNServer.getJackpotTickers", isAsync: true })
    public getJackpotTickers(jackpotIds: string[], currency: string): Promise<JackpotTicker[]> {
        const req = {
            jackpotIds: jackpotIds.join(",")
        };
        if (!!currency) {
            req["currency"] = currency;
        }
        return this.request<JackpotTicker[]>(this.baseTickerUrl, "get", JPNServer.JACKPOT_TICKER_URL, undefined, req);
    }

    @measure({ name: "JPNServer.getJackpotAudits", isAsync: true })
    public getJackpotAudits(jackpotId: string, type: string, token: string): Promise<JackpotAudit[]> {
        const req = { jackpotId, type };

        return this.get<JackpotAudit[]>(JPNServer.JACKPOT_AUDITS_URL, req, token);
    }

    private get<T>(url: string, req?: any, token?: string): Promise<T> {
        return this.request<T>(this.baseUrl, "get", url, undefined, req, token);
    }

    private post<T>(url: string, req: any, token: string): Promise<T> {
        return this.request<T>(this.baseUrl, "post", url, req, undefined, token);
    }

    private patch<T>(url: string, req: any, token: string): Promise<T> {
        return this.request<T>(this.baseUrl, "patch", url, req, undefined, token);
    }

    private async request<T>(baseUrl: string,
                             method: string,
                             url: string,
                             req?: any,
                             qs?: any,
                             token?: string): Promise<T> {
        const requestLog = {
            method,
            url,
            qs: qs,
            baseUrl,
            request: req,
            token
        };
        log.info({ requestJpn: requestLog }, "Jpn Request");

        const requestUrl = `${baseUrl}${url}`;
        const agent = keepalive.createAgent(config.jackpotServer.keepAlive, baseUrl);
        const requestInstance = superagent[method](requestUrl)
            .set(this.getHeaders(token))
            .timeout(config.jackpotServer.requestTimeout)
            .agent(agent);
        if (qs) {
            requestInstance.query(qs);
        }
        if (req) {
            requestInstance.send(req);
        }

        return requestInstance
            .then((res) => this.process(requestLog, res))
            .catch((err) => this.process(requestLog, err.response, err));
    }

    protected process(requestLog: any, response: any, error?: Error): Promise<any> {
        const responseLog = {
            requestJpn: requestLog,
            responseCode: response ? response.status : "no response",
            responseBody: response?.body,
        };
        const codes = new Set([200, 201, 204]);
        if (error) {
            log.warn(error, responseLog, "Error sending request");
            throw new Errors.JPNInternalServerError();
        } else if (!codes.has(response.status)) {
            log.warn(responseLog, "Error in response");
            if (response.status >= 400 && response.status < 500) {
                throw new Errors.JPNBadRequestError(response.status, response?.body);
            } else {
                throw new Errors.JPNInternalServerError(response.status, response?.body);
            }
        } else {
            log.info(responseLog, "Response");
            return response.body;
        }
    }

    private getHeaders(token?: string) {
        const headers = {
            "accept": "application/json",
            "content-type": "application/json"
        };

        if (token) {
            headers[X_ACCESS_TOKEN] = token;

        }
        return headers;
    }
}

export function getJPNBaseUrl(): string {
    return config.jackpotServer.host + ":" + config.jackpotServer.port + config.jackpotServer.apiPath;
}

export function getJPNTickerBaseUrl(): string {
    return config.jackpotTickerServer.host + ":" + config.jackpotTickerServer.port + config.jackpotTickerServer.apiPath;
}

function getJpnServerKey(jpnServerUrl: string, tickerServerUrl: string): string {
    return `${jpnServerUrl}-${tickerServerUrl}`;
}

const JPN_SERVERS = new Map<string, JPNServer>();
JPN_SERVERS.set(
    getJpnServerKey(getJPNBaseUrl(), getJPNTickerBaseUrl()),
    new JPNServer(getJPNBaseUrl(), getJPNTickerBaseUrl())
);

export function getJPNServer(jpnServerUrl = getJPNBaseUrl(), tickerServerUrl = getJPNTickerBaseUrl()): JPNServer {
    const key = getJpnServerKey(jpnServerUrl, tickerServerUrl);
    const jpnServer = JPN_SERVERS.get(key);
    if (jpnServer) {
        return jpnServer;
    }
    const newJpnServer = new JPNServer(jpnServerUrl, tickerServerUrl);
    JPN_SERVERS.set(key, newJpnServer);
    return newJpnServer;
}
