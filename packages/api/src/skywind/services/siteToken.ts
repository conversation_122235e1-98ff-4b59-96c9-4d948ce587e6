import { SiteTokenDBInstance, SiteTokenAttributes } from "../models/siteToken";
import { generateSiteToken } from "../utils/token";
import { WhereOptions } from "sequelize";
import * as Errors from "../errors";
import { Models } from "../models/models";

const SiteTokenModel = Models.SiteTokenModel;

export interface SiteTokenInfo {
    token: string;
    id: number;
    brandId: number;
    status?: string;
}

export interface CreateData {
    brandId: number;
    ts: Date;
    status?: string;
}

class SiteTokenImpl implements SiteTokenAttributes {
    public id: number;
    public brandId: number;
    public status: string;
    public token: string;
    public ts: Date;

    constructor(item?: SiteTokenDBInstance) {
        if (!item) {
            return;
        }
        this.id = item.get("id");
        this.brandId = item.get("brandId");
        this.status = item.get("status");
        this.token = item.get("token");
        this.ts = item.get("ts");
    }

    public toInfo(): SiteTokenInfo {
        return {
            id: this.id,
            token: this.token,
            brandId: this.brandId,
        };
    }
}

export async function generateToken(data: CreateData): Promise<SiteTokenInfo> {
    const record: SiteTokenImpl = new SiteTokenImpl();

    record.brandId = data.brandId;
    record.ts = data.ts;
    record.status = data.status || "normal";
    record.token = await generateSiteToken({
        brandId: data.brandId,
        ts: +new Date(),
    });

    const item: SiteTokenDBInstance = await SiteTokenModel.create(record);
    return new SiteTokenImpl(item).toInfo();
}

export async function listForBrand(brandId: number, query?: WhereOptions<any>): Promise<SiteTokenInfo[]> {
    query = query || {};
    query["brandId"] = brandId;
    query["status"] = "normal";
    const listOfTokens = await SiteTokenModel.findAll({
        where: query,
    });
    return listOfTokens.map(token => new SiteTokenImpl(token).toInfo());
}

export async function findOne(query: WhereOptions<any>): Promise<SiteTokenInfo> {
    const item: SiteTokenDBInstance = await SiteTokenModel.findOne({ where: query });
    return item ? new SiteTokenImpl(item).toInfo() : Promise.reject(new Errors.SiteTokenNotExist());
}

export async function changeStatus(brandId: number, id: number, status: string): Promise<SiteTokenInfo> {
    try {
        const tokenRecord: SiteTokenInfo = await findOne({ brandId: brandId, id: id });
        tokenRecord.status = status;
        await SiteTokenModel.update({ status: status }, { where: { brandId: brandId, id: id } });
        return tokenRecord;
    } catch (err) {
        return Promise.reject(new Errors.SiteTokenChangeStatusFail());
    }

}
