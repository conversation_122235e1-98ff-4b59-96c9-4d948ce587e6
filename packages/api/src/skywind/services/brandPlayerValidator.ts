import { BrandEntity } from "../entities/brand";
import { Game } from "../entities/game";
import * as Errors from "../errors";
import * as PlayerRewardService from "./promotions/playerRewardServices";
import { PROMO_TYPE } from "@skywind-group/sw-management-promo-wallet";
import { BaseEntity } from "../entities/entity";
import { getPlayerResponsibleGamingService, SELF_EXCLUSION_MAX_ATTEMPTS } from "./playerResponsibleGaming";
import { EntitySettings } from "../entities/settings";
import { getEntitiesSettings } from "./settings";
import { PLAYER_CODE_MAX_LENGTH, PLAYER_CODE_MIN_LENGTH } from "../utils/common";
import { lazy } from "@skywind-group/sw-utils";
import { Player } from "../entities/player";
import { Models } from "../models/models";

const PLAYER_CODE_VALIDATOR_REGEXP = /[:\t\n]/g;

const PlayerModel = Models.PlayerModel;

class BrandPlayerValidator {

    public async validateBonusCoinsAvailable(brand: BrandEntity, playerCode: string,
                                             currency: string, game: Game): Promise<void> {
        if (game.features && game.features.isGRCGame) {
            return Promise.reject(new Errors.BonusCoinsNotAvailableError());
        }

        const rewardService = PlayerRewardService.get(brand, PROMO_TYPE.BONUS_COIN);
        const bonusCoin = await rewardService.getGamePromotion(playerCode, currency, game.code,
            PROMO_TYPE.BONUS_COIN, brand);
        if (!bonusCoin) {
            return Promise.reject(new Errors.BonusCoinsNotAvailableError());
        }
    }

    public async validatePlayerCanUnblock(playerCode: string, entity: BaseEntity): Promise<void> {
        const playerModel = await PlayerModel.findOne({
            where: {
                code: playerCode,
                brandId: entity.id
            }
        });
        if (!playerModel) {
            return;
        }

        const playerResponsibleGamingService = getPlayerResponsibleGamingService(entity);
        await playerResponsibleGamingService.validatePlayerSelfExclusionRestriction(playerCode);

        const selfExclusionCount = +playerModel.get("selfExclusionCount");

        if (!isNaN(selfExclusionCount) && selfExclusionCount >= SELF_EXCLUSION_MAX_ATTEMPTS) {
            return Promise.reject(new Errors.RepeatingSelfExclusionError());
        }
        return;
    }

    public async validatePlayerCodes(brand: BrandEntity,
                                     playerCodes: string[],
                                     brandSettings?: EntitySettings): Promise<void> {
        if (!brandSettings) {
            const entitiesSettings = await getEntitiesSettings(brand.path);
            brandSettings = entitiesSettings[brand.path];
        }
        const {
            min = brand.isMerchant ? 0 : PLAYER_CODE_MIN_LENGTH,
            max = brand.isMerchant ? Number.MAX_SAFE_INTEGER : PLAYER_CODE_MAX_LENGTH
        } = brandSettings?.validationSettings?.playerCodeLength || {};

        const invalidPlayerCodes = playerCodes.filter((code) => (code.length > +max || code.length < +min));
        if (invalidPlayerCodes.length) {
            return Promise.reject(new Errors.ValidationError(`code length should be between ${min} and ${max}`,
                undefined,
                {
                    playerCodes: invalidPlayerCodes
                }));
        }
    }

    public validatePlayerSuspended(player: Player) {
        if (player.isSuspended()) {
            throw new Errors.PlayerIsSuspended();
        }
    }

    public validatePlayerCode(playerCode: string) {
        const match = playerCode.match(PLAYER_CODE_VALIDATOR_REGEXP);
        if (match) {
            throw new Errors.PlayerCodeIsInvalidError(playerCode, match.join("','"));
        }
    }
}

const brandPlayerValidator = lazy(() => new BrandPlayerValidator());

export const getBrandPlayerValidator = () => brandPlayerValidator.get();
