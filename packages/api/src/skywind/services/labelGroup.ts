import { lazy } from "@skywind-group/sw-utils";
import { LabelGroup } from "../entities/label";
import { ILabelGroupModel, LabelGroupDBInstance } from "../models/labelGroup";
import * as Errors from "../errors";
import { LABEL_GROUPS_RELATIONS_TYPES, LABEL_GROUPS_TYPES } from "../utils/common";
import { CRUDServiceImpl } from "./crudService";
import * as sequelize from "sequelize";
import { Models } from "../models/models";

const LabelGroupModel = Models.LabelGroupModel;

export const queryParamsKeys = ["group", "type", "relationType"];

export class LabelGroupImpl {
    private readonly id: number;
    private readonly group: string;
    private readonly type: LABEL_GROUPS_TYPES;
    private readonly relationType: LABEL_GROUPS_RELATIONS_TYPES;

    constructor(item?: LabelGroupDBInstance) {
        if (!item) {
            return;
        }

        this.id = item.get("id");
        this.group = item.get("group");
        this.type = item.get("type");
        this.relationType = item.get("relationType");
    }

    public toInfo(): LabelGroup {
        return {
            id: this.id,
            group: this.group,
            type: this.type,
            relationType: this.relationType,
        };
    }
    
    public get(field: string) {
        return this[field];
    }
}

export class LabelGroupService extends CRUDServiceImpl<LabelGroupDBInstance, LabelGroup, ILabelGroupModel> {
    public getModel(): ILabelGroupModel {
        return LabelGroupModel;
    }
    
    protected validateCreateData(data: LabelGroup): LabelGroup {
        if (!Object.values(LABEL_GROUPS_TYPES).includes(data.type)) {
            throw new Errors.ValidationError(`Label group type is not valid: ${data.type}`);
        }
        
        if (!Object.values(LABEL_GROUPS_RELATIONS_TYPES).includes(data.relationType)) {
            throw new Errors.ValidationError(`Label group relationType is not valid: ${data.relationType}`);
        }
        
        if (!data.group || typeof data.group !== "string") {
            throw new Errors.ValidationError(`Label group name should be non-empty string: ${data.group}`);
        }
        
        return {
            type: data.type,
            group: data.group,
            relationType: data.relationType
        };
    }

    protected async performCreate(cleanedData: LabelGroup,
                                  transaction: sequelize.Transaction): Promise<any> {
        const instances = await this.list({ where: { group: cleanedData.group, type: cleanedData.type } });
        if (instances.length) {
            throw new Errors.ValidationError(
                `Label group already exists - ${cleanedData.group}, ${cleanedData.type}`);
        }
        
        return super.performCreate(cleanedData, transaction);
    }
}

export const getLabelGroupService = lazy(() => new LabelGroupService());
