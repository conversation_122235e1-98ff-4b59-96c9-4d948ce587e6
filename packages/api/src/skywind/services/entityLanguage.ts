import { BaseEntity, ChildEntity } from "../entities/entity";
import { getChildEntities } from "./entity";
import { sequelize as db } from "../storage/db";
import { Transaction } from "sequelize";
import { LANGUAGES } from "../utils/common";

type direction = "rtl" | "ltr";

interface LanguageInfo {
    name: string;
    nativeName: string;
    direction: direction;
    code: string;
}

export default class EntityLanguageService {
    constructor(public entity: BaseEntity) {
    }

    public getList(): Array<LanguageInfo> {
        const entityLanguages: Array<string> = this.entity.getLanguages();

        return entityLanguages.map(c => {
            return {
                name: LANGUAGES[c].name,
                nativeName: LANGUAGES[c].nativeName,
                direction: LANGUAGES[c].direction,
                code: c,
            };
        });
    }

    public async add(code: string): Promise<BaseEntity> {
        const childEntity = this.entity as ChildEntity;

        childEntity.addLanguage(code);

        return childEntity.save();
    }

    public async addMany(codes: string[]): Promise<BaseEntity> {
        const childEntity = this.entity as ChildEntity;

        codes.forEach(code => childEntity.addLanguage(code));

        return childEntity.save();
    }

    public async remove(code: string, force?: boolean): Promise<BaseEntity> {
        return db.transaction(async (transaction: Transaction): Promise<any> => {
            if (force) {
                const children = getChildEntities(this.entity).filter(entity => entity.languageExists(code));

                const reversedChildren = children.reverse();

                reversedChildren.forEach(entity => entity.removeLanguage(code));

                for (const child of reversedChildren) {
                    await child.save(transaction);
                }
            }

            this.entity.removeLanguage(code);

            return this.entity.save(transaction);
        });
    }

    public async removeMany(codes: string[]): Promise<BaseEntity> {
        const childEntity = this.entity as ChildEntity;

        codes.forEach(code => childEntity.removeLanguage(code));

        return childEntity.save();
    }
}
