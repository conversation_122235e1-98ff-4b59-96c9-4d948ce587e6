import { BaseEntity, ChildEntity, Entity } from "../entities/entity";
import * as Errors from "../errors";
import { Op, Transaction } from "sequelize";
import { sequelize as db } from "../storage/db";
import EntityCache from "../cache/entity";
import { User } from "../entities/user";
import * as redis from "../storage/redis";
import { getSettingsKey } from "./settings";
import { getEntityJurisdictionService } from "./entityJurisdiction";
import { Models } from "../models/models";

const GameModel = Models.GameModel;
const EntityGameModel = Models.EntityGameModel;

interface MoveEntityPayload {
    entityKey: string;
    newParentKey: string;
}

interface GamesConflicts {
    gameCodes?: string[];
}

interface EntityConflicts {
    countries?: string[];
    currencies?: string[];
    languages?: string[];

    defaultLanguage?: string;
    defaultCountry?: string;
    defaultCurrency?: string;
}

interface JurisdictionConflicts {
    jurisdictions?: string[];
}

export interface Conflicts extends GamesConflicts, EntityConflicts, JurisdictionConflicts {
}

export interface StructureService {
    rearrangeChildren(entity: BaseEntity, payload: MoveEntityPayload, user?: User): Promise<void>;
}

class StructureServiceImpl implements StructureService {
    public async rearrangeChildren(master: BaseEntity, payload: MoveEntityPayload, user?: User): Promise<void> {
        this.validatePayload(payload);

        const entity = master.find({ key: payload.entityKey }) as Entity;
        const newParent = master.find({ key: payload.newParentKey }) as Entity;

        this.validateCompatibility(newParent, entity);
        this.validateUser(entity, master, user);

        await this.checkConflicts(newParent, entity);

        const previousPath = entity.path;
        await db.transaction(async (transaction: Transaction): Promise<any> => {
            await this.setParentToEntity(newParent, entity, transaction);
            await this.setParentToEntityGames(newParent, entity, transaction);
        });

        await this.moveEntitySettings(previousPath, entity.path);

        EntityCache.reset();
    }

    private async moveEntitySettings(previousPath, newPath): Promise<void> {
        const previousPrefix = getSettingsKey(previousPath);
        const newPrefix = getSettingsKey(newPath);
        await redis.usingDb(async (db) => {
            const keys = await db.keys(previousPrefix + "*");

            for (const key of keys) {
                await db.rename(key, key.replace(previousPrefix, newPrefix));
            }
        });
    }

    private validatePayload(payload: MoveEntityPayload): void {
        if (payload.entityKey === payload.newParentKey) {
            throw new Errors.ValidationError("entityKey and newParentKey should be different");
        }
    }

    private validateUser(entity: Entity, keyEntity: BaseEntity, user?: User): void {
        if (!user) {
            return;
        }
        const isSuperAdmin = user.hasSuperAdminRole() && keyEntity.isMaster();
        if (!isSuperAdmin && entity.isSuspended()) {
            throw new Errors.ValidationError("Suspended entity can not be moved");
        }
    }

    private validateCompatibility(newParent: Entity, entity: Entity): void {
        if (!(entity && newParent)) {
            throw new Errors.EntityCouldNotBeFound();
        }

        if (entity.isMaster()) {
            throw new Errors.ValidationError("Master can not be moved");
        }

        if (newParent.isSuspended()) {
            throw new Errors.ParentSuspendedError();
        }

        if (newParent.isBrand()) {
            throw new Errors.ParentIsBrand();
        }

        if (entity.find({ key: newParent.key })) {
            throw new Errors.ValidationError("Child is parent of parent");
        }

        if (newParent.child.some(child => child.name === entity.name)) {
            throw new Errors.ValidationError(
                `This parent "${newParent.name}" already has child with name "${entity.name}"`);
        }
    }

    public async checkConflicts(parent: Entity, entity: Entity): Promise<void> {
        const entityConflicts = this.getEntityConflicts(parent, entity);
        const gamesConflicts = await this.getGamesConflicts(parent, entity);
        const jurisdictionConflicts = await this.getJurisdictionConflicts(parent, entity);

        const conflicts: Conflicts = { ...gamesConflicts, ...entityConflicts, ...jurisdictionConflicts };
        if (Object.keys(conflicts).length) {
            return Promise.reject(new Errors.EntityHasConflictsWithParent(conflicts));
        }
    }

    private async getGamesConflicts(parent: Entity, entity: Entity): Promise<GamesConflicts> {
        const parentGameIdsQuery = "SELECT game_id from entity_games " +
            `WHERE entity_id=${parent.id}`;
        const queryResult: any = await EntityGameModel.findAll({
            where: {
                entityId: entity.id,
                gameId: {
                    [Op.notIn]: db.literal(`(${parentGameIdsQuery})`)
                }
            },
            include: [
                {
                    model: GameModel,
                    required: true,
                    attributes: [
                        "code"
                    ]
                }
            ],
            attributes: []
        });

        const conflicts: GamesConflicts = {};
        if (queryResult && queryResult.length) {
            conflicts.gameCodes = queryResult.map(instance => instance.get("game").get("code"));
        }

        return conflicts;
    }

    private getEntityConflicts(parent: Entity, entity: Entity): EntityConflicts {
        const conflicts: EntityConflicts = {};

        const languages = entity.getLanguages().filter(x => !parent.languageExists(x));
        if (languages.length) {
            conflicts.languages = languages;
        }

        const currencies = entity.getCurrencies().filter(x => !parent.currencyExists(x));
        if (currencies.length) {
            conflicts.currencies = currencies;
        }

        if (!parent.languageExists(entity.defaultLanguage)) {
            conflicts.defaultLanguage = entity.defaultLanguage;
        }

        if (!parent.currencyExists(entity.defaultCurrency)) {
            conflicts.defaultCurrency = entity.defaultCurrency;
        }

        return conflicts;
    }

    private async getJurisdictionConflicts(parent: Entity, entity: Entity): Promise<JurisdictionConflicts> {
        const service = getEntityJurisdictionService();
        const jurisdictions = await service.findAll({ entityId: { [Op.in]: [entity.id, parent.id] } });
        const parentJurisdictions = jurisdictions.filter(jurisdiction => jurisdiction.entityId === parent.id);
        const entityJurisdictions = jurisdictions.filter(jurisdiction => jurisdiction.entityId === entity.id);
        const conflicts = entityJurisdictions.filter(entityJurisdiction =>
            !parentJurisdictions.find(parentJurisdiction => parentJurisdiction.code === entityJurisdiction.code));

        if (conflicts.length) {
            return {
                jurisdictions: conflicts.map(jurisdiction => jurisdiction.code)
            };
        }
    }

    private async setParentToEntity(parent: Entity, entity: ChildEntity, transaction?: Transaction): Promise<void> {
        entity.setParent(parent);

        const promises = [];

        const asEntity = entity as Entity;
        if (asEntity.child && asEntity.child.length) {
            for (const child of asEntity.child) {
                promises.push(this.setParentToEntity(asEntity, child, transaction));
            }
        }

        await Promise.all([entity.save(transaction), ...promises]);
    }

    private async setParentToEntityGames(parent: Entity, entity: Entity, transaction?: Transaction): Promise<any> {
        const updateEntityGameParentQuery = "UPDATE entity_games as eg " +
            "SET parent_entity_game_id = " +
            "(SELECT id FROM entity_games WHERE entity_id = :parentId " +
            "AND game_id = eg.game_id) WHERE eg.entity_id = :entityId";
        try {
            await db.query(updateEntityGameParentQuery,
                { replacements: { parentId: parent.id, entityId: entity.id }, transaction });
        } catch (err) {
            if (err.name === "SequelizeDatabaseError") {
                return Promise.reject(new Errors.ParentGameIsNull());
            }
            return Promise.reject(err);
        }
    }
}

let structureService: StructureService;

export default function getStructureService() {
    if (!structureService) {
        structureService = new StructureServiceImpl();
    }
    return structureService;
}
