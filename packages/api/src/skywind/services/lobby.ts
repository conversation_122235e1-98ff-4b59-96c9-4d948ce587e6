import { sequelize as db } from "../storage/db";
import { DownloadableL<PERSON><PERSON>, ExtendedL<PERSON><PERSON>, <PERSON>bby, LobbyCreateData, LobbyUpdateData } from "../entities/lobby";
import { LobbyModel } from "../models/lobby";
import { BaseEntity, ChildEntity } from "../entities/entity";
import * as Errors from "../errors";
import { decodeId, encodeId } from "../utils/publicid";
import { getAllTerminals, getTerminalToken } from "./terminal";
import EntityCache from "../cache/entity";
import { DatabaseError, Op, Transaction, UniqueConstraintError, } from "sequelize";
import { gameCategoryCache, getGameCategoryService } from "./gameCategory/gameCategoryService";
import { LobbyGameInfo, LobbyGamesParams } from "../entities/game";
import { toMenuItems } from "./lobby/toMenuItems";
import { getMenuItemGames } from "./lobby/getMenuItemGames";
import * as LobbyCache from "../cache/lobby";
import { Models } from "../models/models";
import { parseMenuItems } from "./lobby/parseMenuItems";
import { GAME_CATEGORY_TYPE } from "../utils/common";
import { getParentIds } from "./entity";
import * as toIco from "to-ico";
import { getEntitySettings } from "./settings";
import { buildDynamicLiveManagerUrl } from "./entityDomainService";

const STATUS_NORMAL = "normal";
export const INFO_FIELDS: Array<keyof Lobby> = [
    "id",
    "brandId",
    "title",
    "description",
    "theme",
    "info",
    "status",
    "createdAt",
    "updatedAt",
    "isDefault"
];
export const SHORT_INFO_FIELDS: Array<keyof Lobby> = [
    "id",
    "title",
    "description",
    "info",
    "status",
    "createdAt",
    "updatedAt"
];

const lModel = Models.LobbyModel;

export async function getLobby(entity: BaseEntity, id: number): Promise<Lobby> {
    const item = await getLobbyItem(entity.id, id);
    const fn = await toLobby(entity);
    return fn(item);
}

export async function getLobbies(entity: BaseEntity): Promise<Lobby[]> {
    const items = await lModel.findAll({
        where: { brandId: entity.id }
    });
    const fn = await toLobby(entity);
    return items.map(fn);
}

export async function getExtendedLobby(entity: BaseEntity, lobbyId: number,
    params?: LobbyGamesParams): Promise<ExtendedLobby> {
    const entityIds = [entity.id, ...getParentIds(entity)];
    const item = await lModel.findOne({
        where: {
            brandId: entityIds,
            id: lobbyId
        }
    });
    if (!item) {
        return Promise.reject(new Errors.LobbyNotFound());
    }
    const fn = await toExtendedLobby(entity, params);
    return await fn(item);
}

export async function getExtendedLobbies(entity: BaseEntity): Promise<ExtendedLobby[]> {
    const items = await lModel.findAll({
        where: { brandId: entity.id }
    });
    const fn = await toExtendedLobby(entity);
    return await Promise.all(items.map(fn));
}

export async function getLobbyEntityGames(entity: BaseEntity,
                                          lobbyId: number,
                                          params?: LobbyGamesParams): Promise<LobbyGameInfo[]> {
    const [lobby, brand] = await findLobbyByEntity(lobbyId, entity);
    const categories = await getGameCategoryService(entity).findAll({
        status: "normal",
        type: GAME_CATEGORY_TYPE.GENERAL,
    }, 0, Number.MAX_SAFE_INTEGER);
    const menuItems = await parseMenuItems(brand, categories, lobby.info?.menuItems, params);
    return getMenuItemGames(menuItems);
}

export async function createLobby(entity: BaseEntity, data: LobbyCreateData): Promise<Lobby> {
    try {
        const id = await db.transaction(async (transaction: Transaction): Promise<number> => {
            const lobby = await lModel.create({
                title: data.title,
                description: data.description || null,
                brandId: entity.id,
                status: data.status,
                theme: data.theme,
                info: data.info,
                isDefault: data.isDefault
            }, { transaction, returning: true });
            return lobby.id;
        });
        return await getLobby(entity, id);
    } catch (error) {
        if (error instanceof UniqueConstraintError) {
            return Promise.reject(new Errors.LobbyAlreadyExists());
        }
        return Promise.reject(error);
    } finally {
        await LobbyCache.reset();
    }
}

export async function updateLobby(entity: BaseEntity, id: number, data: LobbyUpdateData): Promise<Lobby> {
    try {
        const item = await getLobbyItem(entity.id, id);
        await db.transaction(async (transaction: Transaction): Promise<void> => {
            await item.update({
                description: data.description,
                status: data.status ? data.status : item.get("status"),
                title: data.title ? data.title : item.get("title"),
                theme: data.theme ? data.theme : item.get("theme"),
                info: data.info ? data.info : item.get("info"),
                isDefault: "isDefault" in data ? data.isDefault : item.get("isDefault")
            }, { transaction });
        });
        return await getLobby(entity, id);
    } catch (error) {
        if (error instanceof DatabaseError) {
            return Promise.reject(new Errors.LobbyUpdateFail());
        }
        return Promise.reject(error);
    } finally {
        await LobbyCache.reset();
    }
}

export async function deleteLobby(entity: BaseEntity, id: number): Promise<void> {
    const item = await getLobbyItem(entity.id, id);
    const terminals = await getAllTerminals({}, entity.id, { lobbyId: id });
    if (terminals.length > 0) {
        const terminalIds = terminals.map(terminal => encodeId(terminal.id));
        return Promise.reject(new Errors.LobbyAssignedTerminals(terminalIds));
    }
    await item.destroy();
    await LobbyCache.reset();
}

async function findLobbyByEntity(id: number, entity: BaseEntity): Promise<[LobbyModel, BaseEntity]> {
    let brand: BaseEntity = entity;
    let item = await lModel.findOne({ where: { id, brandId: brand.id } });
    if (!item) {
        do {
            brand = (brand as ChildEntity).getParent && (brand as ChildEntity).getParent();
            if (!brand || brand.isMaster()) {
                break;
            }
            item = await lModel.findOne({ where: { id, brandId: brand.id } });
        } while (!item);
    }
    if (!item) {
        return Promise.reject(new Errors.LobbyNotFound());
    }
    return [item, brand];
}

export function parseLobbyKey(value: string): number[] {
    if (!Number.isNaN(+value) && Number.isFinite(+value)) {
        return [+value];
    }
    const pid = value.trim().toLowerCase();
    const sp = pid.split("");
    const values: string[] = [];
    for (let i = 0, l = 1 << pid.length; i < l; i++) {
        for (let j = i, k = 0; j; j >>= 1, k++) {
            sp[k] = (j & 1) ? sp[k].toUpperCase() : sp[k].toLowerCase();
        }
        values.push(sp.join(""));
    }
    const ids = values.filter((value, index, arr) => arr.indexOf(value) === index);
    return ids.map(decodeId).filter(value => ids.indexOf(value) === -1);
}

export async function findLobbyOptions(entity: BaseEntity, id: number): Promise<Record<string, any>> {
    const lobby = await getLobbyItem(entity.id, id);
    const { social, useSocialCasinoOperator } = await getEntitySettings(entity.path);
    const liveManagerUrl = await buildDynamicLiveManagerUrl(entity);
    return {
        lobbyId: lobby.id,
        ...(lobby.info?.options ?? []).reduce((result, { key, value }) => ({
            ...result,
            [key]: value
        }), {}),
        ...(social !== undefined ? { social } : {}),
        ...(useSocialCasinoOperator !== undefined ? { useSocialCasinoOperator } : {}),
        ...(liveManagerUrl ? { liveManagerUrl } : {})
    };
}

export async function findLobbyTheme(entity: BaseEntity, id: number) {
    const lobby = await getLobbyItem(entity.id, id);
    return (lobby.theme?.options || [])
        .filter(({ key }) => !["icon", "logo", "loginBgImage", "thumbBgImage"].includes(key))
        .map(({ css }) => css)
        .filter(Boolean)
        .join("\n");
}

export async function findLobbyImage(entity: BaseEntity, id: number, name: "icon" | "logo" | "loginBgImage" | "thumbBgImage") {
    const lobby = await getLobbyItem(entity.id, id);
    const data = lobby.theme?.options?.find(({ key }) => key === name)?.value;
    if (data && typeof data === "string") {
        const [, value] = data.split(";base64,", 2);
        const image = Buffer.from(value, "base64");
        if (name === "icon") {
            return toIco(image, { resize: true });
        }
        return image;
    }
    return Promise.reject(new Errors.LobbyNotFound());
}

export async function findLobbyId(lobbyId: string): Promise<{ id?: number; brandId?: number }> {
    const ids = parseLobbyKey(lobbyId);
    const items = await lModel.findAll({
        where: { id: { [Op.in]: ids } },
        attributes: ["id", "brandId"]
    });
    if (items.length === 1) {
        const { brandId, id } = items.shift();
        return { id, brandId };
    }
    return {};
}

async function getLobbyItem(brandId: number, id: number): Promise<LobbyModel> {
    const item = await lModel.findOne({ where: { brandId, id } });
    if (!item) {
        return Promise.reject(new Errors.LobbyNotFound());
    }
    return item;
}

async function toLobby(entity: BaseEntity): Promise<(item: LobbyModel) => Lobby> {
    const categories = await getGameCategoryService(entity).findAll({
        status: "normal",
        type: GAME_CATEGORY_TYPE.GENERAL,
    }, 0, Number.MAX_SAFE_INTEGER);
    const categoryIds = new Set(categories.map(item => encodeId(item.id)));
    return item => {
        const lobby = item.toInfo();
        if (lobby.info) {
            const menuItems = toMenuItems(lobby.info.menuItems, categoryIds);
            return {
                ...lobby,
                info: {
                    ...lobby.info,
                    ...(menuItems.length ? { menuItems } : {})
                }
            };
        }
        return lobby;
    };
}

async function toExtendedLobby(entity: BaseEntity,
                               params?: LobbyGamesParams): Promise<(item: LobbyModel) => Promise<ExtendedLobby>> {
    const categories = await gameCategoryCache.find(`${entity.id}`, entity.id);
    return async item => {
        const lobby = item;
        const menuItems = await parseMenuItems(entity, categories, lobby.info?.menuItems, params);
        return {
            id: lobby.id,
            title: lobby.title,
            description: lobby.description,
            status: lobby.status,
            createdAt: lobby.createdAt,
            updatedAt: lobby.updatedAt,
            info: {
                ...(lobby.info || {}),
                ...(menuItems.length ? { menuItems } : {})
            }
        };
    };
}

/**
 * @deprecated
 */
export async function getDownloadableLobbies(entityStatus: string): Promise<DownloadableLobby[]> {
    const items = await lModel.findAll({
        where: {
            status: STATUS_NORMAL
        },
        include: [
            {
                // model: EntityModel,
                association: lModel.associations.brand,
                attributes: ["id", "path"],
                as: "brand",
                where: { status: entityStatus },
            }
        ],
    });
    return items.map(item => item.toInfo()).map(toDownloadableInfo);
}

/**
 * @deprecated
 */
export async function getDownloadableLobbyWithTerminalToken(
    lobbyId: number,
    entityStatus: string): Promise<DownloadableLobby> {
    const lobbyItem = await lModel.findOne({
        where: {
            status: STATUS_NORMAL,
            id: lobbyId,
        },
        include: [
            {
                // model: EntityModel,
                association: lModel.associations.brand,
                attributes: ["id", "path"],
                as: "brand",
                where: { status: entityStatus },
            }
        ],

    });
    if (!lobbyItem) {
        return Promise.reject(new Errors.LobbyNotFound());
    }
    const brand = await EntityCache.findOne({ id: lobbyItem.get("brandId") });
    const lobbyInfo = toDownloadableInfo(lobbyItem.toInfo());
    lobbyInfo.terminalToken = await getTerminalToken(brand, {});
    return lobbyInfo;
}

/**
 * @deprecated
 */
export async function patchLobbyMetaInfo(lobbyId: number, data: any, entityStatus: string): Promise<Lobby> {
    const lobbyItem = await lModel.findOne({
        where: {
            status: STATUS_NORMAL,
            id: lobbyId,
        },
        include: [
            {
                // model: EntityModel,
                association: lModel.associations.brand,
                attributes: ["id", "path"],
                as: "brand",
                where: { status: entityStatus },
            }
        ],

    });
    if (!lobbyItem) {
        return Promise.reject(new Errors.LobbyNotFound());
    }
    if (data.updatedAt) {
        const lobby = lobbyItem;
        // If Lobby has been changed after build process started then no more changes in Lobby Metainfo during build
        // In this case some other build process will create other app with new data
        if (new Date(data.updatedAt).getTime() !== lobby.updatedAt.getTime()) {
            return lobby;
        }
    }
    lobbyItem.set("info", {
        ...lobbyItem.get("info"),
        ...data
    });
    lobbyItem.changed("info");
    // No update timestamps for patching Metainfo
    await lobbyItem.save({ silent: true });

    return lobbyItem.toInfo();
}

/**
 * @deprecated
 */
function toDownloadableInfo(lobby: Lobby): DownloadableLobby {
    return {
        id: lobby.id,
        brandId: lobby.brandId,
        title: lobby.title,
        description: lobby.description,
        theme: lobby.theme,
        info: lobby.info,
        status: lobby.status,
        path: lobby.path,
        createdAt: lobby.createdAt,
        updatedAt: lobby.updatedAt,
        isDefault: lobby.isDefault
    };
}
