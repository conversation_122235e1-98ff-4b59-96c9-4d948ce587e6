import { token as T } from "@skywind-group/sw-utils";
import config from "../config";
import { SiteTokenNotExist } from "../errors";

interface GitBookLoginData {
    space: string;
}

interface GitBookConfigData {
    config: T.TokenConfig;
    url: string;
}

interface GitBookAuthData {
    url: string;
}

export async function getTokenData(data: GitBookLoginData): Promise<GitBookAuthData> {
    const configData: GitBookConfigData = config.getGitBookSettings(data.space);
    if (!configData.config.secret) {
        throw new SiteTokenNotExist();
    }
    const token = await T.generate({}, configData.config);
    const url = new URL(configData.url);
    url.searchParams.append("jwt_token", token);
    return { url: url.href };
}
