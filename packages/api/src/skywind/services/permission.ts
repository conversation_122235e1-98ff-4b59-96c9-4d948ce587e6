import { UniqueConstraintError } from "sequelize";
import { apiSwagger } from "../utils/swagger";
import * as Errors from "../errors";
import { sequelize as db } from "../storage/db";
import { Models } from "../models/models";

const PermissionModel = Models.PermissionModel;

export interface PermissionDescription {
    group: string;
    code: string;
    description: string;
}

export async function initDB(): Promise<void> {
    const allPermissions = (await getPermissionsDescriptions()).reduce((all, permission) => {
        all[permission.code] = permission;
        return all;
    }, {});

    const permissionsScope = (await apiSwagger()).securityDefinitions["Permissions"].scopes;
    const toCreate = [];
    for (const [code, description] of Object.entries(permissionsScope)) {
        if (!allPermissions[code]) {
            toCreate.push({ code, description });
        }
    }

    for (const permission of toCreate) {
        try {
            await db.query(`
            INSERT INTO permissions (code, description, created_at, updated_at) 
            VALUES (:code, :description, now(), now()) ON CONFLICT ON CONSTRAINT permissions_pkey DO NOTHING;`,
                { replacements: { code: permission.code, description: permission.description } });
        } catch (err) {
            if (!(err instanceof UniqueConstraintError)) {
                throw err;
            }
        }
    }

}

export async function checkPermissions(rolePermissions: string[]): Promise<void> {
    const availablePermissions = await PermissionModel.findAll();
    const permissionsScope: Set<string> = new Set(
        availablePermissions.map(permission => permission.code)
    );
    for (const permission of rolePermissions) {
        // check is our permission in list of system Permissions
        if (!permissionsScope.has(permission)) {
            return Promise.reject(new Errors.PermissionNotExistInList(permission));
        }
    }
}

export async function getPermissionsDescriptions(): Promise<PermissionDescription[]> {
    const availablePermissions = await PermissionModel.findAll();
    return availablePermissions.map(permission => {
        const code = permission.code;
        const description = permission.description;

        const last: number = code.lastIndexOf(":");

        return {
            group: (last === (-1)) ? code : code.slice(0, last),
            code,
            description
        };
    });
}
