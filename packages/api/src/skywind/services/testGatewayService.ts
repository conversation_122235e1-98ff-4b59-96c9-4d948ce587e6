import { TestData, TestH<PERSON>oryReport, TestReport, TestRunInfo } from "../entities/testService";
import config from "../config";
import * as request from "request";
import { IncomingMessage } from "node:http";
import { lazy } from "@skywind-group/sw-utils";
import { IntegrationTestsAPIError, IntegrationTestsAPITransientError } from "../errors";

export interface TestGatewayService {
    runTests(data: TestData): Promise<TestRunInfo>;

    getAllReports(merchantCode: string, query?: string): Promise<TestReport[]>;

    getReport(id: number, merchantCode: string): Promise<TestReport | string>;

    getHistoryReports(merchantCode: string, query?: string): Promise<TestHistoryReport[]>;
}

const INTEGRATION_TEST_RUN = "/v1/test/run";
const INTEGRATION_TEST_GET_REPORTS = "/v1/test";
const INTEGRATION_TEST_GET_HISTORY_REPORTS = "/v1/test/history";

class TestGatewayAPIService implements TestGatewayService {
    public getAllReports(merchantCode: string, query?: string): Promise<TestReport[]> {
        return new Promise((resolve, reject) => {
            request.get(`${INTEGRATION_TEST_GET_REPORTS}/reports/${merchantCode}`, {
                baseUrl: config.integrationTestApi.url,
                json: true,
                headers: [{ "Content-Type": "application/json" }],
                qs: query,
            }, this.processResponse(resolve, reject));
        });
    }

    public getReport(id: number, merchantCode: string, format?: string): Promise<TestReport | string> {
        let url = `${INTEGRATION_TEST_GET_REPORTS}/report/${id}/${merchantCode}`;
        if (format) {
            url += `?format=${format}`;
        }
        return new Promise((resolve, reject) => {
            request.get(url, {
                baseUrl: config.integrationTestApi.url,
                headers: [{ "Content-Type": "application/json" }],
                json: true
            }, this.processResponse(resolve, reject));
        });
    }

    public runTests(data: TestData): Promise<TestRunInfo> {
        return new Promise((resolve, reject) => {
            request.post(
                INTEGRATION_TEST_RUN,
                {
                    baseUrl: config.integrationTestApi.url,
                    headers: [{ "Content-Type": "application/json" }],
                    body: data,
                    json: true
                },
                this.processResponse(resolve, reject)
            );
        });
    }

    public getHistoryReports(merchantCode: string, query?: string): Promise<TestHistoryReport[]> {
        return new Promise((resolve, reject) => {
            request.get(`${INTEGRATION_TEST_GET_HISTORY_REPORTS}/${merchantCode}`, {
                baseUrl: config.integrationTestApi.url,
                headers: [{ "Content-Type": "application/json" }],
                json: true,
                qs: query
            }, this.processResponse(resolve, reject));
        });
    }

    private processResponse(resolve, reject): (error: any, response: IncomingMessage, body: any) => void {
        return function (error: Error, response: IncomingMessage, body: any): void {
            if (error) {
                reject(new IntegrationTestsAPITransientError());
            } else if (response.statusCode !== 200) {
                if (response.statusCode >= 400 && response.statusCode < 500) {
                    reject(new IntegrationTestsAPIError(response.statusCode, body).dontTranslate());
                } else {
                    reject(new IntegrationTestsAPITransientError(response.statusCode, body).dontTranslate());
                }
            } else {
                resolve(body);
            }
        };
    }
}

const testInternalService = lazy<TestGatewayAPIService>(() => new TestGatewayAPIService());
export const getTestGatewayAPIService = () => testInternalService.get();
