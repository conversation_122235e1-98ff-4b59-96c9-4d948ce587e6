import * as Security from "../security";
import { CaptchaInfo, SECURITY_AUTH_TYPE, SECURITY_EVENT_TYPE } from "../security";
import { default as getUserService, PasswordHistoryRecord, UserImpl } from "./user";
import * as Errors from "../../errors";
import EntityCache from "../../cache/entity";
import { User, UserInfo } from "../../entities/user";
import { EmailData } from "../../utils/emails";
import {
    ChangePasswordData,
    PasswordResetData,
    PasswordResetRequest,
    PasswordResetResponse,
    userDefaultPasswordValidator,
    WithResolvedIp,
} from "./userAuth";
import { BaseEntity } from "../../entities/entity";
import * as Settings from "../settings";
import { getEmailService } from "../email";
import { getAuthSessionService } from "../authSessionService";
import { Models } from "../../models/models";
import { Op } from "sequelize";

const UserModel = Models.UserModel;
const MAX_PASSWORD_HISTORY_RECORDS = 3;

const AUTH_TYPE = SECURITY_AUTH_TYPE.USER;
const captchaEnabled = Security.isCaptchaEnabled(AUTH_TYPE);

export interface UserPasswordService {
    changePassword(username: string, data: ChangePasswordData): Promise<UserInfo>;

    unlockChangingPassword(username: string): Promise<void>;

    unlockResetPassword(ip: string): Promise<void>;

    requestPasswordReset(resetReq: PasswordResetRequest): Promise<PasswordResetResponse>;

    refreshCaptcha(req: WithResolvedIp): Promise<CaptchaInfo>;

    resetPassword(data: PasswordResetData): Promise<UserInfo>;

    forceChangePassword(username: string, newPassword: string): Promise<UserInfo>;
}

export function getUserPasswordService(entity?: BaseEntity): UserPasswordService {
    return new UserPasswordServiceImpl(entity);
}

export class UserPasswordServiceImpl implements UserPasswordService {
    constructor(private entity?: BaseEntity) {
    }

    private raiseErrorIfSuspended(entity?: BaseEntity) {
        if ((this.entity || entity).isSuspended()) {
            throw new Errors.ParentSuspendedError();
        }
    }

    public async unlockChangingPassword(username: string): Promise<void> {
        this.raiseErrorIfSuspended();
        return Security.clearBlockedChangingPassword(this.entity.key, username, AUTH_TYPE);
    }

    public async unlockResetPassword(ip: string): Promise<void> {
        this.raiseErrorIfSuspended();
        return Security.clearBlockedResetPassword(ip);
    }

    public async requestPasswordReset(resetReq: PasswordResetRequest): Promise<PasswordResetResponse> {
        const eventType = SECURITY_EVENT_TYPE.PWD_RESET;
        if (!await Security.checkPasswordResets(resetReq.resolvedIp)) {
            return Promise.reject(new Errors.ResetPasswordBlocked(AUTH_TYPE, resetReq.resolvedIp));
        }
        await Security.incrementPasswordResets(resetReq.resolvedIp);

        if (!resetReq.secretKey || !resetReq.identifier) {
            return Promise.reject(new Errors.ValidationError("missing data"));
        }
        if (captchaEnabled) {
            await Security.checkAndRemoveCaptcha(
                "",
                resetReq.resolvedIp,
                AUTH_TYPE,
                eventType,
                resetReq.captchaToken,
                resetReq.csrfToken
            );
        }

        let user: User | undefined;
        const entity = await EntityCache.findOne({ key: resetReq.secretKey });
        if (!entity || entity.isSuspended()) {
            user = undefined;
        } else {
            const item = await UserModel.findOne({
                where: {
                    entityId: entity.id,
                    [Op.or]: [
                        { username: resetReq.identifier },
                        { email: resetReq.identifier },
                    ],
                },
            });
            if (item) {
                const newUser = new UserImpl(item);
                if (!newUser.isSuspended()) {
                    user = newUser;
                }
            }
        }
        if (!user) {
            if (captchaEnabled) {
                await Security.incrementFailures("", resetReq.resolvedIp, AUTH_TYPE, eventType);
                const attemptsFailed = await Security.getFailedAttempts("", resetReq.resolvedIp,
                    eventType);
                if (Security.checkCaptcha(+attemptsFailed, AUTH_TYPE, eventType)) {
                    const outputCaptcha = await Security.generateCaptcha(
                        "",
                        resetReq.resolvedIp,
                        AUTH_TYPE
                    );
                    return {
                        extraData: outputCaptcha
                    };
                }
            }
            return {};
        }
        if (!user.email) {
            return Promise.reject(new Errors.EmailMissingError());
        }

        const resetToken = await Security.createResetToken(entity.key, user.username, "resetPasswordTokens");
        const entitySettings = await Settings.getEntitySettings(entity.path);
        const { from, html, subject } = entitySettings.emailTemplates.passwordRecovery;
        const emailData: EmailData = {
            subject,
            fromEmail: from,
            htmlPart: html
        };
        await getEmailService().sendEmail([user.email], emailData, {
            username: user.username,
            token: resetToken,
            secretKey: resetReq.secretKey,
            domain: resetReq.domain
        });

        return {};
    }

    public async refreshCaptcha(req: WithResolvedIp): Promise<CaptchaInfo> {
        return await Security.generateCaptcha("", req.resolvedIp, AUTH_TYPE);
    }

    public async resetPassword(data: PasswordResetData): Promise<UserInfo> {
        if (!data.secretKey || !data.username || !data.token || !data.newPassword) {
            return Promise.reject(new Errors.ValidationError("missing data"));
        }

        await Security.verifyResetToken(
            data.secretKey,
            data.username,
            "resetPasswordTokens",
            data.token,
            "Reset password token is expired, please, generate another one"
        );

        const entity: BaseEntity = await EntityCache.findOne({
            key: data.secretKey,
        });
        this.raiseErrorIfSuspended(entity);

        const service = getUserService(entity);
        const user = await service.findOne(data.username, true);
        if (user.isSuspended()) {
            return Promise.reject(new Errors.ParentSuspendedError());
        }

        await UserPasswordServiceImpl.validatePasswordHistory(user.passwordHistory, data.newPassword);
        const { salt, password } = await Security.createSaltAndPassword(data.newPassword);

        user.salt = salt;
        user.password = password;
        user.passwordChangedAt = new Date();

        const newPasswordHistoryRecord: PasswordHistoryRecord = {
            salt,
            password: password,
            createdAt: new Date()
        };

        user.passwordHistory = [newPasswordHistoryRecord, ...(user.passwordHistory || [])]
            .slice(0, MAX_PASSWORD_HISTORY_RECORDS);

        await getAuthSessionService().removeAllUserSessions(user.id);

        return user.save();
    }

    public async changePassword(username: string, data: ChangePasswordData): Promise<UserInfo> {
        await Security.checkBlockedPasswordChange(this.entity.key, username);

        this.raiseErrorIfSuspended();

        const userService = getUserService(this.entity);
        const user = await userService.findOne(username);

        const password = await Security.encryptPassword(user.salt, data.password);

        if (password !== user.password) {
            await Security.incrementPasswordChangeFail(this.entity.key, username);
            return Promise.reject(new Errors.PasswordIncorrect());
        }

        await UserPasswordServiceImpl.validateChangePassword(user, data.newPassword);
        // create new salt and password
        const saltAndPwd = await Security.createSaltAndPassword(data.newPassword);
        const salt = saltAndPwd.salt;
        const encodedPassword = saltAndPwd.password;

        user.salt = salt;
        user.password = encodedPassword;

        const newPasswordHistoryRecord: PasswordHistoryRecord = {
            salt,
            password: encodedPassword,
            createdAt: new Date()
        };
        user.passwordHistory = [newPasswordHistoryRecord, ...(user.passwordHistory || [])]
            .slice(0, MAX_PASSWORD_HISTORY_RECORDS);

        await getAuthSessionService().removeAllUserSessions(user.id);

        return user.save();
    }

    public async forceChangePassword(username: string, newPassword: string): Promise<UserInfo> {
        this.raiseErrorIfSuspended();

        const service = getUserService(this.entity);
        const user = await service.findOne(username);

        await UserPasswordServiceImpl.validateChangePassword(user, newPassword);

        const { salt, password } = await Security.createSaltAndPassword(newPassword);

        user.salt = salt;
        user.password = password;

        const newPasswordHistoryRecord: PasswordHistoryRecord = {
            salt,
            password: password,
            createdAt: new Date()
        };
        user.passwordHistory = [newPasswordHistoryRecord, ...(user.passwordHistory || [])]
            .slice(0, MAX_PASSWORD_HISTORY_RECORDS);

        await getAuthSessionService().removeAllUserSessions(user.id);

        return user.save();
    }

    private static async validateChangePassword(user: User, newPassword: string) {
        if (user.username === newPassword) {
            return Promise.reject(new Errors.SamePasswordAndUsernameError());
        }

        if (user.isSuspended()) {
            return Promise.reject(new Errors.ParentSuspendedError());
        }

        if (!userDefaultPasswordValidator.test(newPassword)) {
            return Promise.reject(new Errors.ValidationPasswordError());
        }

        if (user.isSuperAdmin()) {
            return Promise.reject(new Errors.ValidationError("You cannot reset password for superadmin"));
        }

        const encryptedPassword = await Security.encryptPassword(user.salt, newPassword);
        if (encryptedPassword === user.password) {
            return Promise.reject(new Errors.PasswordNotUnique());
        }

        await UserPasswordServiceImpl.validatePasswordHistory(user.passwordHistory, newPassword);
    }

    private static async validatePasswordHistory(passwordHistory: PasswordHistoryRecord[], newPassword: string) {
        if (!passwordHistory || !passwordHistory.length) {
            return;
        }
        for (const passwordInfo of passwordHistory) {
            const encryptedPassword = await Security.encryptPassword(passwordInfo.salt, newPassword);

            if (passwordInfo.password === encryptedPassword) {
                return Promise.reject(
                    new Errors.ValidationError("One of your previous password was the same")
                );
            }
        }
    }
}
