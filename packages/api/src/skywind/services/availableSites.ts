import {
    AvailableSiteModel,
    IAvailableSiteModel,
} from "../models/availableSites";
import { FindOptions, Op, Transaction } from "sequelize";
import * as FilterService from "./filter";
import * as Errors from "../errors";
import { BaseEntity } from "../entities/entity";
import { AVAILABLE_SITE_STATUSES, AvailableSite } from "../entities/availableSite";
import * as availableSitesCache from "../cache/availableSites";
import { CRUDServiceImpl } from "./crudService";
import { isWebSiteWhitelistedCheckAvailable } from "./entity";
import { Models } from "../models/models";

const DEFAULT_SORT_KEY = "id";

export const sortableKeys = [
    "id",
    "title",
    "url",
    "status",
    "entityId",
    "operatorSiteGroupName",
    "externalCode"
];

export const queryParamsKeys = [
    "sortBy",
    "sortOrder",
    "id",
    "title",
    "url",
    "status",
    "operatorSiteGroupName",
    "externalCode"
];

export class AvailableSiteService extends CRUDServiceImpl<AvailableSiteModel, AvailableSite, IAvailableSiteModel> {
    public getModel(): IAvailableSiteModel {
        return Models.AvailableSiteModel;
    }

    constructor(public entity: BaseEntity) {
        super();
    }

    protected async performCreate(cleanedData: AvailableSite,
                                  transaction: Transaction): Promise<AvailableSiteModel> {
        cleanedData.entityId = this.entity.id;

        const sites = await this.list({ where: { entityId: this.entity.id } });

        if (cleanedData.url) {
            const filteredSites = sites.filter(site => site.url === cleanedData.url);

            if (filteredSites.length) {
                throw new Errors.SiteAlreadyExists();
            }
        }

        if (cleanedData.isDefault) {
            await this.resetDefaultSites(sites, transaction);
        }

        try {
            return super.performCreate(cleanedData, transaction);
        } catch (err) {
            throw new Errors.SiteAlreadyExists();
        }
    }

    protected async performDestroy(instance: AvailableSiteModel,
                                   transaction: Transaction): Promise<void> {
        if (instance.entityId !== this.entity.id) {
            throw new Errors.ValidationError("Site belong to another entity");
        }

        await super.performDestroy(instance, transaction);
        await availableSitesCache.reset();
    }

    protected async performUpdate(instance: AvailableSiteModel,
                                  cleanedData: Partial<AvailableSite>,
                                  transaction: Transaction): Promise<AvailableSiteModel> {
        if (instance.entityId !== this.entity.id) {
            throw new Errors.ValidationError("Site belong to another entity");
        }

        const sites = await this.list({
            where: {
                entityId: this.entity.id,
                id: { [Op.ne]: instance.id },
            }
        });

        if (cleanedData.url) {
            const filteredSites = sites.filter(site => site.url === cleanedData.url);

            if (filteredSites.length) {
                throw new Errors.SiteAlreadyExists();
            }
        }

        if (cleanedData.isDefault) {
            await this.resetDefaultSites(sites, transaction);
        }

        try {
            const result = await super.performUpdate(instance, cleanedData, transaction);
            await availableSitesCache.reset();

            return result;
        } catch (err) {
            throw new Errors.SiteAlreadyExists();
        }
    }

    private async resetDefaultSites(sites: AvailableSiteModel[],
                                    transaction: Transaction): Promise<void> {
        for (const site of sites.filter(site => site.isDefault)) {
            await site.update({ isDefault: false }, { transaction });
        }
    }

    public async getSitesByDomain(domain: string): Promise<AvailableSite[]> {
        const items = await this.list({ where: { url: domain, status: AVAILABLE_SITE_STATUSES.NORMAL } });

        if (!items.length) {
            throw new Errors.SiteNotFound();
        }

        return items.map(item => item.toInfo());
    }

    public async checkSiteIsAuthorized(referer: string): Promise<boolean> {
        const item = await availableSitesCache.findOne(this.entity, referer);

        if (!item) {
            await this.create({
                entityId: this.entity.id,
                url: referer,
                status: AVAILABLE_SITE_STATUSES.NORMAL
            });

            return true;
        }

        return item.isAuthorized;
    }

    public async list(options?: FindOptions): Promise<AvailableSiteModel[]> {
        if (options?.where) {
            const sortBy = FilterService.getSortKey(options.where, sortableKeys, DEFAULT_SORT_KEY);
            const sortOrder = FilterService.valueFromQuery(options.where, "sortOrder") || "ASC";

            options.order = [[sortBy, sortOrder as string]];
        }

        return super.list(options);
    }

    /*
        This function try to find operator site id using some priorities:
        1. if externalCode exists:
            1.1 try to find site by external code
            1.2 if not found then try to find by url
                1.2.1 if exists then update site with externalCode
            1.3 if not exists then create new available site
        2. if externalCode not exists:
            1.1 if it launcher then try to find site by referrer
            1.2 find default site and use it.
            1.3 if not exists then return nothing.

     */
    public async getOperatorSiteId(externalCode?: string, referrer?: string): Promise<number | undefined> {
        const sites = await this.list({ where: { entityId: this.entity.id } });
        if (!externalCode) {
            const isLauncher = referrer && isWebSiteWhitelistedCheckAvailable(this.entity);
            if (isLauncher) {
                const referrerSite = sites.find(({ url }) => url === referrer);
                if (referrerSite) {
                    return referrerSite.id;
                }
            }

            const defaultSite = sites.find(({ isDefault }) => isDefault);
            if (defaultSite) {
                return defaultSite.id;
            }
            return;
        }

        const site = sites.find(({ externalCode: code }) => code === externalCode);
        if (site) {
            return site.id;
        }

        const siteByUrl = sites.find(({ url }) => url === externalCode);
        if (siteByUrl) {
            await siteByUrl.update({ externalCode });
            return siteByUrl.id;
        }

        const created = await this.create({
            status: AVAILABLE_SITE_STATUSES.NORMAL,
            externalCode,
            title: externalCode,
            url: externalCode,
            operatorSiteGroupName: externalCode
        });

        return created.id;
    }
}

export const getAvailableSiteService = (entity: BaseEntity) => new AvailableSiteService(entity);

export async function checkSiteIsAuthorized(entity: BaseEntity, referrer: string) {
    if (referrer && !await getAvailableSiteService(entity).checkSiteIsAuthorized(referrer)) {
        return Promise.reject(new Errors.UnauthorizedSite());
    }
}
