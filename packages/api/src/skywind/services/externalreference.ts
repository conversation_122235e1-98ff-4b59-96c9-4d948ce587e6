import { postgres } from "../storage/postgres";
import { QueryResult } from "pg";
import * as Errors from "../errors";
import { ExternalPayload } from "../entities/player";

/**
 * Creates a new external reference entry and returns generated UUID
 *
 * @param externalData ExternalPayload
 * @returns {Promise<string>}
 */
export async function create(externalData: ExternalPayload): Promise<string> {
    const text: string = "insert into external_references(ext_ref, brand_id) VALUES ($1, $2) returning ref_id;";
    const values: any[] = [externalData.extRef, externalData.brandId];

    const rs: QueryResult = await postgres.query(text, values);
    return rs.rows[0].ref_id;
}

/**
 * Finds redId by external reference
 *
 * @param externalData ExternalPayload
 * @returns {Promise<string>}
 */
export async function  find(externalData: ExternalPayload) {
    const text: string = "select ref_id from external_references where ext_ref = $1 and brand_id = $2;";
    const values: any[] = [externalData.extRef, externalData.brandId];

    const rs: QueryResult = await postgres.query(text, values);
    return rs.rows.length ? rs.rows[0].ref_id : undefined;
}

/**
 * Gets transaction from tx_log by given refId
 *
 * @param externalReference
 * @returns {InternalTransactionInfo|Promise<any>}
 */
export async function getTransaction(externalReference: string): Promise<InternalTransactionInfo> {
    const transactions: InternalTransactionInfo = await getTransactionData(externalReference);
    return transactions && transactions.id ? transactions : Promise.reject(new Errors.TransactionNotFound());
}

export class InternalTransactionInfo {
    constructor(public id: number,
                public currency: string,
                public refId: string) {
    }
}

async function getTransactionData(refId: string): Promise<InternalTransactionInfo> {
    const text: string = "select id, currency_code from tx_log where ref_id = $1 and type = 'COMMIT'";
    const values: any[] = [refId];

    const rs: QueryResult = await postgres.query(text, values);
    const row = rs.rows[0];
    return row ? new InternalTransactionInfo(
        row.id,
        row.currency_code,
        refId
    ) : undefined;
}
