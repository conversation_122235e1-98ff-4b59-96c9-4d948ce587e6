import { usingDb } from "../../storage/redis";
import { measures } from "@skywind-group/sw-utils";
import measure = measures.measure;

export interface PendingReward {
    brandId: number;
    playerCode: string;
    promoType: string;
    promoId: number;
    startDate: number;
    createdAt: number;
    expireDate: number; // set to null to remove pending
}

export class PendingRewardService {

    @measure({ name: "PendingPromotionService.findPending", isAsync: true, debugOnly: true })
    public findPending(brandId: number, playerCode: string, promoType: string): Promise<PendingReward | undefined> {
        return usingDb<PendingReward>(async (db) => {
            const key = this.getKey(brandId, playerCode, promoType);
            const promotions = await db.hgetall(key);
            const pendingRewards = Object.values(promotions).map(promo => JSON.parse(promo));
            return pendingRewards.sort((a, b) => {
                if (a.startDate === b.startDate) {
                    return a.createdAt - b.createdAt;
                }
                return a.startDate - b.startDate;
            })[0];
        });
    }

    @measure({ name: "PendingPromotionService.updatePending", isAsync: true, debugOnly: true })
    public updatePending(pending: PendingReward[]): Promise<void> {
        return usingDb<void>(async (db) => {
            const multi = db.multi();
            for (const item of pending) {
                const key = this.getKey(item.brandId, item.playerCode, item.promoType);

                if (item.expireDate) {
                    const expireInSec = Math.round((item.expireDate - Date.now()) / 1000);
                    if (expireInSec > 0) {
                        multi.hset(key, item.promoId.toString(), JSON.stringify({
                            promoId: item.promoId,
                            promoType: item.promoType,
                            startDate: item.startDate,
                            createdAt: item.createdAt
                        }));
                        multi.call("HEXPIRE", key, expireInSec, "FIELDS", 1, item.promoId.toString());
                    } else {
                        multi.hdel(key, item.promoId.toString());
                    }
                } else {
                    multi.hdel(key, item.promoId.toString());
                }
            }
            await multi.exec();
        });
    }

    @measure({ name: "PendingPromotionService.removePending", isAsync: true, debugOnly: true })
    public removePending(pending: PendingReward[]): Promise<void> {
        return usingDb<void>(async (db) => {
            const multi = db.multi();
            for (const item of pending) {
                const key = this.getKey(item.brandId, item.playerCode, item.promoType);
                multi.hdel(key, item.promoId.toString());
            }
            await multi.exec();
        });
    }

    private getKey(brandId: number, playerCode: string, promoType: string): string {
        return `promo:pending:${promoType}:${brandId}:${playerCode}`;
    }
}

export default new PendingRewardService();
