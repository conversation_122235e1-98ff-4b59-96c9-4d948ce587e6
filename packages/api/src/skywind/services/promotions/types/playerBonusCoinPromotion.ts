import { BonusCoinRewardInfo } from "../../../entities/promotion";
import * as Errors from "../../../errors";
import {
    BnsWalletExistenceType,
    BONUS_COIN_CURRENCY,
    BonusCoinsData,
    PlayerBonusCoinWallet,
    PlayerGameBonusCoin,
    PlayerPromotionWalletFacade,
    PROMO_TYPE
} from "@skywind-group/sw-management-promo-wallet";
import { PromotionToPlayer, PromotionToPlayerStatus } from "../../../models/promotionPlayer";
import { IsActiveResult, PromotionPlayersUpdate } from "../promotionPlayersUpdate";
import { PlayerPromotionDb, PlayerPromotionInfo } from "../playerPromotionDb";
import { lazy, Lazy } from "@skywind-group/sw-utils";
import EntitySettingsService from "../../settings";
import { PromotionBonusCoinRewardImpl } from "../promotionReward";
import * as PromoService from "../promotion";
import logger from "../../../utils/logger";
import { EntitySettings } from "../../../entities/settings";
import { EntityBalance } from "../../../entities/entity";
import { ITransaction } from "@skywind-group/sw-wallet";
import { PromotionRewardService } from "../promotionRewardService";
import { Transaction } from "sequelize";
import { values } from "lodash";
import { getRewardExpirationPeriod, PromotionImpl } from "../promotion";

export interface ProlongBonusCoinsData {
    amount?: number;
    expirationPeriod?: number;
    expirationPeriodType?: string;
}

const log = logger("bonus-coins");

export enum BonusCoinsStatus {
    NOT_STARTED = "not_started",                // promo is not started for player or not rewarded in the wallet
    AWARDED = "awarded",                        // player received promo in the wallet
    STARTED = "started",                        // player started playing bonus coins
    EXPIRED = "expired",                        // promo expired for player without redeemable bonus coins
    REDEEMED = "redeemed",                      // player redeemed bonus coins
    FINISHED = "finished",                      // player finished playing bonus coins with 0 amount
    REDEEMABLE_EXPIRED = "redeemable_expired"   // promo expired for player but there are redeemable bonus coins
}

export interface PlayerBonusCoinPromotionInfo extends PlayerPromotionInfo {
    status: string | BonusCoinsStatus;
    bonusCoins?: PlayerBonusCoinRewardInfo[];
}

export interface PlayerBonusCoinRewardInfo {
    amount: number;
    rewardedAmount: number;
    redeemMinAmount: number;
    redeemMaxAmount?: number;
    games: string[];
    startDate: Date;
    endDate: Date;
    underRedeem?: boolean;
    redeemingGame?: string;
}

export class BonusCoinRewardService extends PromotionRewardService {

    private brandSettings: Lazy<Promise<EntitySettings>> = lazy(async () => {
        return await new EntitySettingsService(this.brand).get();
    });

    private minBonusCoinBet: Lazy<Promise<number>> = lazy(async () => {
        const settings: EntitySettings = await this.brandSettings.get();
        return settings.minBonusCoinBet || 1;
    });

    public async getPromotionInfo(playerPromo: PromotionToPlayer): Promise<PlayerBonusCoinPromotionInfo> {
        let data: PlayerBonusCoinPromotionInfo;
        if (playerPromo.rewardedAt) {
            const promos = await this.getPromotionsFromWallet(playerPromo.playerCode, playerPromo.playerCurrency);
            data = promos.find((p) => p.promoId === playerPromo.promotionId);
        }

        let status: BonusCoinsStatus;
        switch (playerPromo.status) {
            case PromotionToPlayerStatus.PENDING:
            case PromotionToPlayerStatus.CONFIRMED:
                status = BonusCoinsStatus.NOT_STARTED;
                break;

            case PromotionToPlayerStatus.STARTED:
                if (playerPromo.expireAt > new Date()) {
                    if (playerPromo.rewardedAt) {
                        if (playerPromo.playedAt) {
                            status = BonusCoinsStatus.STARTED;
                        } else {
                            status = BonusCoinsStatus.AWARDED;
                        }
                    } else {
                        status = BonusCoinsStatus.NOT_STARTED;
                    }
                } else {
                    if (data) {
                        status = BonusCoinsStatus.REDEEMABLE_EXPIRED;
                    } else {
                        status = BonusCoinsStatus.EXPIRED;
                    }
                }
                break;

            case PromotionToPlayerStatus.FINISHED:
                if (playerPromo.finishStatus === "redeemed") {
                    status = BonusCoinsStatus.REDEEMED;
                } else if (playerPromo.finishStatus === "expired") {
                    status = BonusCoinsStatus.EXPIRED;
                } else {
                    status = BonusCoinsStatus.FINISHED;
                }
                break;

            default:
                status = playerPromo.status as any;
        }

        if (!data) {
            data = {
                promoId: playerPromo.promotionId,
                status
            };
            if (playerPromo.expireAt) {
                data.expireAt = playerPromo.expireAt;
            }
        } else {
            data.status = status;
        }

        return data;
    }

    protected async confirmPlayers(update: PromotionPlayersUpdate, dbTransaction?: Transaction) {
        await this.validatePromoIntersection(update, dbTransaction);

        let transaction: ITransaction;
        const settings: EntitySettings = await this.brandSettings.get();

        if (!settings.omitBnsBalance) {
            const totalRequiredBalance = this.getTotalRequiredBrandBalance(update.promo, update.promoPlayers);
            const balance = await this.brand.fetchBalance(BONUS_COIN_CURRENCY);
            if (balance.main < totalRequiredBalance) {
                return Promise.reject(new Errors.InsufficientBonusCoinsBrandBalance());
            }
            transaction = await update.initWalletTransaction();
            await this.brand.wallet.debit(transaction, BONUS_COIN_CURRENCY, totalRequiredBalance);
        }

        transaction = transaction || await update.initWalletTransaction();

        for (const playerPromo of update.promoPlayers) {
            if (playerPromo.playerCurrency) {
                const facade = PlayerPromotionWalletFacade.create(this.brand.id,
                    playerPromo.playerCode, playerPromo.playerCurrency);
                const bonusCoinWallet = await facade.getBonusCoinWallet(transaction);
                await this.validatePromoForPlayer(update.promo, playerPromo, bonusCoinWallet);
            }
        }

        update.updateAllPlayerPromos({ status: PromotionToPlayerStatus.CONFIRMED });
    }

    protected async resetConfirmedPlayers(update: PromotionPlayersUpdate) {
        const totalReturnedBalance = this.getTotalRequiredBrandBalance(update.promo, update.promoPlayers);

        const transaction = await update.initWalletTransaction();

        await this.brand.wallet.credit(transaction, BONUS_COIN_CURRENCY, totalReturnedBalance);

        update.updateAllPlayerPromos({ status: PromotionToPlayerStatus.PENDING });
    }

    /**
     *
     * @param update
     * @param startDate is now or promo.startedAt in case of pending.
     * @param isPendingReward
     * @param dbTransaction
     */
    protected async rewardPlayers(update: PromotionPlayersUpdate,
                                  startDate: Date,
                                  isPendingReward: boolean = false,
                                  dbTransaction?: Transaction) {
        const now = new Date(); // will be used as rewardDate

        const isStartRewardOnGameOpen = update.promo.isStartRewardOnGameOpen();
        const initRewardOnGameOpen = isStartRewardOnGameOpen && !isPendingReward; // init state for deferred reward
        const updateRewardOnGameOpen = isStartRewardOnGameOpen && isPendingReward; // update state for deferred reward

        await this.validatePromoIntersection(update, dbTransaction);

        const transaction = await update.initWalletTransaction();

        if (updateRewardOnGameOpen) {
            startDate = now; // cause in that case it was set to promo.startedAt
        }

        let expireIn: number;

        for (const reward of update.promo.getRewards()) {
            const rewardInfo: BonusCoinRewardInfo = reward.toInfo() as BonusCoinRewardInfo;
            const rewardExpireIn = getRewardExpirationPeriod(rewardInfo);

            if (!expireIn || expireIn < rewardExpireIn) {
                expireIn = rewardExpireIn;
            }
        }

        const expireDate = new Date(now.getTime() + expireIn);
        // here we use promo end of date or reward expire date
        const pendingExpireDate: Date = update.promo.getEndDate() > expireDate ?
                                 update.promo.getEndDate() : expireDate;

        // all rewarded means that all promo players has playerCurrency.
        const allRewarded: boolean = update.promoPlayers.find((v) => !v.playerCurrency) === undefined;

        for (const playerPromo of update.promoPlayers) {
            const prolongData: any = playerPromo.data && playerPromo.data;
            const prolongedAmount = (prolongData && prolongData.amount) || 0;
            const prolongedExpire = (prolongData && prolongData.expireIn) || 0;

            if (initRewardOnGameOpen) {
                update.updatePendingReward({
                    brandId: this.brand.id,
                    playerCode: playerPromo.playerCode,
                    promoType: update.promo.getType(),
                    promoId: update.promo.getId(),
                    startDate: update.promo.getStartDate().getTime(),
                    createdAt: update.promo.getCreatedAt().getTime(),
                    expireDate: (pendingExpireDate.getTime() + prolongedExpire)
                });
            } else if (playerPromo.playerCurrency) {
                // we playerPromo.playerCurrency in case of brand player
                // and in case of merchant when we do reward pending
                const facade = PlayerPromotionWalletFacade.create(this.brand.id,
                    playerPromo.playerCode, playerPromo.playerCurrency);

                const bonusCoinWallet = await facade.getBonusCoinWallet(transaction);
                await this.validatePromoForPlayer(update.promo, playerPromo, bonusCoinWallet);

                if (bonusCoinWallet) {
                    for (const reward of update.promo.getRewards()) {
                        const rewardInfo: BonusCoinRewardInfo = reward.toInfo() as BonusCoinRewardInfo;
                        const rewardExpireIn = getRewardExpirationPeriod(rewardInfo) + prolongedExpire;

                        bonusCoinWallet.addBonusCoins({
                            promoId: update.promo.getId(),
                            rewardId: reward.getId(),
                            amount: rewardInfo.amount + prolongedAmount,
                            rewardedAmount: rewardInfo.amount + prolongedAmount,
                            startDate: startDate.getTime(),
                            expireIn: rewardExpireIn,
                            redeemMinAmount: rewardInfo.redeemMinAmount,
                            redeemMaxAmount: rewardInfo.redeemMaxAmount,
                            existenceType: null
                        });
                    }
                }

                if (!allRewarded) {
                    update.updatePlayerPromo(playerPromo, {
                        rewardedAt: now,
                    });
                }

                if (prolongData) {
                    update.updatePlayerPromo(playerPromo, {
                        data: null,
                        expireAt: new Date(expireDate.getTime() + prolongedExpire)
                    });
                } else if (!playerPromo.rewardedAt) {
                    // means that it the update to the PENDING reward and we need to adjust it expire date
                    update.updatePlayerPromo(playerPromo, {
                        expireAt: expireDate
                    });
                }

            } else {
                update.updatePendingReward({
                    brandId: this.brand.id,
                    playerCode: playerPromo.playerCode,
                    promoType: update.promo.getType(),
                    promoId: update.promo.getId(),
                    startDate: update.promo.getStartDate().getTime(),
                    createdAt: update.promo.getCreatedAt().getTime(),
                    expireDate: (pendingExpireDate.getTime() + prolongedExpire)
                });

                if (prolongedExpire) {
                    update.updatePlayerPromo(playerPromo, {
                        expireAt: new Date(pendingExpireDate.getTime() + prolongedExpire)
                    });
                }
            }
        }

        update.updateAllPlayerPromos({
            status: PromotionToPlayerStatus.STARTED,
            startedAt: startDate,
            expireAt: expireDate
        });

        if (initRewardOnGameOpen) {
            // here we update rewardedAt cause we can assign the same promotion to player again and again
            update.updateAllPlayerPromos({
                rewardedAt: null
            });
        } else {
            if (allRewarded) {
                update.updateAllPlayerPromos({
                    rewardedAt: now
                });
            }
        }
    }

    protected async updatePlayers(update: PromotionPlayersUpdate, data: ProlongBonusCoinsData) {
        if (!data.amount && !data.expirationPeriod) {
            return Promise.reject(new Errors.ValidationError("amount or expirationPeriod should be specified"));
        }
        if (data.expirationPeriod && !data.expirationPeriodType) {
            return Promise.reject(new Errors.ValidationError("expirationPeriodType should be specified"));
        }

        // 1) Update brand BNS balance for entity
        if (data.amount) {
            const settings: EntitySettings = await this.brandSettings.get();
            if (!settings.omitBnsBalance) {
                // Here we update brand BNS balance
                const transaction = await update.initWalletTransaction();
                const totalRequiredBalance =
                    update.promo.getRewards().length * data.amount * update.promoPlayers.length;
                const balance: EntityBalance = await this.brand.fetchBalance(BONUS_COIN_CURRENCY);
                if (balance.main < totalRequiredBalance) {
                    return Promise.reject(new Errors.InsufficientBonusCoinsBrandBalance());
                }
                await this.brand.wallet.debit(transaction, BONUS_COIN_CURRENCY, totalRequiredBalance);
            }
        }

        // 2) Update players wallet or pending rewards
        for (const playerPromo of update.promoPlayers) {
            const prolongedExpire = getRewardExpirationPeriod(data);
            const newExpireAt: Date = new Date(playerPromo.expireAt.getTime() + (prolongedExpire || 0));

            if (playerPromo.rewardedAt) {
                const transaction = await update.initWalletTransaction();
                const playerPromotionWalletFacade = PlayerPromotionWalletFacade.create(this.brand.id,
                    playerPromo.playerCode, playerPromo.playerCurrency);
                const bonusCoinWallet = await playerPromotionWalletFacade.getBonusCoinWallet(transaction);
                const currentPromo = bonusCoinWallet.getCurrentPromoId();
                if (currentPromo !== update.promo.getId()) {
                    return Promise.reject(new Errors.PromotionPlayersValidationError(
                        "Promo is not active for player", [playerPromo.playerCode]));
                }

                bonusCoinWallet.prolongBonusCoins({
                    promoId: update.promo.getId(),
                    amount: data.amount,
                    expireIn: prolongedExpire
                });
            } else {
                const pendingData: any = playerPromo.data && (playerPromo.data as any).amount ?
                                         playerPromo.data : { amount: 0 };
                if (data.amount) {
                    pendingData.amount += data.amount;
                }
                if (prolongedExpire) {
                    pendingData.expireIn = (pendingData.expireIn || 0) + prolongedExpire;
                }

                update.updatePendingReward({
                    brandId: this.brand.id,
                    playerCode: playerPromo.playerCode,
                    promoType: update.promo.getType(),
                    promoId: update.promo.getId(),
                    startDate: update.promo.getStartDate().getTime(),
                    createdAt: update.promo.getCreatedAt().getTime(),
                    expireDate: newExpireAt.getTime()
                });

                update.updatePlayerPromo(playerPromo, { data: pendingData });
            }

            update.updatePlayerPromo(playerPromo, { expireAt: newExpireAt });
        }
    }

    protected async removePlayers(update: PromotionPlayersUpdate) {
        const settings: EntitySettings = await this.brandSettings.get();
        if (!settings.omitBnsBalance) {
            const transaction = await update.initWalletTransaction();
            const totalReturnedBalance = this.getTotalRequiredBrandBalance(update.promo, update.promoPlayers);
            await this.brand.wallet.credit(transaction, BONUS_COIN_CURRENCY, totalReturnedBalance);
        }
        update.updateAllPlayerPromos({ status: PromotionToPlayerStatus.REVOKED });
    }

    protected async revokePlayers(update: PromotionPlayersUpdate): Promise<void> {
        const transaction = await update.initWalletTransaction();

        for (const playerPromo of update.promoPlayers) {
            const playerPromotionWalletFacade = PlayerPromotionWalletFacade.create(this.brand.id,
                playerPromo.playerCode, playerPromo.playerCurrency);
            const bonusCoinWallet = await playerPromotionWalletFacade.getBonusCoinWallet(transaction);
            bonusCoinWallet.revokeBonusCoins(playerPromo.promotionId);
        }

        update.updateAllPlayerPromos({ status: PromotionToPlayerStatus.REVOKED });
    }

    protected async getPlayerGamePromotion(playerCode: string,
                                           playerCurrency: string, gameCode: string): Promise<PlayerGameBonusCoin> {
        const playerPromotionWalletFacade = PlayerPromotionWalletFacade.create(
            this.brand.id, playerCode, playerCurrency);
        const bonusCoinWallet: PlayerBonusCoinWallet = await playerPromotionWalletFacade.getBonusCoinWallet();
        return this.getGameBonusCoin(bonusCoinWallet, gameCode);
    }

    public async getPromoInfo(bonusCoinWallet: PlayerBonusCoinWallet): Promise<PlayerBonusCoinPromotionInfo[]> {
        const promos: { [promoId: string]: PlayerBonusCoinPromotionInfo } = {};
        for (const item of bonusCoinWallet.bonusCoins) {
            if (!promos[item.promoId]) {
                promos[item.promoId] = {
                    promoId: item.promoId,
                    status: BonusCoinsStatus.STARTED,
                    expireAt: new Date(item.startDate + item.expireIn),
                    bonusCoins: []
                };
            }

            const [reward] = await this.findBonusCoinReward(item);

            if (!reward) {
                log.warn(`Unknown reward: promo=${item.promoId}`);
                continue;
            }

            const bonusCoin: PlayerBonusCoinRewardInfo = {
                amount: item.amount,
                rewardedAmount: item.rewardedAmount,
                redeemMinAmount: item.redeemMinAmount,
                games: reward.games,
                startDate: new Date(item.startDate),
                endDate: new Date(item.startDate + item.expireIn)
            };
            if (item.redeemMaxAmount) {
                bonusCoin.redeemMaxAmount = item.redeemMaxAmount;
            }
            if (item.redeemingGame) {
                bonusCoin.underRedeem = true;
                bonusCoin.redeemingGame = item.redeemingGame;
            }
            promos[item.promoId].bonusCoins.push(bonusCoin);
        }
        return values(promos);
    }

    private async getGameBonusCoin(bonusCoinWallet: PlayerBonusCoinWallet,
                                   gameCode: string): Promise<PlayerGameBonusCoin> {
        for (const bonusCoin of bonusCoinWallet.bonusCoins) {
            const [reward, promo] = await this.findBonusCoinReward(bonusCoin, gameCode);
            if (reward) {
                if (bonusCoin.redeemingGame !== undefined && bonusCoin.redeemingGame !== gameCode) {
                    continue;
                }
                const result: PlayerGameBonusCoin = {
                    promoId: bonusCoin.promoId,
                    rewardId: bonusCoin.rewardId,
                    exchangeRate: reward.exchangeRates[bonusCoinWallet.currency],
                };

                if (promo.isExternal()) {
                    result.externalId = promo.getExternalId();
                }
                return result;
            }
        }
    }

    private async findBonusCoinReward(bonusCoin: BonusCoinsData,
                                      gameCode?: string): Promise<[BonusCoinRewardInfo, PromotionImpl]> {
        try {
            const [reward, promo] =
                await PromoService.findReward<PromotionBonusCoinRewardImpl>(bonusCoin.promoId, bonusCoin.rewardId);
            if (!reward) {
                log.warn(`Unknown bonus coin: promo=${bonusCoin.promoId}, reward=${bonusCoin.rewardId}`);
                return [] as any;
            }
            if (!(reward instanceof PromotionBonusCoinRewardImpl)) {
                log.warn(`Reward is not bonus coin: promo=${bonusCoin.promoId}, reward=${bonusCoin.rewardId}`);
                return [] as any;
            }
            const rewardInfo: BonusCoinRewardInfo = reward.toInfo();
            // we should return expired reward to any game
            if (!gameCode || rewardInfo.games.includes(gameCode) || this.isExpiredBonusCoin(bonusCoin)) {
                return [rewardInfo, promo];
            }

            return [] as any;
        } catch (err) {
            if (err instanceof Errors.PromotionNotFoundError) {
                log.warn(`Unknown bonus coin: promo=${bonusCoin.promoId}, reward=${bonusCoin.rewardId}`);
            }

            throw err;
        }
    }

    private isExpiredBonusCoin(bonusCoin: BonusCoinsData): boolean {
        return (bonusCoin.startDate + bonusCoin.expireIn) < Date.now();
    }

    protected async getPromotionsFromWallet(playerCode: string,
                                            playerCurrency: string): Promise<PlayerBonusCoinPromotionInfo[]> {
        const playerPromotionWalletFacade = PlayerPromotionWalletFacade.create(
            this.brand.id, playerCode, playerCurrency);
        const bonusCoinWallet = await playerPromotionWalletFacade.getBonusCoinWallet();
        return this.getPromoInfo(bonusCoinWallet);
    }

    protected async isPromotionActive(playerPromo: PromotionToPlayer, promoType: string): Promise<IsActiveResult> {
        if (playerPromo.status === PromotionToPlayerStatus.STARTED && playerPromo.rewardedAt) {
            const facade = PlayerPromotionWalletFacade.create(this.brand.id,
                playerPromo.playerCode, playerPromo.playerCurrency);
            const hasPromo = await facade.hasPromo(playerPromo.promotionId, promoType);

            if (hasPromo && promoType === PROMO_TYPE.BONUS_COIN) {
                if (hasPromo.existenceType !== BnsWalletExistenceType.EXPIRED_REDEEMABLE) {
                    const minValue = await this.minBonusCoinBet.get();
                    if (minValue) {
                        return {
                            result: (await facade.getBonusCoinWallet()).isGreaterThanMin(minValue)
                        };
                    }
                }
            }

            return hasPromo as IsActiveResult;
        }
        if (playerPromo.status === PromotionToPlayerStatus.STARTED && !playerPromo.rewardedAt) {
            return {
                result: playerPromo.expireAt > new Date()
            };
        }
        return {
            result: playerPromo.status === PromotionToPlayerStatus.CONFIRMED
        };
    }

    private getTotalRequiredBrandBalance(promo: PromotionImpl, promoPlayers: PromotionToPlayer[]): number {
        let totalRequiredBalance = 0;
        for (const reward of promo.getRewards()) {
            const rewardInfo: BonusCoinRewardInfo = reward.toInfo() as BonusCoinRewardInfo;
            totalRequiredBalance += rewardInfo.amount * promoPlayers.length;
        }
        return totalRequiredBalance;
    }

    private async validatePromoForPlayer(promo: PromotionImpl,
                                         playerPromo: PromotionToPlayer,
                                         bonusCoinWallet: PlayerBonusCoinWallet): Promise<void> {
        const rewards: BonusCoinRewardInfo[] = promo.getRewards()
            .map(reward => reward.toInfo() as BonusCoinRewardInfo);
        if (!rewards.filter((reward => reward.exchangeRates[playerPromo.playerCurrency])).length) {
            throw new Errors.PromotionPlayersValidationError(
                "There are no exchange rates for player currency", [playerPromo.playerCode]);
        }

        // check if there are expired but not redeemed bonus coins left in the wallet
        if (bonusCoinWallet.getCurrentPromoId()) {
            const minBonusCoinBet = await this.minBonusCoinBet.get();
            if (bonusCoinWallet.isGreaterThanMin(minBonusCoinBet)) {
                return Promise.reject(new Errors.PromotionPlayersValidationError(
                    "Player has non-redeemed bonus coins", [playerPromo.playerCode]));
            } else {
                await bonusCoinWallet.expireByMin(minBonusCoinBet);
            }
        }
    }

    private async validatePromoIntersection(update: PromotionPlayersUpdate, transaction?: Transaction) {
        const playerCodes = update.promoPlayers.map((p) => p.playerCode);

        await this.validateIfPlayersHaveActiveFreeBets(playerCodes, transaction);
        await this.validateIfPlayersHaveActiveBonusCoins(playerCodes, update.promo.getId(), transaction);
    }

    private async validateIfPlayersHaveActiveFreeBets(playerCodes: string[],
                                                      transaction?: Transaction) {
        const freeBets = await PlayerPromotionDb.getActivePlayersPromotions(
            this.brand.id, playerCodes, PROMO_TYPE.FREEBET, transaction);

        const playersWithFb = new Map<string, number[]>();

        for (const fb of freeBets) {
            if ((await this.isPromotionActive(fb, PROMO_TYPE.FREEBET)).result) {
                if (!playersWithFb.has(fb.playerCode)) {
                    playersWithFb.set(fb.playerCode, []);
                }
                playersWithFb.get(fb.playerCode).push(fb.promotionId);
            }
        }

        if (playersWithFb.size) {
            return Promise.reject(new Errors.PromotionPlayersValidationError(
                "Players have another active freebet promo",
                [...playersWithFb.keys()], playersWithFb));
        }
    }

    private async validateIfPlayersHaveActiveBonusCoins(playerCodes: string[],
                                                        promoId: number,
                                                        transaction?: Transaction) {
        const activePlayerBnsPromos = await PlayerPromotionDb.getActivePlayersPromotions(
            this.brand.id, playerCodes, PROMO_TYPE.BONUS_COIN, transaction);

        const playersWithBNS = new Map<string, number>();
        const playersWithExpiredRedeemableBNS = new Map<string, number>();

        for (const playerPromo of activePlayerBnsPromos) {
            const isActive = await this.isPromotionActive(playerPromo, PROMO_TYPE.BONUS_COIN);
            if (playerPromo.promotionId !== promoId) {
                // here we check other active BNS promotions
                if (isActive.result) {
                    if (isActive.existenceType === BnsWalletExistenceType.EXPIRED_REDEEMABLE) {
                        playersWithExpiredRedeemableBNS.set(playerPromo.playerCode, playerPromo.promotionId);
                    } else {
                        playersWithBNS.set(playerPromo.playerCode, playerPromo.promotionId);
                    }
                }
            } else {
                if (isActive.result && isActive.existenceType === BnsWalletExistenceType.EXPIRED_REDEEMABLE) {
                    playersWithExpiredRedeemableBNS.set(playerPromo.playerCode, playerPromo.promotionId);
                }
            }
        }

        if (playersWithExpiredRedeemableBNS.size) {
            return Promise.reject(new Errors.PromotionPlayersValidationError(
            "Cannot add this player because he has redeemable expired coins from a previous BNS promotion",
            [...playersWithExpiredRedeemableBNS.keys()], playersWithBNS));
        }

        if (playersWithBNS.size) {
            return Promise.reject(new Errors.PromotionPlayersValidationError(
                "Players have another active bonus coin promo",
                [...playersWithBNS.keys()], playersWithBNS));
        }
    }
}
