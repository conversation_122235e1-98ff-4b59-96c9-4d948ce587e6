import {
    BnsWalletExistenceType, BonusFreebetsData,
    FreebetsData,
    PlayerFreebetWallet,
    PlayerGameFreebet,
    PlayerPromotionWalletFacade,
    PROMO_TYPE
} from "@skywind-group/sw-management-promo-wallet";
import * as Errors from "../../../errors";
import {
    FreebetGameConfig,
    FreebetRewardInfo
} from "../../../entities/promotion";
import { PromotionToPlayerStatus } from "../../../models/promotionPlayer";
import { PromotionPlayersUpdate } from "../promotionPlayersUpdate";
import { PromotionRewardService } from "../promotionRewardService";
import { PlayerPromotionDb, PlayerPromotionInfo } from "../playerPromotionDb";
import { PlayerWalletImpl } from "@skywind-group/sw-management-wallet";
import { findOne, getRewardExpirationDate, PromotionImpl } from "../promotion";
import { PromotionFreebetRewardImpl } from "../promotionReward";
import logger from "../../../utils/logger";
import { getCurrencyExchange } from "../../currencyExchange";
import { getLimitsExchanger } from "../../gameLimits/limitsExchanger";
import { Transaction } from "sequelize";
import { values } from "lodash";

const log = logger("free-bets");

export interface PlayerFreebetPromotionInfo extends PlayerPromotionInfo {
    freebets?: PlayerFreebetRewardInfo[];
}

export interface PlayerFreebetRewardInfo {
    currency: string;
    amount: number;
    games: FreebetGameConfig[];
    startDate: Date;
    endDate?: Date;
}

export class FreebetRewardService extends PromotionRewardService {

    public async getPromoInfo(freebetWallet: PlayerFreebetWallet): Promise<PlayerFreebetPromotionInfo[]> {
        const promos: { [promoId: string]: PlayerFreebetPromotionInfo } = {};
        await Promise.all(freebetWallet.freeBets.map(freeBets => {
            return findOne({ id: freeBets.promoId }).then(
                (promo) => this.findFreebetsReward(promo, freeBets.rewardId)
                    .then((reward: FreebetRewardInfo) => {

                        if (!reward) {
                            return;
                        }
                        if (!promos[freeBets.promoId]) {
                            promos[freeBets.promoId] = {
                                promoId: freeBets.promoId,
                                status: PromotionToPlayerStatus.STARTED,
                                expireAt: freeBets.endDate ? new Date(freeBets.endDate) : undefined,
                                freebets: []
                            };
                        }
                        promos[freeBets.promoId].freebets.push({
                            currency: freebetWallet.currency,
                            amount: freeBets.amount,
                            games: reward.games,
                            startDate: new Date(freeBets.startDate),
                            endDate: freeBets.endDate ? new Date(freeBets.endDate) : undefined
                        });
                    }));
        }));
        return values(promos);
    }

    private async findFreebetsReward(promo: PromotionImpl,
                                     rewardId: number | string,
                                     gameCode?: string): Promise<FreebetRewardInfo> {
        try {
            const reward = promo.getRewardById(rewardId);
            if (!reward) {
                log.warn(`Unknown free bet: promo=${promo.getId()}, reward=${rewardId}`);
                return;
            }
            if (!(reward instanceof PromotionFreebetRewardImpl)) {
                log.warn(`Reward is not free bet: promo=${promo.getId()}, reward=${rewardId}`);
                return;
            }
            const rewardInfo: FreebetRewardInfo = reward.toInfo();
            if (gameCode && !rewardInfo.games.some((freebetGameConfig) => freebetGameConfig.gameCode === gameCode)) {
                return;
            }
            return rewardInfo;
        } catch (err) {
            if (err instanceof Errors.PromotionNotFoundError) {
                log.warn(`Unknown free bet: promo=${promo.getId()}, reward=${rewardId}`);
            } else {
                throw err;
            }
        }
    }

    protected async confirmPlayers(update: PromotionPlayersUpdate, transaction?: Transaction) {
        update.updateAllPlayerPromos({ status: PromotionToPlayerStatus.CONFIRMED });
    }

    protected async rewardPlayers(update: PromotionPlayersUpdate,
                                  startDate: Date,
                                  isPendingReward?: boolean,
                                  dbTransaction?: Transaction,
                                  forcePending?: boolean) {
        const playerCodes = update.promoPlayers.map(promoPlayer => promoPlayer.playerCode);
        await this.validateIfPlayersHaveActiveBonusCoins(playerCodes, dbTransaction);

        const transaction = await update.initWalletTransaction();

        const rewardDate: Date = new Date();
        let expireDate: Date;

        for (const reward of update.promo.getRewards()) {
            const rewardInfo: FreebetRewardInfo = reward.toInfo() as FreebetRewardInfo;
            const rewardExpireDate = new Date(getRewardExpirationDate(startDate, rewardInfo) ||
                update.promo.getEndDate().getTime());

            if (!expireDate || expireDate < rewardExpireDate) {
                expireDate = rewardExpireDate;
            }
        }

        const allRewarded: boolean = update.promoPlayers.find((v) => !v.playerCurrency) === undefined;

        for (const playerPromo of update.promoPlayers) {
            if (playerPromo.playerCurrency && !forcePending) {
                const playerWallet = new PlayerWalletImpl(this.brand.id,
                    playerPromo.playerCode, playerPromo.playerCurrency);

                for (const reward of update.promo.getRewards()) {
                    const rewardInfo: FreebetRewardInfo = reward.toInfo() as FreebetRewardInfo;

                    const amount: number = Number.isSafeInteger(playerPromo.data as any) ?
                                           +playerPromo.data : rewardInfo.freebetAmount;

                    const rewardExpireDate = getRewardExpirationDate(startDate, rewardInfo) ||
                        update.promo.getEndDate().getTime();

                    if (!this.playerOrBrandHasAtLeastOneRewardCurrency(playerPromo.playerCurrency, rewardInfo)) {
                        return Promise.reject(new Errors.PromotionPlayersValidationError(
                            "Player doesn't have currency", [playerPromo.playerCode]));
                    }
                    const facade = new PlayerPromotionWalletFacade(playerWallet);

                    await facade.addFreebets(transaction, {
                        promoId: update.promo.getId(),
                        rewardId: reward.getId(),
                        amount: amount,
                        startDate: startDate.getTime(),
                        endDate: rewardExpireDate
                    });
                }

                if (!allRewarded) {
                    update.updatePlayerPromo(playerPromo, {
                        rewardedAt: rewardDate
                    });
                }
            } else {
                update.updatePendingReward({
                    brandId: update.promo.getBrandId(),
                    playerCode: playerPromo.playerCode,
                    promoType: PROMO_TYPE.FREEBET,
                    promoId: update.promo.getId(),
                    startDate: update.promo.getStartDate().getTime(),
                    createdAt: update.promo.getCreatedAt().getTime(),
                    expireDate: expireDate.getTime()
                });
            }
        }

        update.updateAllPlayerPromos({
            status: PromotionToPlayerStatus.STARTED,
            startedAt: startDate,
            expireAt: new Date(expireDate)
        });

        if (allRewarded) {
            update.updateAllPlayerPromos({
                rewardedAt: rewardDate
            });
        }
    }

    protected async revokePlayers(update: PromotionPlayersUpdate): Promise<void> {
        const transaction = await update.initWalletTransaction();

        for (const playerPromo of update.promoPlayers) {
            const brandId = playerPromo.brandId || this.brand.id; // for external promo we save brandId in playerPromo

            const facade = PlayerPromotionWalletFacade.create(brandId,
                playerPromo.playerCode,
                playerPromo.playerCurrency);

            const freebetWallet = await facade.getFreebetWallet(transaction);

            const reward = update.promo.getRewards()[0]; // for 'freebet_simple' promo type we created single reward

            if (!reward) {
                throw new Errors.PromoIsInvalidRewardIsMissing();
            }

            const freebetData: FreebetsData | BonusFreebetsData = freebetWallet.getFreebetPromoData(update.promo.getId(),
                reward.getId());

            if (freebetData) {
                await facade.resetFreebets(transaction, freebetData);
            }
        }

        update.updateAllPlayerPromos({ status: PromotionToPlayerStatus.REVOKED });
    }

    protected async getPlayerGamePromotion(playerCode: string,
                                           playerCurrency: string, gameCode: string): Promise<PlayerGameFreebet[]> {
        const facade = PlayerPromotionWalletFacade.create(this.brand.id, playerCode, playerCurrency);
        const freeBetsWallet = await facade.getFreebetWallet();
        return this.getGameFreebets(freeBetsWallet, gameCode);
    }

    private async getGameFreebets(freeBetsWallet: PlayerFreebetWallet,
                                  gameCode: string): Promise<PlayerGameFreebet[]> {
        const gameFreebets: PlayerGameFreebet[] = [];
        for (const freeBet of freeBetsWallet.freeBets) {
            if (freeBetsWallet.isActive(freeBet)) {
                if (typeof freeBet.promoId !== "number") {
                    continue;
                }
                const promo = await findOne({ id: freeBet.promoId });
                const reward = await this.findFreebetsReward(promo, freeBet.rewardId, gameCode);
                if (!reward) {
                    continue;
                }

                const coin = await this.getCoinOfRewardForGivenGameCode(reward, gameCode, freeBetsWallet.currency);
                const gameFreebet: PlayerGameFreebet = {
                    promoId: freeBet.promoId,
                    rewardId: freeBet.rewardId,
                    coin
                };

                if (promo.isExternal()) {
                    gameFreebet.externalId = promo.getExternalId();
                }

                gameFreebets.push(gameFreebet);
            }
        }
        return gameFreebets.length ? gameFreebets : undefined;
    }

    public async getCoinOfRewardForGivenGameCode(reward: FreebetRewardInfo,
                                                 gameCode: string,
                                                 currency: string): Promise<number> {
        if (reward.games) {
            const rewardGameConfig = reward.games.find(gameConfig => gameConfig.gameCode === gameCode);
            if (rewardGameConfig) {
                const coinConfig = rewardGameConfig.coins.find(config => config[currency] !== undefined);
                if (coinConfig) {
                    return coinConfig[currency].coin;
                } else {
                    const defaultCoinConfig = rewardGameConfig.coins
                        .find(config => config[this.brand.defaultCurrency] !== undefined);
                    const defaultCoin = defaultCoinConfig[this.brand.defaultCurrency].coin;
                    const service = await getCurrencyExchange();
                    const convertedCoin = service.exchange(defaultCoin, this.brand.defaultCurrency, currency);
                    return getLimitsExchanger().roundCoinBet(convertedCoin);
                }
            }
        }
        return undefined;
    }

    protected async getPromotionsFromWallet(playerCode: string,
                                            playerCurrency: string): Promise<PlayerFreebetPromotionInfo[]> {
        const facade = PlayerPromotionWalletFacade.create(this.brand.id, playerCode, playerCurrency);
        const freebetWallet: PlayerFreebetWallet = await facade.getFreebetWallet();
        return this.getPromoInfo(freebetWallet);
    }

    private playerOrBrandHasAtLeastOneRewardCurrency(playerCurrency: string, rewardInfo: FreebetRewardInfo): boolean {
        return rewardInfo.games.some(gameConfig => gameConfig.coins
            .some(coinConfig => Object.keys(coinConfig)
                .some(currency => currency === playerCurrency || currency === this.brand.defaultCurrency)
            ));
    }

    private async validateIfPlayersHaveActiveBonusCoins(playerCodes: string[], transaction?: Transaction) {
        const bonusCoins = await PlayerPromotionDb.getActivePlayersPromotions(
            this.brand.id, playerCodes, PROMO_TYPE.BONUS_COIN, transaction);
        const playersWithBNS = new Map<string, number>();
        const playersWithExpiredRedeemableBNS = new Map<string, number>();

        for (const bns of bonusCoins) {
            const isActive = await this.isPromotionActive(bns, PROMO_TYPE.BONUS_COIN);
            if (isActive.result) {
                if (isActive.existenceType === BnsWalletExistenceType.EXPIRED_REDEEMABLE) {
                    playersWithExpiredRedeemableBNS.set(bns.playerCode, bns.promotionId);
                } else {
                    playersWithBNS.set(bns.playerCode, bns.promotionId);
                }
            }
        }

        if (playersWithExpiredRedeemableBNS.size) {
            return Promise.reject(new Errors.PromotionPlayersValidationError(
                "Cannot add this player because he has redeemable expired coins from a previous BNS promotion",
                [...playersWithExpiredRedeemableBNS.keys()], playersWithBNS));
        }

        if (playersWithBNS.size) {
            return Promise.reject(new Errors.PromotionPlayersValidationError(
                "Players have another active bonus coin promo",
                [...playersWithBNS.keys()], playersWithBNS));
        }
    }
}
