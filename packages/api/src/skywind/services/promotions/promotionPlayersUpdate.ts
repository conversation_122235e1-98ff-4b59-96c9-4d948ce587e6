import { PlayerPromotionDb } from "./playerPromotionDb";
import {
    PromotionToPlayer, PromotionToPlayerImpl,
    PromotionToPlayerOperation,
    PromotionToPlayerStatus
} from "../../models/promotionPlayer";

import {
    BAD_TRANSACTION_ID,
    INSUFFICIENT_BALANCE,
    ITransaction,
    MAX_CAPACITY_REACHED,
    TRANSACTION_EXISTS
} from "@skywind-group/sw-wallet";
import PendingRewards, { PendingReward } from "./pendingPromotion";
import { OPERATION_ID, WalletErrors, WalletFacade } from "@skywind-group/sw-management-wallet";

import { PROMO_TYPE, PromoWalletErrors } from "@skywind-group/sw-management-promo-wallet";
import { WALLET_OPERATION_NAME } from "@skywind-group/sw-management-playservice";
import { Transaction } from "sequelize";
import { PromotionImpl } from "./promotion";

export interface IsActiveResult {
    result: boolean;
    existenceType?: string;
}

/**
 * Update batch of promotion players in atomic way.
 * If wallet transaction is initialized, collect changes in memory and
 * commit/rollback them according to wallet transaction result.
 *
 * WARNING: May have troubles under race conditions, because of limitations
 * on promo intersections, e.g. 1) cannot add bonus coins and free bets to the same player simultaneously,
 * or 2) cannot add multiple bonus coin promos to the same player. If multiple promotions will be
 * committed at the same time, limitation won't be verified correctly and later promo will fail to start.
 * Possible solution - use application lock on any promotion players changes.
 */
export class PromotionPlayersUpdate {

    private walletTransaction: ITransaction;
    private pendingRewardsToRemove: PendingReward[] = [];
    private pendingRewards: PendingReward[] = [];
    private newPromoStatus: string;
    private promoEverStarted: boolean;
    private promoPlayersUpdates: Map<string, PromotionToPlayer> = new Map<string, PromotionToPlayer>();
    private promoPlayersBatchUpdate: PromotionToPlayer;

    public constructor(public promo: PromotionImpl,
                       public promoPlayers: PromotionToPlayerImpl[],
                       userId?: number) {
        promo.setUpdatedUser(userId);
    }

    public async initWalletTransaction(): Promise<ITransaction> {
        const [operationId, operationName] = this.getWalletOperation();
        if (!this.walletTransaction) {
            const trxId = await WalletFacade.generateTrxId();
            this.walletTransaction = await WalletFacade.startTransactionWithID(trxId, {
                operationId,
                operationName,
                params: {
                    promoId: this.promo.getId(),
                }
            });
        }

        return this.walletTransaction;
    }

    public updatePlayerPromo(playerPromo: PromotionToPlayerImpl, data: PromotionToPlayer) {
        if (!this.promoPlayersUpdates.has(playerPromo.playerCode)) {
            this.promoPlayersUpdates.set(playerPromo.playerCode, {});
        }
        const toUpdate: PromotionToPlayer = this.promoPlayersUpdates.get(playerPromo.playerCode);

        if (data.status && data.status === PromotionToPlayerStatus.REVOKED) {
            this.updatePendingReward({
                brandId: this.promo.getBrandId(),
                playerCode: playerPromo.playerCode,
                promoType: this.promo.getType(),
                promoId: this.promo.getId(),
                startDate: this.promo.getStartDate().getTime(),
                createdAt: this.promo.getCreatedAt().getTime(),
                expireDate: null
            });
        }

        PromotionPlayersUpdate.updatePlayerPromoData(toUpdate, data);
    }

    public updateAllPlayerPromos(data: PromotionToPlayer) {
        if (!this.promoPlayersBatchUpdate) {
            this.promoPlayersBatchUpdate = {};
        }

        PromotionPlayersUpdate.updatePlayerPromoData(this.promoPlayersBatchUpdate, data);

        if (data.status && data.status === PromotionToPlayerStatus.REVOKED) {
            for (const playerPromo of this.promoPlayers) {
                this.updatePendingReward({
                    brandId: this.promo.getBrandId(),
                    playerCode: playerPromo.playerCode,
                    promoType: this.promo.getType(),
                    promoId: this.promo.getId(),
                    startDate: new Date(this.promo.getStartDate()).getTime(),
                    createdAt: new Date(this.promo.getCreatedAt()).getTime(),
                    expireDate: null
                });
            }
        }
    }

    public updatePendingReward(pendingReward: PendingReward) {
        this.pendingRewards.push(pendingReward);
    }

    public removePendingRewards(pendingRewards: PendingReward[]) {
        this.pendingRewardsToRemove.push(...pendingRewards);
    }

    public updatePromoStatus(status: string) {
        this.newPromoStatus = status;
    }

    public setPromoEverStarted() {
        this.promoEverStarted = true;
    }

    public async commit(transaction?: Transaction): Promise<void> {
        // no need to rollback pending rewards if something fails later on because
        // remaining pending rewards are validated when trying to apply them
        const addedPending = this.pendingRewards.filter((pending) => pending.expireDate);
        if (addedPending.length) {
            await PendingRewards.updatePending(addedPending);
        }

        if (this.removePendingRewards.length) {
            await PendingRewards.removePending(this.pendingRewardsToRemove);
        }

        if (this.walletTransaction) {
            const trxId = this.walletTransaction.trxId.publicId;

            const [updateData, batchUpdateData] = this.updatePlayerPromoTransaction();
            await PlayerPromotionDb.updatePromotionPlayers(this.promoPlayers, this.promo,
                updateData, batchUpdateData, transaction);

            try {
                await this.walletTransaction.commit();
            } catch (error) {
                if (error !== TRANSACTION_EXISTS) {

                    await PlayerPromotionDb.rollbackPromotionPlayersUpdate(trxId, this.promoPlayers, this.promo);
                    this.promoPlayers.forEach((p) => PromotionPlayersUpdate.rollbackPlayerPromoTransaction(p));

                    if (error === INSUFFICIENT_BALANCE) {
                        return Promise.reject(new WalletErrors.InsufficientEntityBalanceError());
                    }

                    if (error === BAD_TRANSACTION_ID) {
                        return Promise.reject(new WalletErrors.BadTransactionId());
                    }

                    if (error === MAX_CAPACITY_REACHED) {
                        return Promise.reject(new PromoWalletErrors.BonusCoinsUnderRedeemError());
                    }

                    return Promise.reject(error);
                }
            }

            this.updatePromo();
            await PlayerPromotionDb.commitPromotionPlayersUpdate(trxId, this.promoPlayers, this.promo, transaction);
            this.promoPlayers.forEach((p) => PromotionPlayersUpdate.commitPlayerPromoTransaction(p));
        } else {
            this.updatePromo();
            await PlayerPromotionDb.updatePromotionPlayers(this.promoPlayers, this.promo,
                this.promoPlayersUpdates, this.promoPlayersBatchUpdate, transaction);
            this.promoPlayers.forEach((p) => PromotionPlayersUpdate.updatePlayerPromoData(p,
                { ...this.promoPlayersBatchUpdate, ...this.promoPlayersUpdates.get(p.playerCode) }));
        }

        // not critical if crash here because remaining pending rewards are validated when trying to apply them
        const removedPending = this.pendingRewards.filter((pending) => !pending.expireDate);
        if (removedPending.length) {
            await PendingRewards.updatePending(removedPending);
        }
    }

    private getWalletOperation(): [number, string] {
        switch (this.promo.getType()) {
            case PROMO_TYPE.FREEBET:
                return [OPERATION_ID.FREE_BET, WALLET_OPERATION_NAME.FREE_BET];
            case PROMO_TYPE.BONUS_COIN:
                return [OPERATION_ID.BONUS_COIN, WALLET_OPERATION_NAME.BONUS_COIN];
        }
    }

    private updatePromo() {
        this.promo.setStatus(this.newPromoStatus || this.promo.getStatus());
        this.promo.setEverStarted(this.promoEverStarted === true || this.promo.isEverStarted());
    }

    private updatePlayerPromoTransaction(): [Map<string, PromotionToPlayer>, PromotionToPlayer] {
        const activeTransactionId = this.walletTransaction.trxId.publicId;
        const activeOperation: PromotionToPlayerOperation = {
            operationId: this.walletTransaction.operation.operationId,
            promoStatus: this.newPromoStatus,
            batchUpdate: this.promoPlayersBatchUpdate
        };

        const playerPromoUpdateData: Map<string, PromotionToPlayer> = new Map<string, PromotionToPlayer>();
        for (const playerPromo of this.promoPlayers) {
            const isNew = playerPromo.id === undefined;
            let playerPromoActiveOperation: PromotionToPlayerOperation = activeOperation;

            if (isNew || this.promoPlayersUpdates.has(playerPromo.playerCode)) {
                playerPromoActiveOperation = {
                    ...activeOperation,
                    isNew,
                    update: this.promoPlayersUpdates.get(playerPromo.playerCode)
                };

                playerPromoUpdateData.set(playerPromo.playerCode, {
                    activeTransactionId,
                    activeOperation: playerPromoActiveOperation
                });
            }

            playerPromo.activeTransactionId = activeTransactionId;
            playerPromo.activeOperation = playerPromoActiveOperation;
        }

        const batchUpdateData: PromotionToPlayer = { activeTransactionId, activeOperation };

        return [playerPromoUpdateData, batchUpdateData];
    }

    public static commitPlayerPromoTransaction(playerPromo: PromotionToPlayerImpl) {
        const updateData = playerPromo.activeOperation &&
            { ...playerPromo.activeOperation.batchUpdate, ...playerPromo.activeOperation.update };
        playerPromo.activeTransactionId = null;
        playerPromo.activeOperation = null;
        if (updateData) {
            PromotionPlayersUpdate.updatePlayerPromoData(playerPromo, updateData);
        }
    }

    public static rollbackPlayerPromoTransaction(playerPromo: PromotionToPlayerImpl) {
        if (playerPromo.activeOperation && playerPromo.activeOperation.isNew) {
            playerPromo.id = null;
        }
        playerPromo.activeTransactionId = null;
        playerPromo.activeOperation = null;
    }

    public static updatePlayerPromoData(toUpdate: PromotionToPlayer, data: PromotionToPlayer) {
        if (data.status) {
            toUpdate.status = data.status;
        }

        // here we use null to reset state to null, if undefined we don't do update but if null we do
        if (data.startedAt || data.startedAt === null) {
            toUpdate.startedAt = data.startedAt;
        }
        // here we use null to reset state to null, if undefined we don't do update but if null we do
        if (data.rewardedAt || data.rewardedAt === null) {
            toUpdate.rewardedAt = data.rewardedAt;
        }
        // here we use null to reset state to null, if undefined we don't do update but if null we do
        if (data.expireAt || data.expireAt === null) {
            toUpdate.expireAt = data.expireAt;
        }
        if (data.data) {
            toUpdate.data = data.data;
        }
        if (data.playerCurrency) {
            toUpdate.playerCurrency = data.playerCurrency;
        }
        if (data.brandId) {
            toUpdate.brandId = data.brandId;
        }
    }
}
