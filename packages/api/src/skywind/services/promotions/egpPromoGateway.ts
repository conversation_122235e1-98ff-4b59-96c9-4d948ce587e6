import { CommonRewardData, PromotionInfo } from "../../entities/promotion";
import {
    EGPPromoService,
    EGPPromoInfoCreate,
    RewardInfo,
    PromoStatus,
    EGPPromoInfo,
    EGPPromoGetFilters,
    EGPPromoPlayerReward
} from "@skywind-group/sw-gameprovider-adapter-core";
import config from "../../config";
import { InvalidEGPPromoConfiguration, OperationForbidden } from "../../errors";
import { PROMO_TYPE } from "@skywind-group/sw-management-promo-wallet";
import { getEntitySettings } from "../settings";
import { FreeBetsReward } from "../../entities/player";
import { getGame } from "../gameprovider";
import { BaseEntity } from "../../entities/entity";
import { PlayerPromotionInfo } from "./playerPromotionDb";
import * as NodeCache from "node-cache";

export enum PROMO_LOCATION {
    SW = "SW",
    EGP = "EGP" // EGP = external game provider
}

type EGPConfig = {
    url: string;
    promoLocation: PROMO_LOCATION;
};

export const getEGPConfiguration = (gameProviderCode: string, skipError?: boolean) => {
    const egpConfig = config.egpConfigurations[gameProviderCode] as EGPConfig;
    if (!egpConfig && !skipError) {
        throw new OperationForbidden(`Game provider ${gameProviderCode} does not support promotions`);
    }
    if (!egpConfig?.url && !skipError) {
        throw new InvalidEGPPromoConfiguration(`Invalid configuration: url is missing for ${gameProviderCode}`);
    }
    if (!egpConfig?.promoLocation && !skipError) {
        throw new InvalidEGPPromoConfiguration(
            `Invalid configuration: promoLocation is missing for ${gameProviderCode}`
        );
    }
    return egpConfig;
};

interface DestructuredPromoId {
    egpPromoId: string;
    gameProviderCode: string;
}

export const destructureEGPPromoId = (promoId: string | number): DestructuredPromoId | undefined => {
    if (typeof promoId === "number") {
        return undefined;
    }
    if (typeof promoId === "string" && promoId.includes(EGPPromoGateway.egpEncodedPromoIdSeparator)) {
        const [egpPromoId, gameProviderCode] = promoId.split(EGPPromoGateway.egpEncodedPromoIdSeparator);
        return {
            egpPromoId,
            gameProviderCode
        };
    }
    return undefined;
};

export class EGPPromoGateway {
    public static readonly egpEncodedPromoIdSeparator = ":_:";
    private readonly egpPromoService: EGPPromoService;

    constructor(externalGameProviderUrl: string, private readonly EGPCode: string) {
        this.egpPromoService = new EGPPromoService(externalGameProviderUrl);
    }

    private static suffixEGPPromoId(EGPPromoId: string, EGPCode: string) {
        return `${EGPPromoId}${EGPPromoGateway.egpEncodedPromoIdSeparator}${EGPCode.toUpperCase()}`;
    }

    public async createPromo(promo: PromotionInfo, entity: BaseEntity): Promise<PromotionInfo> {
        const siteCode = await this.getSiteCode(entity);
        const egpPromoRequest = await this.mapToEGPPromoCreateRequest(promo, siteCode);
        const { gameProviderPromoId } = await this.egpPromoService.createPromotion(egpPromoRequest);
        return { ...promo, id: EGPPromoGateway.suffixEGPPromoId(gameProviderPromoId, this.EGPCode) as any };
    }

    public async removePromo(egpPromoId: string): Promise<void> {
        await this.egpPromoService.removePromotion(egpPromoId);
    }

    public async addPlayersToPromo(
        egpPromoId: string,
        playerCodes: string[],
        reward?: any
    ): Promise<PromotionInfo> {
        const rewards = this.createPlayerRewards(playerCodes, reward);
        const egpPromoInfo = await this.egpPromoService.addPlayersToPromo(egpPromoId, rewards);
        return this.mapToSWPromoInfo(egpPromoInfo);
    }

    private createPlayerRewards(playerCodes: string[], reward?: any): EGPPromoPlayerReward[] {
        if (reward) {
            return playerCodes.map<EGPPromoPlayerReward>(playerCode => {
                return {
                    playerCode,
                    ...reward,
                    extraData: reward.providerSpecificOptions
                };
            });
        }
        return playerCodes.map<EGPPromoPlayerReward>(playerCode => {
            return { playerCode };
        });
    }

    public async removePlayersFromPromo(egpPromoId: string, playerCode: string): Promise<void> {
        await this.egpPromoService.removePlayerFromPromo(egpPromoId, playerCode);
    }

    public async getPromo(egpPromoId: string): Promise<PromotionInfo> {
        const egpPromoInfo = await this.egpPromoService.getPromo(egpPromoId);
        return this.mapToSWPromoInfo(egpPromoInfo);
    }

    public async getPromoPlayers(egpPromoId: string): Promise<PlayerPromotionInfo[]> {
        const egpPromotion = await this.getPromo(egpPromoId);
        return egpPromotion.customerIds.map(playerCode => this.mapToPlayerPromoInfo(egpPromotion, playerCode));
    }

    public async getEntityPromotions(entity: BaseEntity, filters?: EGPPromoGetFilters): Promise<PromotionInfo[]> {
        const builtFilters: EGPPromoGetFilters = {
            ...(filters || {}),
            operatorIdentifier: await this.getSiteCode(entity)
        };
        return this.getFilteredPromotions(builtFilters);
    }

    public async getPlayerPromotions(entity: BaseEntity, playerCode: string): Promise<PlayerPromotionInfo[]> {
        const entityPromotions = await this.getEntityPromotions(entity);
        const playerPromotions = entityPromotions.filter(promo => promo.customerIds.includes(playerCode));
        return playerPromotions.map(playerPromo => this.mapToPlayerPromoInfo(playerPromo, playerCode));
    }

    public async getPlayerPromotion(egpPromoId: string, playerCode: string): Promise<PlayerPromotionInfo> {
        const playerPromotions = await this.getPromoPlayers(egpPromoId);
        return playerPromotions.find(playerPromo => playerPromo.playerCode === playerCode);
    }

    // we should support filters like playerCode, limit, offset as well
    public async getFilteredPromotions(filters?: EGPPromoGetFilters): Promise<PromotionInfo[]> {
        const egpPromotions = await this.egpPromoService.getFilteredPromotions(filters);
        return egpPromotions.map(egpPromo => this.mapToSWPromoInfo(egpPromo));
    }

    private mapToPlayerPromoInfo(egpPromotion: PromotionInfo, playerCode: string): PlayerPromotionInfo {
        return {
            promoId: egpPromotion.id,
            playerCode,
            externalId: egpPromotion.externalId,
            status: egpPromotion.status
        };
    }

    private mapToSWPromoInfo(egpPromoInfo: EGPPromoInfo): PromotionInfo {
        return {
            id: EGPPromoGateway.suffixEGPPromoId(egpPromoInfo.gameProviderPromoId, this.EGPCode) as any,
            title: egpPromoInfo.title,
            type: egpPromoInfo.type,
            startDate: egpPromoInfo.startDate,
            description: egpPromoInfo.description,
            endDate: egpPromoInfo.endDate,
            brandId: egpPromoInfo.operatorIdentifier as number,
            startRewardOnGameOpen: false,
            providerSpecificOptions: egpPromoInfo.extraData,
            status: egpPromoInfo.status,
            rewards: egpPromoInfo.rewards as CommonRewardData[],
            customerIds: egpPromoInfo.customerIds,
            externalId: egpPromoInfo.operatorPromoId,
            players: this.getPlayers(egpPromoInfo)
        };
    }

    private getPlayers(egpPromoInfo: EGPPromoInfo): { playerCode: string }[] {
        if (Array.isArray(egpPromoInfo.players)) {
            return egpPromoInfo.players;
        }
        return Array.isArray(egpPromoInfo.customerIds) && egpPromoInfo.customerIds.map(custId => {
            return  { playerCode: custId };
        });
    }

    private async mapToEGPPromoCreateRequest(promo: PromotionInfo, siteCode?: string): Promise<EGPPromoInfoCreate> {
        const freeBetRewards = promo.rewards as FreeBetsReward[];
        // We need to map all game codes to provider game codes
        for (const reward of freeBetRewards) {
            reward.games = await Promise.all(reward.games.map(async (game) => {
                const dbGame = await getGame(game.gameCode);
                return {
                    ...game,
                    gameCode: dbGame.providerGameCode
                };
            }));
        }
        return {
            operatorPromoId: promo.externalId,
            operatorIdentifier: siteCode || promo.brandId.toString(),
            title: promo.title,
            description: promo.description,
            type: PROMO_TYPE.FREEBET,
            status: promo.status as PromoStatus,
            rewards: promo.rewards as RewardInfo[],
            customerIds: promo.customerIds || [],
            startDate: promo.startDate,
            endDate: promo.endDate,
            extraData: promo.providerSpecificOptions
        };
    }

    private async getSiteCode(entity: BaseEntity): Promise<string> {
        const entitySettings = await getEntitySettings(entity.path);
        return entitySettings.gameProviderSiteCodes?.[this.EGPCode];
    }
}

const gatewayCache = new NodeCache({
    stdTTL: config.cache.ttl,
    checkperiod: config.cache.checkPeriod,
    useClones: false
});

export const getEGPPromoGateway = (url: string, gameProviderCode: string): EGPPromoGateway => {
    const key = `${url}_${gameProviderCode}`;
    const cachedGateway = gatewayCache.get<EGPPromoGateway>(key);
    if (cachedGateway) {
        return gatewayCache.get(key);
    }
    const egpPromoGatewayInstance = new EGPPromoGateway(url, gameProviderCode);
    gatewayCache.set(key, egpPromoGatewayInstance);
    return egpPromoGatewayInstance;
};
