import {
    BonusCoinRewardInfo,
    CommonRewardData,
    ExpiringReward,
    FreebetGameConfig,
    FreebetRewardInfo, PROMO_REWARD_INTERVAL_TYPES
} from "../../entities/promotion";
import { DestroyOptions, Transaction } from "sequelize";
import {
    FreebetRewardDBAttributes,
    FreebetRewardDBInstance,
} from "../../models/promotionFreebetReward";

import { BnsSupportGameValidationResult, PromotionImpl, ValidationResult } from "./promotion";
import { isEmpty } from "lodash";
import { BrandEntity } from "../../entities/brand";
import { getAvailableGamesByCodes } from "../game";
import config from "../../config";
import {
    BonusCoinRewardDBAttributes,
    BonusCoinRewardDBInstance,
} from "../../models/promotionBonusCoinReward";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { BaseEntity } from "../../entities/entity";
import EntitySettingsService from "../../services/settings";
import * as Errors from "../../errors";
import { BONUS_COIN_CURRENCY, PROMO_TYPE } from "@skywind-group/sw-management-promo-wallet";
import { Models } from "../../models/models";

const PromotionFreebetRewardModel = Models.PromotionFreebetReward;
const PromotionBonusCoinRewardModel = Models.PromotionBonusCoinReward;
const PromotionModel = Models.Promotion;

export async function getPromoRewards(promoId: number): Promise<PromotionRewardImpl[]> {
    const promoRewardDBInstance =
        await PromotionModel.findOne({
            where: { id: promoId }, include: [
                {
                    model: PromotionFreebetRewardModel, as: "freebetRewards"
                }
            ]
        });
    return new PromotionImpl(promoRewardDBInstance).getRewards();
}

export async function createFreebetRewards(data: FreebetRewardInfo[],
                                           promoId: number,
                                           transaction: Transaction): Promise<PromotionFreebetRewardImpl[]> {
    return Promise.all(data.map(freebetRewardInfo => {
        return createFreebetReward(freebetRewardInfo, promoId, transaction);
    }));
}

async function createFreebetReward(data: FreebetRewardInfo,
                                   promoId: number,
                                   transaction: Transaction): Promise<PromotionFreebetRewardImpl> {

    const promoFreebetReward: PromotionFreebetRewardImpl = new PromotionFreebetRewardImpl()
        .setPromoId(promoId)
        .setFreebetAmount(data.freebetAmount)
        .setGames(data.games)
        .setExpirationDate(data.expirationDate ? new Date(data.expirationDate) : undefined)
        .setExpirationPeriod(data.expirationPeriod)
        .setExpirationPeriodType(data.expirationPeriodType);

    const item: FreebetRewardDBInstance = await PromotionFreebetRewardModel.create(promoFreebetReward.toDBAttributes(),
        {
            transaction: transaction
        });

    return new PromotionFreebetRewardImpl(item);
}

export async function validateFreebetRewards(rewards: FreebetRewardInfo[],
                                             brand: BrandEntity): Promise<ValidationResult> {
    if (isEmpty(rewards)) {
        return {
            passed: false,
            failedMessage: "rewards is required for freebet promo"
        };
    }

    const uniqGameCodes = [
        ...new Set(
            [].concat(...rewards.map(freebetReward => {
                    return freebetReward.games.map(gameConfig => gameConfig.gameCode);
                })
            )
        )
    ];

    const games = await getAvailableGamesByCodes(uniqGameCodes, brand);
    for (const game of games) {
        if (!game.features.isFreebetSupported) {
            return {
                passed: false,
                failedMessage: `free bets unsupported in game ${game.code}`
            };
        }
    }

    for (const freebetReward of rewards) {
        if (isNaN(freebetReward.freebetAmount)
            || freebetReward.freebetAmount < 1
            || freebetReward.freebetAmount > config.promo.maxFreebetsToAssign
            || (freebetReward.freebetAmount % 1) !== 0) {
            return {
                passed: false,
                failedMessage: `freebetAmount must be integer between 1 and ${config.promo.maxFreebetsToAssign}`
            };
        }
        for (const gameConfig of freebetReward.games) {
            if (!gameConfig.coins) {
                return {
                    passed: false,
                    failedMessage: `the coins field is required for game ${gameConfig.gameCode}`
                };
            }
            if (!Array.isArray(gameConfig.coins)) {
                return {
                    passed: false,
                    failedMessage: `the coins field should be an array for game ${gameConfig.gameCode}`
                };
            }
            if (!gameConfig.coins.length) {
                return {
                    passed: false,
                    failedMessage: `the coins array should not be empty for game ${gameConfig.gameCode}`
                };
            }
            for (const coinConfig of gameConfig.coins) {
                const currencies = Object.keys(coinConfig);
                for (const currency of currencies) {
                    if (!brand.currencyExists(currency)) {
                        return {
                            passed: false,
                            failedMessage: "currency unavailable for this brand"
                        };
                    }
                    if (typeof coinConfig[currency].coin !== "number" || +coinConfig[currency].coin <= 0) {
                        return {
                            passed: false,
                            failedMessage: "coin should be a positive number"
                        };
                    }
                }
            }
        }
    }

    return {
        passed: true
    };
}

export async function createBonusCoinRewards(data: BonusCoinRewardInfo[],
                                             promoId: number,
                                             entity: BaseEntity,
                                             transaction: Transaction): Promise<PromotionBonusCoinRewardImpl[]> {
    const currencyRates = (await new EntitySettingsService(entity).get()).virtualCurrencyRate;
    const rewards: PromotionBonusCoinRewardImpl[] = [];
    for (const info of data) {
        const exchangeRates = info.exchangeRates || (currencyRates && currencyRates[BONUS_COIN_CURRENCY]);
        if (!exchangeRates) {
            return Promise.reject(new Errors.ValidationError(
                "exchangeRates should be defined in reward or in virtualCurrencyRate entity setting"));
        }

        const promoBonusCoinReward: PromotionBonusCoinRewardImpl = new PromotionBonusCoinRewardImpl()
            .setPromoId(promoId)
            .setAmount(info.amount)
            .setRedeemMinAmount(info.redeemMinAmount)
            .setRedeemMaxAmount(info.redeemMaxAmount)
            .setGames(info.games)
            .setExchangeRates(exchangeRates)
            .setExpirationPeriod(info.expirationPeriod)
            .setExpirationPeriodType(info.expirationPeriodType);

        rewards.push(promoBonusCoinReward);
    }

    const created = await PromotionBonusCoinRewardModel.bulkCreate(rewards.map(item => item.toDBAttributes()),
        { transaction: transaction, returning: true });

    for (let i = 0; i < rewards.length; i += 1) {
        rewards[i].setId(+created[i].id);
    }

    return rewards;
}

export async function validateBonusCoinRewards(rewards: BonusCoinRewardInfo[],
                                               brand: BrandEntity): Promise<ValidationResult> {
    if (isEmpty(rewards)) {
        return {
            passed: false,
            failedMessage: "rewards is required for free coin promo"
        };
    }

    const games: string[] = [];
    const gameValidationResultGrouped: BnsSupportGameValidationResult = {};
    for (const reward of rewards) {
        if (isNaN(reward.amount) || reward.amount < 10 || (reward.amount % 1) !== 0
            || reward.amount > Number.MAX_SAFE_INTEGER) {
            return {
                passed: false,
                failedMessage: `amount must be integer greater than or equal to 10 and less or equal than ${Number.MAX_SAFE_INTEGER}`
            };
        }
        if (!reward.expirationPeriodType || !PROMO_REWARD_INTERVAL_TYPES.includes(reward.expirationPeriodType)) {
            return {
                passed: false,
                failedMessage: `expirationPeriodType must be one of these: ${PROMO_REWARD_INTERVAL_TYPES.join(", ")}`
            };
        }
        if (!reward.expirationPeriod || !Number.isInteger(+reward.expirationPeriod) ||
            +reward.expirationPeriod > Number.MAX_SAFE_INTEGER || +reward.expirationPeriod < 1) {
            return {
                passed: false,
                failedMessage: `expirationPeriod must be integer greater than 1 and less 
                or equal than ${Number.MAX_SAFE_INTEGER}`
            };
        }
        if (isNaN(reward.redeemMinAmount) || reward.redeemMinAmount < 1 || (reward.redeemMinAmount % 1) !== 0
            || reward.redeemMinAmount > Number.MAX_SAFE_INTEGER) {
            return {
                passed: false,
                failedMessage: `redeemMinAmount must be integer greater than 1 and less 
                or equal than ${Number.MAX_SAFE_INTEGER}`
            };
        }
        if (reward.redeemMaxAmount !== undefined) {
            if (isNaN(reward.redeemMaxAmount) || reward.redeemMaxAmount < 1 || (reward.redeemMaxAmount % 1) !== 0
                || reward.redeemMaxAmount > Number.MAX_SAFE_INTEGER) {
                return {
                    passed: false,
                    failedMessage: `redeemMaxAmount must be integer greater than 1 and less 
                    or equal than ${Number.MAX_SAFE_INTEGER}`
                };
            }
            if (reward.redeemMaxAmount < reward.redeemMinAmount) {
                return {
                    passed: false,
                    failedMessage: "redeemMaxAmount must be greater or equal to redeemMinAmount"
                };
            }
            if (reward.redeemMaxAmount < reward.amount) {
                return {
                    passed: false,
                    failedMessage: "redeemMaxAmount must be greater or equal to rewarded amount"
                };
            }
        }
        if (!Array.isArray(reward.games) || !reward.games.length) {
            return {
                passed: false,
                failedMessage: "games must be an array with at least one game code"
            };
        }
        const availableGames = await getAvailableGamesByCodes(reward.games, brand);
        const gamesValidationResult: BnsSupportGameValidationResult = {};
        for (const gameCode of reward.games) {
            const game = availableGames.find(g => g.code === gameCode);
            if (!game) {
                gamesValidationResult[gameCode] = `invalid game code ${gameCode}`;
                continue;
            }
            if (!game.features.isBonusCoinsSupported) {
                gamesValidationResult[gameCode] = `bonus coins are not supported for game ${gameCode}`;
                continue;
            }
            if (game.features.isGRCGame) {
                gamesValidationResult[gameCode] = `bonus coins are not available for GRC game ${gameCode}`;
                continue;
            }
            if (games.indexOf(gameCode) >= 0) {
                return {
                    passed: false,
                    failedMessage: `games cannot be intersected between rewards: ${gameCode}`
                };
            }
            games.push(gameCode);
        }
        if (reward.games.length === Object.keys(gamesValidationResult).length) {
            return {
                passed: false,
                failedMessage: "All games do not support bns"
            };
        }

        if (reward.exchangeRates) {
            const currencies = Object.keys(reward.exchangeRates);
            for (const currency of currencies) {
                if (!Currencies.exists(currency)) {
                    return {
                        passed: false,
                        failedMessage: `currency ${currency} is not exist`
                    };
                }
                if (typeof reward.exchangeRates[currency] !== "number" || reward.exchangeRates[currency] <= 0) {
                    return {
                        passed: false,
                        failedMessage: `exchange rate for ${currency} should be a positive number`
                    };
                }
            }
        }
        Object.assign(gameValidationResultGrouped, gamesValidationResult);
    }

    return {
        passed: true,
        unsupportedGames: gameValidationResultGrouped
    };
}

export async function deletePromoRewards(promoId: number, transaction?: Transaction) {
    const destroyOptions: DestroyOptions = { where: { promoId: promoId } };
    if (transaction) {
        destroyOptions.transaction = transaction;
    }

    await PromotionFreebetRewardModel.destroy(destroyOptions);
    await PromotionBonusCoinRewardModel.destroy(destroyOptions);
}

export async function updateRewardExpiration(promoType: PROMO_TYPE, reward: PromotionRewardImpl & ExpiringReward,
                                             transaction?: Transaction): Promise<void> {
    const update: any = {
        expirationPeriod: reward.expirationPeriod, expirationPeriodType: reward.expirationPeriodType
    };
    switch (promoType) {
        case PROMO_TYPE.FREEBET:
            await PromotionFreebetRewardModel.update(update, { where: { id: reward.getId() }, transaction });
            break;
        case PROMO_TYPE.BONUS_COIN:
            await PromotionBonusCoinRewardModel.update(update, { where: { id: reward.getId() }, transaction });
            break;
        default:
            await transaction.rollback();
            return Promise.reject(new Errors.ValidationError("Unsupported promo type"));
    }
}

export interface PromotionRewardImpl {
    getId(): number;

    toInfo(): CommonRewardData;
}

export class PromotionFreebetRewardImpl implements PromotionRewardImpl {
    private id: number;

    private promoId: number;

    private freebetAmount: number;
    private games: FreebetGameConfig[];
    private expirationPeriod: number;
    private expirationPeriodType: string;
    private expirationDate: Date;

    constructor(item?: FreebetRewardDBInstance) {
        if (!item) {
            return;
        }

        this.id = +item.id;
        this.promoId = +item.promoId;

        this.freebetAmount = +item.freebetAmount;
        this.games = item.games;
        this.expirationPeriod = +item.expirationPeriod;
        this.expirationPeriodType = item.expirationPeriodType;
        this.expirationDate = item.expirationDate;
    }

    public toInfo(): FreebetRewardInfo {
        return {
            id: this.id,
            promoId: this.promoId,
            freebetAmount: this.freebetAmount,
            games: this.games,
            expirationPeriod: this.expirationPeriod ? this.expirationPeriod : undefined,
            expirationPeriodType: this.expirationPeriodType ? this.expirationPeriodType : undefined,
            expirationDate: this.expirationDate ? this.expirationDate.toJSON() : undefined
        };
    }

    public toDBAttributes(): FreebetRewardDBAttributes {
        return {
            id: this.id,
            promoId: this.promoId,
            freebetAmount: this.freebetAmount,
            games: this.games,
            expirationPeriod: this.expirationPeriod ? this.expirationPeriod : undefined,
            expirationPeriodType: this.expirationPeriodType ? this.expirationPeriodType : undefined,
            expirationDate: this.expirationDate ? this.expirationDate : undefined
        };
    }

    public setId(value: number): PromotionFreebetRewardImpl {
        this.id = value;
        return this;
    }

    public getId(): number {
        return this.id;
    }

    public setPromoId(value: number): PromotionFreebetRewardImpl {
        this.promoId = value;
        return this;
    }

    public setGames(value: FreebetGameConfig[]): PromotionFreebetRewardImpl {
        this.games = value;
        return this;
    }

    public setFreebetAmount(value: number): PromotionFreebetRewardImpl {
        this.freebetAmount = value;
        return this;
    }

    public setExpirationPeriod(value: number): PromotionFreebetRewardImpl {
        this.expirationPeriod = value;
        return this;
    }

    public setExpirationPeriodType(value: string): PromotionFreebetRewardImpl {
        this.expirationPeriodType = value;
        return this;
    }

    public setExpirationDate(value: Date): PromotionFreebetRewardImpl {
        this.expirationDate = value;
        return this;
    }
}

export class PromotionBonusCoinRewardImpl implements PromotionRewardImpl {
    private id: number;

    private promoId: number;

    private amount: number;
    private redeemMinAmount: number;
    private redeemMaxAmount: number;
    private games: string[];
    private exchangeRates: any;
    private expirationPeriod: number;
    private expirationPeriodType: string;

    constructor(item?: BonusCoinRewardDBInstance) {
        if (!item) {
            return;
        }

        this.id = +item.id;
        this.promoId = +item.promoId;

        this.amount = +item.amount;
        this.redeemMinAmount = +item.redeemMinAmount;
        this.redeemMaxAmount = +item.redeemMaxAmount || undefined;
        this.games = item.games;
        this.exchangeRates = item.exchangeRates;
        this.expirationPeriod = +item.expirationPeriod;
        this.expirationPeriodType = item.expirationPeriodType;
    }

    public toInfo(): BonusCoinRewardInfo {
        return {
            id: this.id,
            promoId: this.promoId,
            amount: this.amount,
            redeemMinAmount: this.redeemMinAmount,
            redeemMaxAmount: this.redeemMaxAmount,
            games: this.games,
            exchangeRates: this.exchangeRates,
            expirationPeriod: this.expirationPeriod ? this.expirationPeriod : undefined,
            expirationPeriodType: this.expirationPeriodType ? this.expirationPeriodType : undefined,
        };
    }

    public toDBAttributes(): BonusCoinRewardDBAttributes {
        return {
            id: this.id,
            promoId: this.promoId,
            amount: this.amount,
            redeemMinAmount: this.redeemMinAmount,
            redeemMaxAmount: this.redeemMaxAmount,
            games: this.games,
            exchangeRates: this.exchangeRates,
            expirationPeriod: this.expirationPeriod ? this.expirationPeriod : undefined,
            expirationPeriodType: this.expirationPeriodType ? this.expirationPeriodType : undefined,
        };
    }

    public setId(value: number): PromotionBonusCoinRewardImpl {
        this.id = value;
        return this;
    }

    public getId(): number {
        return this.id;
    }

    public setPromoId(value: number): PromotionBonusCoinRewardImpl {
        this.promoId = value;
        return this;
    }

    public setGames(value: string[]): PromotionBonusCoinRewardImpl {
        this.games = value;
        return this;
    }

    public setAmount(value: number): PromotionBonusCoinRewardImpl {
        this.amount = value;
        return this;
    }

    public setRedeemMinAmount(value: number): PromotionBonusCoinRewardImpl {
        this.redeemMinAmount = value;
        return this;
    }

    public setRedeemMaxAmount(value: number): PromotionBonusCoinRewardImpl {
        this.redeemMaxAmount = value;
        return this;
    }

    public setExchangeRates(value: any): PromotionBonusCoinRewardImpl {
        this.exchangeRates = value;
        return this;
    }

    public setExpirationPeriod(value: number): PromotionBonusCoinRewardImpl {
        this.expirationPeriod = value;
        return this;
    }

    public setExpirationPeriodType(value: string): PromotionBonusCoinRewardImpl {
        this.expirationPeriodType = value;
        return this;
    }
}
