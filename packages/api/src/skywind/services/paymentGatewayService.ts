import { IncomingMessage } from "http";
import { InitializeDepositData, InitializePaymentInfo, InitializeWithdrawalData } from "../entities/payment_method";
import { PublicKeyValue } from "../entities/payment";
import { BaseEntity } from "../entities/entity";
import { getPlayerResponsibleGamingService } from "./playerResponsibleGaming";
import { PaymentAPIError, PaymentAPITransientError, AmountIsNegativeError } from "../errors";

import config from "../config";
import request = require("request");
import { validateMaintenance } from "./entity";

const PM_SET_PUBLIC_KEY_API = "/v1/gateways/key";
const PM_GET_PUBLIC_KEY_API = "/v1/gateways/key";

const PM_INIT_DEPOSIT_API = "/v1/gateways/deposit/";
const PM_INIT_WITHDRAW_API = "/v1/gateways/withdraw/";

const paymentApi = config.paymentApi;

export const getPaymentGatewayService = (): PaymentGatewayService => {
    return new PaymentGatewayAPIService();
};

export interface PaymentGatewayService {
    deposit(entity: BaseEntity, data: InitializeDepositData): Promise<InitializePaymentInfo>;
    withdraw(entity: BaseEntity, data: InitializeWithdrawalData): Promise<InitializePaymentInfo>;
    setPublicKey(entity: BaseEntity, publicKey: string): Promise<void>;
    getPublicKey(entity: BaseEntity): Promise<string>;
    generatePublicKey(entity: BaseEntity): Promise<string>;
}

class PaymentGatewayAPIService implements PaymentGatewayService {

    public async deposit(entity: BaseEntity,
                         data: InitializeDepositData
    ): Promise<InitializePaymentInfo> {
        validateMaintenance(entity);
        this.validateInitializeData(data);
        await getPlayerResponsibleGamingService(entity).validatePlayerDepositRestriction(data.customerId, data.amount);
        return post<InitializePaymentInfo>(PM_INIT_DEPOSIT_API + entity.id, data);
    }

    public async withdraw(entity: BaseEntity,
                          data: InitializeWithdrawalData
    ): Promise<InitializePaymentInfo> {
        validateMaintenance(entity);
        this.validateInitializeData(data);
        return post<InitializePaymentInfo>(PM_INIT_WITHDRAW_API + entity.id, data);
    }

    private validateInitializeData(data: InitializeDepositData | InitializeWithdrawalData): void {
        if (data.amount <= 0) {
            throw new AmountIsNegativeError();
        }
    }

    public setPublicKey(entity: BaseEntity, publicKey: string): Promise<void> {
        return post(PM_SET_PUBLIC_KEY_API, {
            entityId: entity.id,
            publicKey: publicKey,
        });
    }

    public async getPublicKey(entity: BaseEntity): Promise<string> {
        const response = await get<PublicKeyValue>(PM_GET_PUBLIC_KEY_API, {
            entityId: entity.id,
        });

        return response.publicKey;
    }

    public async generatePublicKey(entity: BaseEntity): Promise<string> {
        const response = await post<PublicKeyValue>(PM_GET_PUBLIC_KEY_API, {
            entityId: entity.id,
        });

        return response.publicKey;
    }
}

async function post<T>(url: string, req): Promise<T> {
    return new Promise<T>((resolve, reject) => {
        request.post(url, {
            baseUrl: paymentApi.url,
            headers: [{ "Content-Type": "application/json" }],
            json: req,
        }, processResponse(resolve, reject));
    });
}

async function get<T>(url: string, req): Promise<T> {
    return new Promise<T>((resolve, reject) => {
        request.get(url, {
            baseUrl: paymentApi.url,
            json: true,
            headers: [{ "Content-Type": "application/json" }],
            qs: req,
        }, processResponse(resolve, reject));
    });
}

function processResponse(resolve, reject): (error: any,
                                            response: IncomingMessage,
                                            body: any) => Promise<any> {
    return function(error: Error,
                    response: IncomingMessage,
                    body: any): Promise<any> {
        if (error) {
            return reject(new PaymentAPITransientError());
        } else if (response.statusCode !== 200) {
            if (response.statusCode >= 400 && response.statusCode < 500) {
                return reject(new PaymentAPIError(response.statusCode, body));
            } else {
                return reject(new PaymentAPITransientError(response.statusCode, body));
            }
        } else {
            return resolve(body);
        }
    };
}
