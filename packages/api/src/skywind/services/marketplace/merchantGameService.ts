import { getEntityJurisdictionService } from "../entityJurisdiction";
import { Merchant, MerchantMarketplaceGameList } from "../../entities/merchant";
import { BrandEntity } from "../../entities/brand";
import { getEntitySettings } from "../settings";
import { getAllGames } from "../game";
import { WhereOptions } from "sequelize";
import EntityCache from "../../cache/entity";
import { getMerchantSearchService } from "../merchant";

export class MerchantGameService {

    public async gameList(merchantType: string[], gameCode?: string): Promise<MerchantMarketplaceGameList>  {
        const games = [];
        const languages = [];
        const currencies = [];
        const jurisdictions = [];
        const merchantCodes = [];

        const merchants: Merchant[] = await getMerchantSearchService().findAllByType(merchantType) as Merchant[];

        for (const merchant of merchants) {
            const marketplaceGameInfo = await this.gameListByMerchant(merchant, gameCode);
            if (!marketplaceGameInfo) {
                continue;
            }

            games.push(...marketplaceGameInfo.games);
            languages.push(...marketplaceGameInfo.languages);
            currencies.push(...marketplaceGameInfo.currencies);
            jurisdictions.push(...marketplaceGameInfo.jurisdictions);
            merchantCodes.push(merchant.code);
        }

        const uniqueGames = games.reduce(this.uniqueGames, []);

        return {
            games: uniqueGames,
            languages: [...new Set(languages)],
            currencies: [...new Set(currencies)],
            jurisdictions: [...new Set(jurisdictions)],
            merchantCodes,
        };
    }

    public async gameListByMerchant(merchant: Merchant, gameCode?: string): Promise<MerchantMarketplaceGameList> {

        const brand: BrandEntity = await EntityCache.findOne<BrandEntity>({ id: merchant.brandId });
        const settings = await getEntitySettings(brand.path);
        if (!settings.isMarketplaceSupported) {
            return;
        }
        const query: WhereOptions<any> = { isMarketplaceSupported: true };
        if (gameCode) {
            query.code = gameCode;
        }
        const games = await getAllGames(brand, undefined, query);
        if (!games || !games.length) {
            return;
        }

        const brandJurisdictions = await getEntityJurisdictionService().findAll({
            entityId: brand.id
        });

        return {
            games,
            languages: brand.getLanguages(),
            currencies: brand.getCurrencies(),
            jurisdictions: brandJurisdictions.map(jurisdiction => jurisdiction.code)
        };
    }

    private uniqueGames(acc, game) {
        const notFound = !acc.find(g => g.code === game.code);
        if (notFound) {
            acc.push(game);
        }
        return acc;
    }
}
