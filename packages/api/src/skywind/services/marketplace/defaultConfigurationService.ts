import { BrandEntity } from "../../entities/brand";
import { EntityGame, Game } from "../../entities/game";
import { getSchemaDefinitionService, SchemaDefinitionHelper } from "../gameLimits/schemaDefinition";
import { SegmentSearchData } from "../../models/segment";
import {
    GameLimits,
    GameLimitsByCurrencyCode,
    GameLimitsConfigurationDetailedInfo,
    GameLimitsConfigurationInStorage,
    MarketplaceGameLimitsConfiguration,
} from "../../models/gameLimitsConfiguration";
import { getMarketplaceSegmentService, MarketplaceSegmentService } from "./marketplaceSegmentService";
import { getCurrencyMultiplierService } from "../gameLimits/currencyMultiplier";
import { CurrencyMultiplier, DEFAULT_CURRENCY } from "../../models/currencyMultiplier";
import { findOneEntityGame } from "../game";
import { findGameLimitsConfiguration } from "../gameLimits/gameLimitsStorage";
import { getNewLimitsFacade } from "../gameLimits/limitsFacade";
import { LIMITS_CONFIGURATION_STATUS } from "../gameLimits/helper";
import { REGION } from "../../entities/merchant";
import { DefaultConfigurationFacade, getConfigurationFacade } from "../gameLimits/defaultConfigurationFacade";

export abstract class DefaultConfigurationService {
    public segmentService: MarketplaceSegmentService;

    constructor(region: REGION) {
        this.segmentService = getMarketplaceSegmentService(region);
    }

    public async getDefaultsForEntity(entity: BrandEntity,
                                      entityGames: EntityGame[],
                                      entityConfigurations: GameLimitsConfigurationDetailedInfo[],
                                      externalBrandId: string,
                                      segment?: SegmentSearchData)
        : Promise<MarketplaceGameLimitsConfiguration[]> {

        const needAppendDefaults = segment
                                   ? this.segmentService.isDefault(segment)
                                   : true;
        if (!needAppendDefaults) {
            return [];
        }

        let defaults = [];

        for (const entityGame of entityGames) {
            const gameConfigurations = entityConfigurations
                .filter(config => config.gameCode === entityGame.game.code);

            const schemaDefinition = await getSchemaDefinitionService()
                .retrieve(entityGame.game.schemaDefinitionId);
            const facade = await getConfigurationFacade(schemaDefinition, entityGame.game.code);

            if (!facade.configuration || !schemaDefinition) {
                continue;
            }
            const entityGameDefaults = await this.getDefaultsFromSchema(
                entity,
                entityGame.game,
                gameConfigurations,
                facade,
                externalBrandId,
                segment);

            defaults = defaults.concat(entityGameDefaults);
        }
        return defaults;
    }

    public async getDefaultsForEntityGame(entity: BrandEntity,
                                          entityConfigurations: GameLimitsConfigurationDetailedInfo[],
                                          gameCode: string,
                                          externalBrand: string,
                                          segment?: SegmentSearchData): Promise<GameLimitsConfigurationDetailedInfo[]> {
        const entityGame: EntityGame = await findOneEntityGame(entity, gameCode);

        return this.getDefaultsForEntity(entity, [entityGame], entityConfigurations, externalBrand, segment);
    }

    public async buildConfigurableLimits(entity: BrandEntity,
                                         game: Game,
                                         facade: DefaultConfigurationFacade,
                                         currency: string,
                                         currencyMultipliers?: CurrencyMultiplier): Promise<GameLimits> {

        const configurableFields: string[] = new SchemaDefinitionHelper(facade.definition, currency)
            .getConfigurableFields();

        const gameLimitsConfiguration: GameLimitsConfigurationInStorage = await findGameLimitsConfiguration(
            entity,
            game.code,
            facade.definition.id
        );

        const builtLimits = await getNewLimitsFacade(entity).build(
            facade,
            game.totalBetMultiplier,
            currency,
            gameLimitsConfiguration,
            true,
            currencyMultipliers,
            undefined,
            undefined,
            game.features?.isSmartRoundingSupported ?? true
        );

        const configurableLimits: GameLimits = {};
        for (const configurableField of configurableFields) {
            configurableLimits[configurableField] = builtLimits[configurableField];
        }

        return configurableLimits;
    }

    protected abstract getDefaultsFromSchema(entity: BrandEntity,
                                             game: Game,
                                             gameConfigurations: GameLimitsConfigurationDetailedInfo[],
                                             facade: DefaultConfigurationFacade,
                                             externalBrandId: string,
                                             segment: SegmentSearchData):
        Promise<MarketplaceGameLimitsConfiguration[]>;
}

class DefaultConfigurationEUService extends DefaultConfigurationService {

    public async buildConfigurableLimitsByCurrency(entity: BrandEntity,
                                                   game: Game,
                                                   facade: DefaultConfigurationFacade,
                                                   currencyMultipliers?: CurrencyMultiplier)
        : Promise<GameLimitsByCurrencyCode> {
        const currency = currencyMultipliers?.baseCurrency || DEFAULT_CURRENCY;

        const limits: GameLimits = await this.buildConfigurableLimits(
            entity, game, facade, currency, currencyMultipliers);

        if (!limits || Object.keys(limits).length === 0) {
            return;
        }

        return {
            [currency]: limits
        };
    }

    private isDefaultConfigurationsExist(configurations: GameLimitsConfigurationDetailedInfo[]) {
        return configurations.some(configuration => configuration?.segment?.externalId === "");
    }

    protected async getDefaultsFromSchema(entity: BrandEntity,
                                          game: Game,
                                          gameConfigurations: GameLimitsConfigurationDetailedInfo[],
                                          facade: DefaultConfigurationFacade,
                                          externalBrandId: string,
                                          segment: SegmentSearchData):
        Promise<MarketplaceGameLimitsConfiguration[]> {

        const exists = this.isDefaultConfigurationsExist(gameConfigurations);
        if (exists) {
            return [];
        }

        const currencyMultiplier = await getCurrencyMultiplierService().findOne(entity);

        const gameLimits = await this.buildConfigurableLimitsByCurrency(
            entity, game, facade, currencyMultiplier);

        if (!gameLimits) {
            return [];
        }

        const currency = currencyMultiplier?.baseCurrency || DEFAULT_CURRENCY;
        return [
            {
                status: LIMITS_CONFIGURATION_STATUS.ACTIVE,
                id: facade.configurationId,
                isDefault: true,
                entityId: entity.id,
                gameCode: game.code,
                createdAt: facade.createdAt,
                updatedAt: facade.updatedAt,
                externalBrandId,
                gameLimits,
                title: "Default",
                description: `Default configuration for game ${game.title}`,
                segment: this.segmentService.getDefault(currency)
            }
        ];
    }
}

class DefaultConfigurationAsiaService extends DefaultConfigurationService {
    public async buildConfigurableLimitsByCurrency(entity: BrandEntity,
                                                   game: Game,
                                                   facade: DefaultConfigurationFacade,
                                                   currency: string)
        : Promise<GameLimitsByCurrencyCode> {

        const limits: GameLimits = await this.buildConfigurableLimits(entity, game, facade, currency, null);

        if (!limits || Object.keys(limits).length === 0) {
            return;
        }

        return {
            [currency]: limits
        };
    }

    protected async getDefaultsFromSchema(entity: BrandEntity,
                                          game: Game,
                                          gameConfigurations: GameLimitsConfigurationDetailedInfo[],
                                          facade: DefaultConfigurationFacade,
                                          externalBrandId: string,
                                          segment: SegmentSearchData):
        Promise<MarketplaceGameLimitsConfiguration[]> {

        const currencies: string[] = this.getCurrenciesWithMissingConfigurations(entity, gameConfigurations, segment);
        if (!currencies.length) {
            return [];
        }

        const defaults: MarketplaceGameLimitsConfiguration[] = [];

        for (const currency of currencies) {
            const gameLimits: GameLimitsByCurrencyCode = await this.buildConfigurableLimitsByCurrency(
                entity, game, facade, currency);

            if (!gameLimits) {
                continue;
            }

            defaults.push({
                status: LIMITS_CONFIGURATION_STATUS.ACTIVE,
                id: facade.configurationId,
                isDefault: true,
                gameCode: game.code,
                entityId: entity.id,
                externalBrandId,
                createdAt: facade.createdAt,
                updatedAt: facade.updatedAt,
                gameGroupName: null,
                gameLimits,
                title: "Default",
                description: `Default configuration for game ${game.title} and currency ${currency}`,
                segment: this.segmentService.getDefault(currency)
            });
        }

        return defaults;
    }

    private getCurrenciesWithMissingConfigurations(entity: BrandEntity,
                                                   configurations: GameLimitsConfigurationDetailedInfo[],
                                                   segment?: SegmentSearchData): string[] {

        const currencySegment = segment?.segment?.currency;
        const entityCurrencies = currencySegment && currencySegment.length ? currencySegment : entity.getCurrencies();
        return entityCurrencies.filter(currency =>
            !configurations.some(config => {
                const segmentDimensions = config?.segment?.segment;
                if (!segmentDimensions || !segmentDimensions.vipLevel || !segmentDimensions.currency) {
                    return;
                }
                const segmentProperties = Object.keys(segmentDimensions);

                // default segment should include only currency and vipLevel
                return segmentDimensions.currency[0] === currency &&
                    segmentDimensions.vipLevel[0] === "" && segmentProperties.length === 2;
            })
        );
    }
}

export const getDefaultConfigurationService = (region: REGION): DefaultConfigurationService =>
    region === REGION.ASIA
    ? new DefaultConfigurationAsiaService(region)
    : new DefaultConfigurationEUService(region);
