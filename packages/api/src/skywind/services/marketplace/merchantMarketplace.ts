import {
    CreateBetConfigurationRequest,
    MerchantMarketplaceGameList, REGION
} from "../../entities/merchant";
import {
    MarketplaceGameLimitsConfiguration,
    SetGameLimitsConfigurationStatus
} from "../../models/gameLimitsConfiguration";
import { SegmentSearchData } from "../../models/segment";
import { BaseEntity } from "../../entities/entity";
import { MerchantGameService } from "./merchantGameService";
import {
    getMerchantConfigurationService,
} from "./merchantConfigurationService";

export const getMerchantMarketplaceService = (region: REGION) =>
    new MerchantMarketplaceService(region);

class MerchantMarketplaceService {
    constructor(private region: REGION,
                private gameService = new MerchantGameService()) {
    }

    public async getGameList(merchantType: string, gameCode?: string): Promise<MerchantMarketplaceGameList> {
        return this.gameService.gameList(merchantType.split(","), gameCode);
    }

    public async getGameLimitsConfigurationsList(merchantType: string,
                                                 merchantCode?: string,
                                                 gameCode?: string,
                                                 externalBrandId?: string,
                                                 allStatuses?: boolean,
                                                 segment?: SegmentSearchData):
        Promise<MarketplaceGameLimitsConfiguration[]> {

        return getMerchantConfigurationService(this.region)
            .getGameLimitsConfigurationsList(
                merchantType,
                merchantCode,
                gameCode,
                externalBrandId,
                allStatuses,
                segment);

    }

    public async createBetConfiguration(brand: BaseEntity, body: CreateBetConfigurationRequest)
        : Promise<MarketplaceGameLimitsConfiguration> {

        return getMerchantConfigurationService(this.region).createBetConfiguration(brand, body);
    }

    public async setBetConfigurationStatus(gameLimitsConfigurationId: number,
                                           request: SetGameLimitsConfigurationStatus)
        : Promise<MarketplaceGameLimitsConfiguration> {

        return getMerchantConfigurationService(this.region)
            .setBetConfigurationStatus(gameLimitsConfigurationId, request);
    }

}
