import { Segment, SEGMENT_SEARCH_MODE, SegmentDimensions, SegmentSearchData } from "../../models/segment";
import { getSearchSegmentsService } from "../gameLimits/segment";
import { REGION } from "../../entities/merchant";

export abstract class MarketplaceSegmentService {
    public async searchEntitySegments(entityGameIds: number[],
                                      segment: SegmentSearchData,
                                      allStatuses: boolean): Promise<Segment[]> {

        let segments: Segment[] = [];
        const externalResellerPath = [];

        for (const entityGameId of entityGameIds) {
            const segmentValues = {};

            for (const segmentDimension in segment.segment) {
                if (segment.segment.hasOwnProperty(segmentDimension)) {
                    if (segmentDimension === "resellerId") {
                        externalResellerPath.push(...segment.segment[segmentDimension]);
                    } else {
                        segmentValues[segmentDimension] = segment.segment[segmentDimension];
                    }
                }
            }

            const matchedSegments = await getSearchSegmentsService().search(
                entityGameId,
                segmentValues,
                segment.externalResellerId,
                segment.externalId,
                segment.mode || SEGMENT_SEARCH_MODE.LIKE,
                allStatuses);
            if (matchedSegments && matchedSegments.length) {
                segments.push(...matchedSegments);
            }
        }

        if (segment.mode === SEGMENT_SEARCH_MODE.EXACT) {
            segments = segments
                .filter(foundSegment => this.isSegmentsDeepEqual(foundSegment.segment, segment.segment));
        }

        return segments;
    }

    public abstract isDefault(segment: Segment);

    public abstract searchEquals(segment: Segment);

    public abstract getDefault(currency: string): Segment;

    private isSegmentsDeepEqual(segmentToCreate: SegmentDimensions, segment: SegmentDimensions): boolean {
        let isEqual = false;
        for (const segmentDimension in segment) {
            if (segment.hasOwnProperty(segmentDimension)) {
                if (!segmentToCreate[segmentDimension]) {
                    isEqual = false;
                    break;
                }
                const oldSegment = [... new Set(segment[segmentDimension])];
                const newSegment = [... new Set(segmentToCreate[segmentDimension])];
                let valuesEqual = oldSegment.length === newSegment.length;
                if (valuesEqual) {
                    valuesEqual = oldSegment.every(value => newSegment.includes(value));
                }
                if (!valuesEqual) {
                    isEqual = false;
                    break;
                }

                isEqual = true;
            }
        }

        return isEqual;
    }
}

class MarketplaceSegmentEUService extends MarketplaceSegmentService {
    public isDefault(segment: Segment) {
        if (!segment || typeof segment !== "object" || Array.isArray(segment)) {
            return;
        }
        return segment.externalId === "";
    }

    public getDefault(currency: string): Segment {
        return {
            externalId: "",
            segment: {}
        };
    }

    public searchEquals(segment: Segment): Segment & SegmentSearchData {
        return { ...segment, mode: SEGMENT_SEARCH_MODE.LIKE };
    }

}

class MarketplaceSegmentAsiaService extends MarketplaceSegmentService {
    public isDefault(segment: Segment) {
        if (!segment || typeof segment !== "object" || Array.isArray(segment)) {
            return;
        }
        const segmentDimension: SegmentDimensions = segment?.segment;
        const vipLevel = segmentDimension?.vipLevel || [];

        return Object.keys(segmentDimension).length === 2 && vipLevel[0] === "";
    }

    public getDefault(currency: string): Segment {
        return {
            segment: {
                vipLevel: [""],
                currency: [currency]
            },
            priority: 999
        };
    }

    public searchEquals(segment: Segment): Segment & SegmentSearchData {
        return { ...segment, mode: SEGMENT_SEARCH_MODE.EXACT };
    }

}

export const getMarketplaceSegmentService = (region: REGION): MarketplaceSegmentService =>
    region === REGION.ASIA
       ? new MarketplaceSegmentAsiaService()
       : new MarketplaceSegmentEUService();
