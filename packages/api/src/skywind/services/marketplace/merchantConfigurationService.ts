import { BrandEntity } from "../../entities/brand";
import { EntityGame } from "../../entities/game";
import { Segment, SegmentSearchData } from "../../models/segment";
import { Transaction, WhereOptions, Op } from "sequelize";
import EntityCache from "../../cache/entity";
import {
    GameLimitsConfigurationDetailedInfo,
    MarketplaceGameLimitsConfiguration,
    SetGameLimitsConfigurationStatus
} from "../../models/gameLimitsConfiguration";
import { getGameLimitsConfigurationService } from "../gameLimits/gameLimitsConfiguration";
import { BaseEntity } from "../../entities/entity";
import { getSchemaDefinitionService, SchemaDefinitionHelper } from "../gameLimits/schemaDefinition";
import { findAllEntityGames, findOneEntityGame } from "../game";
import { getMarketplaceSegmentService, MarketplaceSegmentService } from "./marketplaceSegmentService";
import { CreateBetConfigurationRequest, REGION } from "../../entities/merchant";
import { sequelize } from "../../storage/db";
import { getCurrencyMultiplierService } from "../gameLimits/currencyMultiplier";
import { CurrencyMismatch } from "../../errors";
import { DefaultConfigurationService, getDefaultConfigurationService } from "./defaultConfigurationService";
import { getMerchantSearchService } from "../merchant";
import { GameLimitsStorage } from "../gameLimits/gameLimitsStorage";
import { LIMITS_CONFIGURATION_STATUS } from "../gameLimits/helper";
import { Models } from "../../models/models";

const MerchantModel = Models.MerchantModel;

abstract class MerchantConfigurationService {
    public segmentsService: MarketplaceSegmentService;
    public defaultsFromSchemaService: DefaultConfigurationService;

    constructor(region: REGION) {
        this.segmentsService = getMarketplaceSegmentService(region);
        this.defaultsFromSchemaService = getDefaultConfigurationService(region);
    }

    public async getGameLimitsConfigurationsList(merchantType: string,
                                                 merchantCode?: string,
                                                 gameCode?: string,
                                                 externalBrandId?: string,
                                                 allStatuses?: boolean,
                                                 segment?: SegmentSearchData):
        Promise<MarketplaceGameLimitsConfiguration[]> {

        let configurations = [];
        const merchants = await getMerchantSearchService().findAllByTypeAndCode(merchantType, merchantCode);

        for (const merchant of merchants) {
            const entity: BrandEntity = await EntityCache.findOne<BrandEntity>({ id: merchant.brandId });
            const entityGames: EntityGame[] = await this.getConfigurableEntityGames(entity, gameCode);

            let entityConfigurations: GameLimitsConfigurationDetailedInfo[] = await this.configurationListByEntity(
                entity,
                entityGames,
                externalBrandId,
                true,
                segment) || [];

            const defaults = await this.defaultsFromSchemaService
                .getDefaultsForEntity(entity, entityGames, entityConfigurations, externalBrandId, segment);

            if (!allStatuses) {
                entityConfigurations = entityConfigurations
                    .filter(config => config.status === LIMITS_CONFIGURATION_STATUS.ACTIVE ||
                        config.status === LIMITS_CONFIGURATION_STATUS.INACTIVE);
            }

            configurations = configurations.concat(
                [ ...entityConfigurations, ...defaults ].map(this.decorateConfigurationWithMerchantCode(merchant.code))
            );
        }

        return configurations;
    }

    public async createBetConfiguration(brand: BaseEntity,
                                        body: CreateBetConfigurationRequest)
        : Promise<MarketplaceGameLimitsConfiguration> {

        await this.validateBetConfigCreation(brand, body);

        const equalConfigurations = await this.searchEqualsConfigurations(body);
        let createdConfiguration: GameLimitsConfigurationDetailedInfo;

        await sequelize.transaction(async (transaction: Transaction) => {

            const isDefaultSegment = this.segmentsService.isDefault(body.segment);
            if (isDefaultSegment) {
                const defaultLimits = await this.defaultsFromSchemaService
                    .getDefaultsForEntityGame(
                        brand as BrandEntity,
                        equalConfigurations || [],
                        body.gameCode,
                        body.externalBrandId,
                        body.segment) || [];

                const defaultLimit = defaultLimits.shift();

                if (defaultLimit) {
                    defaultLimit.status = LIMITS_CONFIGURATION_STATUS.ARCHIVED;
                    await getGameLimitsConfigurationService(brand).create(
                        brand, defaultLimit, true, transaction);
                }
            }

            createdConfiguration = await getGameLimitsConfigurationService(brand)
                .create(brand, body, true, transaction);

            await this.archiveDuplicates(brand.path, equalConfigurations, transaction);
        });

        const createdConfig = await getGameLimitsConfigurationService(brand)
            .retrieve(brand, createdConfiguration.id, true);
        return this.decorateConfigurationWithMerchantCode(body.merchantCode)(createdConfig);
    }

    public async setBetConfigurationStatus(gameLimitsConfigurationId: number,
                                           request: SetGameLimitsConfigurationStatus)
        : Promise<MarketplaceGameLimitsConfiguration> {

        if (request.isDefault) {
            const segment = this.segmentsService.getDefault(request.currency);
            const entity: BrandEntity = await EntityCache.findOne<BrandEntity>({ id: request.entityId });
            const entityGame: EntityGame = await findOneEntityGame(entity, request.gameCode);

            const entityConfigurations: GameLimitsConfigurationDetailedInfo[] = await this.configurationListByEntity(
                entity,
                [entityGame],
                null,
                false,
                segment) || [];

            // request was sent with id from schema but game limits configuration already exists
            if (entityConfigurations.length) {
                return this.setStatus(entityConfigurations[0].id, request);
            }

            const defaultLimits = await this.defaultsFromSchemaService
                .getDefaultsForEntityGame(
                    entity,
                    entityConfigurations,
                    request.gameCode,
                    request.externalBrandId,
                    segment) || [];

            const defaultLimit = defaultLimits.shift();
            defaultLimit.status = request.status;

            // create game limits copied from schema
            const createdConfig = await getGameLimitsConfigurationService(entity).create(
                entity, defaultLimit, true);

            const merchant = await MerchantModel.findOne({ where: { brandId: entity.id } });

            return this.decorateConfigurationWithMerchantCode(merchant.get("code"))(createdConfig);

        } else {
            return this.setStatus(gameLimitsConfigurationId, request);
        }

    }

    protected async setStatus(gameLimitsId: number, request: SetGameLimitsConfigurationStatus)
        : Promise<MarketplaceGameLimitsConfiguration> {

        const config = await getGameLimitsConfigurationService().setStatus(
            gameLimitsId,
            request);
        const merchant = await MerchantModel.findOne({ where: { brandId: config.entityId } });

        return this.decorateConfigurationWithMerchantCode(merchant.get("code"))(config);
    }

    protected async configurationListByEntity(entity: BaseEntity,
                                              entityGames: EntityGame[],
                                              externalBrandId?: string,
                                              allStatuses?: boolean,
                                              segment?: SegmentSearchData)
        : Promise<GameLimitsConfigurationDetailedInfo[]> {

        const entityGameIds = entityGames.map(entityGame => entityGame.id);

        const options: WhereOptions<any> = { entityGameId: { [Op.in]: entityGameIds } };
        if (externalBrandId) {
            options.externalBrandId = externalBrandId;
        }

        if (segment) {
            const segments = await this.segmentsService.searchEntitySegments(entityGameIds, segment, allStatuses);

            if (segments && segments.length) {
                options.segmentId = { [Op.in]: segments.map(s => s.id) };
            } else {
                return [];
            }
        }

        let entityConfigurations = await getGameLimitsConfigurationService(entity)
            .list(entity, options, true, true) || [];
        if (!allStatuses) {
            entityConfigurations = entityConfigurations
                .filter(config => config.status === LIMITS_CONFIGURATION_STATUS.ACTIVE ||
                    config.status === LIMITS_CONFIGURATION_STATUS.INACTIVE);
        }

        return entityConfigurations;
    }

    protected async getConfigurableEntityGames(entity: BaseEntity, gameCode?: string): Promise<EntityGame[]> {

        const queries = new Map();
        const gameQuery: WhereOptions<any> = {
            features: {
                [Op.contains]: {
                    isMarketplaceSupported: true
                }
            }
        };
        if (gameCode) {
            gameQuery.code = gameCode;
        }
        queries.set("game", gameQuery);

        const entityGames: EntityGame[] = await findAllEntityGames(entity, queries, true);

        const configurableEntityGames = [];
        for (const entityGame of entityGames) {
            if (entityGame.game.schemaDefinitionId !== null) {
                const schemaDefinition = await getSchemaDefinitionService()
                    .retrieve(entityGame.game.schemaDefinitionId);

                const isGameConfigurable: boolean = new SchemaDefinitionHelper(schemaDefinition).isSchemaConfigurable();
                if (isGameConfigurable) {
                    configurableEntityGames.push(entityGame);
                }
            }
        }

        return configurableEntityGames;
    }

    protected abstract validateBetConfigCreation(brand: BaseEntity, body: CreateBetConfigurationRequest);

    private async archiveDuplicates(brandPath: string,
                                    equalConfigurations: GameLimitsConfigurationDetailedInfo[],
                                    transaction: Transaction) {
        if (!equalConfigurations || !equalConfigurations.length) {
            return;
        }

        const [{ schemaDefinitionId, gameCode, segmentId }] = equalConfigurations;
        const gameLimitsStorage = new GameLimitsStorage(brandPath, schemaDefinitionId, gameCode, undefined, segmentId);

        for (const configuration of equalConfigurations) {
            await getGameLimitsConfigurationService().performSetStatus(
                gameLimitsStorage,
                configuration.segmentId,
                LIMITS_CONFIGURATION_STATUS.ARCHIVED,
                transaction);
        }
    }

    private decorateConfigurationWithMerchantCode(merchantCode) {
        return (config) => ({
            ...config,
            merchantCode
        });
    }

    private async searchEqualsConfigurations(body: CreateBetConfigurationRequest)
        : Promise<GameLimitsConfigurationDetailedInfo[]> {

        const segmentForSearch: Segment & SegmentSearchData = this.segmentsService.searchEquals(body.segment);
        const merchant = await getMerchantSearchService().findOneByTypeAndCode(
            body.merchantType || body.merchantTypes, body.merchantCode);

        const entity: BrandEntity = await EntityCache.findOne<BrandEntity>({ id: merchant.brandId });
        const entityGames: EntityGame[] = await this.getConfigurableEntityGames(entity, body.gameCode);

        return this.configurationListByEntity(
            entity,
            entityGames,
            body.externalBrandId,
            false,
            segmentForSearch);
    }
}

class MerchantConfigurationAsiaService extends MerchantConfigurationService {
    protected async validateBetConfigCreation(brand: BaseEntity, body: CreateBetConfigurationRequest) {
        return;
    }
}

class MerchantConfigurationEUService extends MerchantConfigurationService {
    protected async validateBetConfigCreation(brand: BaseEntity, body: CreateBetConfigurationRequest) {
        const baseCurrency: string = body.gameLimits && Object.keys(body.gameLimits)[0];
        const currencyMultiplier = await getCurrencyMultiplierService().findOne(brand);
        const configBaseCurrency = currencyMultiplier?.baseCurrency;

        if (!configBaseCurrency || configBaseCurrency !== baseCurrency) {
            throw new CurrencyMismatch("Invalid base currency");
        }
    }
}

export const getMerchantConfigurationService = (region: REGION) => {
    return region === REGION.ASIA
           ? new MerchantConfigurationAsiaService(region)
           : new MerchantConfigurationEUService(region);
};
