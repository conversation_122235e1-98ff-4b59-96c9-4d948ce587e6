import { literal, Op, QueryTypes } from "sequelize";
import { sequelize as db } from "../storage/db";
import {
    DividedEntitySettings,
    EntitiesSettings,
    EntitySettings,
    EntitySettingsUpdate,
    GlobalSettings,
    GlobalSettingsUpdate
} from "../entities/settings";
import * as redis from "../storage/redis";
import * as Errors from "../errors";
import config from "../config";
import { BaseEntity, ChildEntity } from "../entities/entity";
import {
    filterObjectFields,
    mergeArrayToObject,
    PLAYER_CODE_MAX_LENGTH,
    PLAYER_CODE_MIN_LENGTH
} from "../utils/common";
import { getBrands, getChildIds, getParentIds } from "./entity";
import { BrandEntity } from "../entities/brand";
import { getGameGroupService } from "./gamegroup";
import { getBrandPlayerService } from "./brandPlayer";
import { DeferredPaymentMethod } from "@skywind-group/sw-deferred-payment";
import { Models } from "../models/models";
import { isArray, isEqual, isObject, mergeWith } from "lodash";
import MergedEntitySettingsCache from "../cache/mergedEntitySettings";

const GLOBAL_SETTINGS_KEY: string = "sw-management-api:settings";
const ENTITY_SETTINGS_KEY: string = "sw-management-api:entitySettings";

const EntityModel = Models.EntityModel;
const PlayerModel = Models.PlayerModel;
const GameGroupModel = Models.GameGroupModel;
const UserModel = Models.UserModel;

export const IP_WHITELIST_KEY: string = "ipWhitelist";
export const BO_WHITELIST_KEY: string = "boIpWhitelist";
export const USER_WHITELIST_KEY: string = "userIpWhitelist";

export async function getGlobalSettings(): Promise<GlobalSettings> {
    return redis.usingDb<GlobalSettings>(async (db) => {
        const settingsDefaults = config.settingsDefaults;
        const settingsNames = Object.keys(settingsDefaults);

        const values = await db.hmget(GLOBAL_SETTINGS_KEY, ...settingsNames);
        const result = Object.assign({}, settingsDefaults);
        for (let i = 0; i < settingsNames.length; i++) {
            const name = settingsNames[i];
            if (values[i]) {
                result[name] = JSON.parse(values[i]);
            }
        }
        return result;
    });
}

export async function getGlobalSetting(name: string): Promise<any> {
    const settingsDefaults = config.settingsDefaults;
    if (!settingsDefaults[name]) {
        return Promise.reject(new Errors.ValidationError(`unknown setting ${name}`));
    }
    return redis.usingDb<GlobalSettings>(async (db) => {
        const value = await db.hget(GLOBAL_SETTINGS_KEY, name);
        return value ? JSON.parse(value) : settingsDefaults[name];
    });
}

export async function updateGlobalSettings(update: GlobalSettingsUpdate): Promise<GlobalSettings> {
    const toUpdate = {};
    const settingsDefaults = config.settingsDefaults;
    const names = Object.keys(settingsDefaults);
    // sanitize received settings
    for (const name of names) {
        if (update[name] !== undefined) {
            const isTypeEquals = typeof (update[name]) === typeof (settingsDefaults[name]);
            const isValidNumber = typeof (update[name]) !== "number" || +update[name] >= 0;
            if (!isTypeEquals || !isValidNumber) {
                return Promise.reject(new Errors.ValidationError(`invalid type of ${name}`));
            }
            toUpdate[name] = JSON.stringify(update[name]);
        }
    }

    return redis.usingDb(async (db) => {
        return db.hmset(GLOBAL_SETTINGS_KEY, toUpdate);
    }).then(getGlobalSettings);
}

export async function resetGlobalSettings(): Promise<GlobalSettings> {
    return redis.usingDb(async (db) => {
        return db.del(GLOBAL_SETTINGS_KEY);
    }).then(getGlobalSettings);
}

async function getOnlyEntitySettings(path: string): Promise<EntitySettings> {
    const settings = await redis.usingDb<EntitySettings>(async (db) => {
        const key = getSettingsKey(path);
        const value = await db.get(key);

        if (value) {
            const settings: EntitySettings = JSON.parse(value);
            const filteredSettings = filterObjectFields<EntitySettings>(settings, filterSettings);

            if (settings.templates) {
                await db.set(key, JSON.stringify(filteredSettings));
            }

            return filteredSettings;
        }
    });
    addDefaultSmsTemplateForBO(settings);
    return settings || {} as any;
}

async function getDividedEntitySettings(path: string, keys: string[]): Promise<DividedEntitySettings> {
    const parentKeys = keys.filter(key => !getPathFromKey(key).endsWith(path));

    const ownSettings = await getOnlyEntitySettings(path);
    const parentSettings = await redis.usingDb<EntitySettings>(async (db) => {
        const values: EntitySettings[] = await db.mget(...parentKeys)
            .then(list => list && list.map(value => value && JSON.parse(value)));
        return mergeEntitySettings(values);
    });

    return {
        parent: parentSettings,
        own: ownSettings,
    };
}

export async function getChildEntitiesSettings(childEntities: BaseEntity[]): Promise<EntitiesSettings> {
    const keys = childEntities.map(child => ENTITY_SETTINGS_KEY + child.path.substring(0, child.path.length - 1));

    const childsSettings: EntitySettings[] = await redis.usingDb<EntitySettings[]>(async (db) => {
        const values = await db.mget(...keys);
        return values.map(value => value ? JSON.parse(value) : undefined);
    });

    const settings: EntitiesSettings = {};
    if (!childsSettings) {
        return settings;
    }
    for (const [i, childSettings] of childsSettings.entries()) {
        const entityPath = getPathFromKey(keys[i]);
        addDefaultSmsTemplateForBO(childSettings);
        settings[entityPath] = childSettings;
    }

    return settings;
}

function getEntitiesSettingsKeys(path: string): string[] {
    const keys = [];
    let parentKey = ENTITY_SETTINGS_KEY;

    for (const part of path.split(":").filter(p => !!p)) {
        keys.push(parentKey);
        parentKey += ":" + part;
    }
    keys.push(parentKey);
    return keys;
}

const getPathFromKey = (key: string): string => key.replace(ENTITY_SETTINGS_KEY, "") + ":";

export async function getEntitiesSettings(path: string): Promise<EntitiesSettings> {
    const keys: string[] = getEntitiesSettingsKeys(path);

    return redis.usingDb<EntitiesSettings>(async (db) => {
        const entitiesSettings: EntitiesSettings = {};
        await db.mget(...keys)
            .then(list => list && list.map((value, i) => {
                    const entityPath = getPathFromKey(keys[i]);
                    entitiesSettings[entityPath] = value && JSON.parse(value);
                }
            ));
        const currentSettings: EntitySettings = mergeEntitySettings(Object.values(entitiesSettings));
        entitiesSettings[getPathFromKey(keys[keys.length - 1])] = currentSettings;

        return entitiesSettings;
    });
}

export async function getEntitySettings(path: string,
                                        ownSettingsOnly?: boolean,
                                        extendSettings?: boolean): Promise<EntitySettings> {
    if (ownSettingsOnly) {
        return getOnlyEntitySettings(path);
    }

    const keys = getEntitiesSettingsKeys(path);
    const settings = await MergedEntitySettingsCache.find(path, keys);

    if (extendSettings) {
        settings.dividedSettings = await getDividedEntitySettings(path, keys);
    }

    return settings;
}

export function getMergedEntitySettings(keys: string[]): Promise<EntitySettings> {
    return redis.usingDb<EntitySettings>(async (db) => {
        const values: EntitySettings[] = await db.mget(...keys)
            .then(list => list && list.map(value => value && JSON.parse(value)));
        const mergedSettings = mergeEntitySettings(values);
        addDefaultSmsTemplateForBO(mergedSettings);

        return mergedSettings;
    });
}

function mergeEntitySettings(list: EntitySettings[]): EntitySettings {
    const settings = {} as EntitySettings;
    list.filter(item => item).forEach(result => {
        Object.keys(result).forEach(key => {
             if ([BO_WHITELIST_KEY, USER_WHITELIST_KEY].includes(key)) {
                settings[key] = (settings[key] || [])
                    .concat(result[key])
                    .filter((ip, index, a) =>
                        a.indexOf(ip) === index
                    );
            } else if (isObject(result[key]) && !Array.isArray(result[key])) {
                settings[key] = mergeWith(settings[key], result[key], (objValue, newValue, key) => {
                    if (isArray(objValue)) {
                        return newValue;
                    }
                });
            } else {
                settings[key] = result[key];
            }
        });
    });
    return settings;
}

export default class EntitySettingsService {
    constructor(public entity: BaseEntity) {
    }

    public async get(ownSettingsOnly: boolean = false): Promise<EntitySettings> {
        return getEntitySettings(this.entity.path, ownSettingsOnly);
    }

    public async patch(settings: EntitySettingsUpdate): Promise<EntitySettings> {
        await redis.usingDb(async (db) => {
            const key = getSettingsKey(this.entity.path);
            const oldSettings = await db.get(key);

            let mergedSettings;
            if (oldSettings) {
                mergedSettings = mergeArrayToObject([JSON.parse(oldSettings), settings]);
            } else {
                mergedSettings = settings;
            }

            const value = JSON.stringify(filterObjectFields<EntitySettings>(mergedSettings, filterSettings));
            return db.set(key, value);
        });

        MergedEntitySettingsCache.reset(this.entity.path);

        return this.get();
    }

    public async update(settings: EntitySettings): Promise<EntitySettings> {

        await redis.usingDb(async (db) => {
            const key = getSettingsKey(this.entity.path);
            return db.set(key, JSON.stringify(filterObjectFields<EntitySettings>(settings, filterSettings)));
        });

        MergedEntitySettingsCache.reset(this.entity.path);

        return this.get();
    }

    public async reset(): Promise<EntitySettings> {
        await redis.usingDb(async (db) => {
            const key = getSettingsKey(this.entity.path);
            return db.del(key);
        });

        MergedEntitySettingsCache.reset(this.entity.path);

        return this.get();
    }

    public async validate(settings: EntitySettingsUpdate): Promise<void> {
        const childIds = getChildIds(this.entity);
        const entityIds = [this.entity.id, ...childIds];

        if (settings.maxTestPlayers) {
            await getBrandPlayerService()
                .checkNumberOfTestPlayers(this.entity as BrandEntity, settings as EntitySettings);
        }

        if (settings.isPlayerCodeUniqueInSubtree) {
            await this.validateDuplicatePlayers(entityIds);
        }

        if (settings.uniqueEntityNamesInSubtree) {
            await this.validateDuplicateEntities(entityIds);
        }

        if (settings.uniqueUsernamesInSubtree) {
            await this.validateDuplicateUsernames(entityIds);
        }

        if (settings.gameGroupsInheritance !== undefined) {
            await this.validateGameGroupInheritance();

            if (settings.gameGroupsInheritance) {
                await this.validateDuplicateGameGroups(entityIds);
            } else {
                await this.validatePlayerParentGameGroup(entityIds);
            }
        }

        const entitySettings: EntitySettings = await getEntitySettings(this.entity.path);

        if (settings.defaultGameGroup) {
            const inheritance = typeof settings.gameGroupsInheritance === "boolean"
                ? settings.gameGroupsInheritance
                : entitySettings.gameGroupsInheritance;
            await getGameGroupService()
                .findOne(this.entity, { name: settings.defaultGameGroup }, true, inheritance);
        }

        const supportedBonusPaymentMethod = "supportedBonusPaymentMethod" in settings ?
            settings.supportedBonusPaymentMethod :
            entitySettings.supportedBonusPaymentMethod;
        if (settings.bonusPaymentMethod !== DeferredPaymentMethod.MANUAL &&
            [DeferredPaymentMethod.CREDIT, DeferredPaymentMethod.BONUS].includes(settings.bonusPaymentMethod) &&
            supportedBonusPaymentMethod !== settings.bonusPaymentMethod) {
            throw new Errors.ValidationError(`${settings.bonusPaymentMethod} is not supported`);
        }

        const useCountriesFromJurisdiction = typeof settings.useCountriesFromJurisdiction === "boolean"
            ? settings.useCountriesFromJurisdiction
            : entitySettings.useCountriesFromJurisdiction;
        if (useCountriesFromJurisdiction &&
            Array.isArray(settings.restrictedCountries) &&
            !isEqual(settings.restrictedCountries, entitySettings.restrictedCountries)) {
            throw new Errors.ValidationError("restrictedCountries can be updated only for useCountriesFromJurisdiction=false");
        }

    }

    private async validateDuplicateEntities(entityIds: number[]) {
        const duplicatedEntity = await EntityModel.findOne({
            attributes: ["name"],
            where: { id: { [Op.in]: entityIds } },
            group: ["name"],
            having: literal("COUNT(*) > 1") as any
        });
        if (duplicatedEntity) {
            throw new Errors.EntityHasDuplicates("child entities");
        }
    }

    private async validateDuplicatePlayers(entityIds: number[]) {
        const duplicatedPlayer = await PlayerModel.findOne({
            attributes: ["code"],
            where: { brandId: { [Op.in]: entityIds } },
            group: ["code"],
            having: literal("COUNT(*) > 1") as any
        });
        if (duplicatedPlayer) {
            throw new Errors.EntityHasDuplicates("players");
        }
    }

    private async validateDuplicateUsernames(entityIds: number[]) {
        const duplicatedUser = await UserModel.findOne({
            attributes: ["name"],
            where: { entityId: { [Op.in]: entityIds } },
            group: ["name"],
            having: literal("COUNT(*) > 1") as any
        });
        if (duplicatedUser) {
            throw new Errors.EntityHasDuplicates("user names");
        }
    }

    private async validateDuplicateGameGroups(entityIds: number[]) {
        const brands = getBrands(this.entity);

        const sql = "SELECT gg.id, gg.name, gg.brand_id as brandid FROM game_groups gg " +
            "INNER JOIN (SELECT name FROM game_groups " +
            "WHERE game_groups.brand_id IN (:entityIds) " +
            "GROUP BY name HAVING COUNT(*) > 1) subtree_gg ON subtree_gg.name = gg.name";
        // TODO: maybe there is a bug here. maybe need to use brandId instead of brandid
        const duplicatedGameGroups: { brandid: number, name: string }[] =
            await db.query(sql, { type: QueryTypes.SELECT, replacements: { entityIds } });

        for (const brand of brands) {
            const parentIds = [brand.id, ...getParentIds(brand as BaseEntity)];

            const parentGameGroups = duplicatedGameGroups
                .filter(gameGroup => parentIds.includes(gameGroup.brandid))
                .map(gameGroup => gameGroup.name);
            const duplicatedNames = new Set();

            parentGameGroups.forEach((gameGroup, i) => {
                if (i + 1 < parentGameGroups.length) {
                    const duplicate = parentGameGroups.includes(gameGroup, i + 1);
                    if (duplicate) {
                        duplicatedNames.add(gameGroup);
                    }
                }
            });

            if (duplicatedNames.size) {
                throw new Errors.EntityHasDuplicates(`game groups: ${[...duplicatedNames.values()]}`);
            }

        }
    }

    private async validatePlayerParentGameGroup(entityIds: number[]) {
        const playerWithParentGameGroup = await PlayerModel.findOne({
            where: { brandId: { [Op.in]: entityIds } },
            include: [
                {
                    model: GameGroupModel,
                    where: { brandId: { [Op.ne]: literal("\"player\".\"brand_id\"") } }
                }
            ],
        });

        if (playerWithParentGameGroup) {
            throw new Errors.ValidationError("Parent game groups are used");
        }
    }

    private async validateGameGroupInheritance() {
        if (this.entity.isMaster()) {
            return;
        }

        const parent = (this.entity as ChildEntity).getParent();
        const parentsEntitySettings: EntitiesSettings = await getEntitiesSettings(parent.path);

        for (const path in parentsEntitySettings) {
            if (parentsEntitySettings.hasOwnProperty(path)) {
                const gameGroupsInheritance = parentsEntitySettings?.[path]?.gameGroupsInheritance;

                if (typeof gameGroupsInheritance === "boolean") {
                    const [, parentName] = path.split(":").reverse();
                    throw new Errors.OperationForbidden(`Parent ${parentName} already set this setting`);
                }
            }
        }
    }
}

export function getSettingsKey(path: string) {
    if (path.endsWith(":")) {
        path = path.substring(0, path.length - 1);
    }
    return ENTITY_SETTINGS_KEY + (!path || path.startsWith(":") ? "" : ":") + path;
}

export function getErrorsOfValidationSettings(settings: EntitySettingsUpdate): string {
    if (!("validationSettings" in settings) || settings.validationSettings === null) {
        return;
    }

    if (typeof settings.validationSettings !== "object") {
        return ("validationSettings should be an object");
    }

    if (!("playerCodeLength" in settings.validationSettings)) {
        return;
    }

    const playerCodeLength = settings.validationSettings.playerCodeLength;
    if (typeof playerCodeLength !== "object") {
        return ("validationSettings.playerCodeLength should be an object");
    }

    if ("min" in playerCodeLength && !Number.isInteger(+playerCodeLength.min)) {
        return ("validationSettings.playerCodeLength.min should be a number");
    }

    if ("max" in playerCodeLength && !Number.isInteger(+playerCodeLength.max)) {
        return ("validationSettings.playerCodeLength.max should be a number");
    }

    const min = playerCodeLength.min >= PLAYER_CODE_MIN_LENGTH ? playerCodeLength.min : PLAYER_CODE_MIN_LENGTH;
    const max = playerCodeLength.max <= PLAYER_CODE_MAX_LENGTH ? playerCodeLength.max : PLAYER_CODE_MAX_LENGTH;

    if ("min" in playerCodeLength &&
        (playerCodeLength.min < PLAYER_CODE_MIN_LENGTH || playerCodeLength.min >= max)) {

        return (`validationSettings.playerCodeLength.min should be between ${PLAYER_CODE_MIN_LENGTH} and ${max}`);
    }

    if ("max" in playerCodeLength &&
        (playerCodeLength.max > PLAYER_CODE_MAX_LENGTH || playerCodeLength.max <= min)) {

        return (`validationSettings.playerCodeLength.max should be between ${min} and ${PLAYER_CODE_MAX_LENGTH}`);
    }
}

function addDefaultSmsTemplateForBO(settings: EntitySettings) {
    if (settings && settings.twoFactorAuthSettings) {
        const smsTemplates = settings.twoFactorAuthSettings.smsTemplates;
        if (smsTemplates) {
            smsTemplates.defaultEnTemplateForBO = config.twilio.defaultSmsTemplateForBO;
        } else {
            settings.twoFactorAuthSettings.smsTemplates = {
                defaultEnTemplateForBO: config.twilio.defaultSmsTemplateForBO
            };
        }
    }
}

const filterSettings = (value, key) => value !== null && key !== "templates";
