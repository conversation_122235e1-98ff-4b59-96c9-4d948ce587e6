import * as TokenUtils from "../utils/token";
import { token } from "@skywind-group/sw-utils";
import * as Errors from "../errors";
import { BaseEntity } from "../entities/entity";
import EntityCache from "../cache/entity";
import PlayerResponsibleGaming from "../services/playerResponsibleGaming";
import { createPlayerSessionFacade } from "./player/playerSessionFacade";
import { getBrandPlayerService } from "./brandPlayer";
import { Models } from "../models/models";
import { UserStatus } from "../entities/user";

const UserModel = Models.UserModel;

export interface JwtPayload {
    exp?: number | undefined;
    iat?: number | undefined;
}

export type PlayerTokenInfo = TokenUtils.PlayerLoginTokenData & { brand: BaseEntity };
export async function parseToken(playerLoginToken: string) {
    try {
        return await TokenUtils.verifyPlayerLoginToken(playerLoginToken);
    } catch (err) {
        if (err instanceof token.TokenVerifyException) {
            return Promise.reject(new Errors.AccessTokenError());
        }
        if (err instanceof token.TokenExpiredException) {
            return Promise.reject(new Errors.AccessTokenExpired());
        }
        return Promise.reject(err);
    }
}

export async function getEntity(id: number): Promise<BaseEntity> {
    const entity = await EntityCache.findById(id);
    if (!entity) {
        throw new Errors.EntityCouldNotBeFound();
    }
    if (entity.isSuspended()) {
        throw new Errors.ParentSuspendedError();
    }
    return entity;
}

export async function validateToken(playerLoginToken?: string): Promise<PlayerTokenInfo> {
    if (!playerLoginToken) {
        throw new Errors.TokenIsMissing();
    }
    const result: TokenUtils.PlayerLoginTokenData = await parseToken(playerLoginToken);
    const entity = await getEntity(result.brandId);

    if (result.userId) {
        // there is no userId if player logs in site
        const user = await UserModel.findOne({ where: { id: result.userId } });
        if (!user) {
            throw new Errors.UserNotExist();
        }
        if (user.get("status") !== UserStatus.NORMAL) {
            throw new Errors.ParentSuspendedError();
        }
    }

    await (new PlayerResponsibleGaming(entity).validatePlayerSelfExclusionRestriction(result.playerCode));

    // For case Brand-Merchants we can not do simple check entity.type == "merchant"
    if (result.sessionId && !result.isExternalTerminal) {
        await createPlayerSessionFacade().find({
            brandId: result.brandId,
            playerCode: result.playerCode,
            sessionId: result.sessionId
        });
        await getBrandPlayerService().getPlayer(result.brandId, result.playerCode);
    } else if (result.customerSessionId || result.isExternalTerminal) {
        // Noop
    } else {
        throw new Errors.TerminalTokenError();
    }
    return {
        ...result,
        brand: entity
    };
}
