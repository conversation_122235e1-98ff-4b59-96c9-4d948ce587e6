import * as Errors from "../errors";
import { BaseEntity } from "../entities/entity";
import { Models } from "../models/models";
import { isNull, mergeWith, omitBy } from "lodash";
import { Op } from "sequelize";
import { EntityInfo } from "../entities/entityInfo";

export class EntityInfoService {
    private static readonly model = Models.EntityInfoModel;
    public static async getOne(entity: BaseEntity, type: string, throwError: boolean = false): Promise<any> {
        const item = await this.model.findOne({
            where: {
                entityId: entity.id,
                type,
            }
        });
        if (!item && throwError) {
            return Promise.reject(new Errors.EntityInfoRecordNotFound());
        }

        return item && item.getDataValue("data");
    }

    public static async create(entity: BaseEntity, type: string, data: any): Promise<any> {
        try {
            const item = await this.model.create({
                entityId: entity.id,
                type,
                data,
            });
            return item.getDataValue("data");
        } catch (err) {
            return Promise.reject(new Errors.EntityInfoRecordAlreadyExists());
        }
    }

    public static async update(entity: BaseEntity, type: string, data: any): Promise<any> {
        await this.getOne(entity, type, true);
        try {
            await this.model.update(
                { data, updatedAt: new Date() },
                {
                    where: {
                        entityId: entity.id,
                        type,
                    }
                });
            return data;
        } catch (err) {
            return Promise.reject(new Errors.EntityInfoUpdateFail());
        }
    }

    protected static mergeEntityInfo(currentInfo: any, infoToUpdate: any) {
        const mergedInfo = mergeWith(currentInfo, infoToUpdate, (curValue, newValue) => {
            if (Array.isArray(newValue)) {
                return newValue;
            }
        });

        return omitBy(mergedInfo, isNull);
    }

    public static async patch(entity: BaseEntity, type: string, updatedData: any) {
        const entityInfoRecord: any = await this.getOne(entity, type, true);
        if (!entityInfoRecord) {
            return Promise.reject(new Errors.EntityInfoRecordNotFound());
        }
        const newData = this.mergeEntityInfo(entityInfoRecord, updatedData);

        return this.update(entity, type, newData);
    }

    public static async deleteInfoRecord(entity: BaseEntity, type: string): Promise<void> {
        try {
            const destroyed = await this.model.destroy(
                {
                    where: {
                        entityId: entity.id,
                        type,
                    }
                });
            if (!destroyed) {
                return Promise.reject(new Errors.EntityInfoRecordNotFound());
            }
        } catch (err) {
            return Promise.reject(new Errors.EntityInfoUpdateFail());
        }
    }

    public static async getEntitiesInfoForLowBalanceNotification(): Promise<EntityInfo[]> {
        return this.model.findAll({
            raw: true,
            where: {
                type: "notifications",
                data: {
                    "lowBalance": {
                        [Op.ne]: null
                    }
                }
            },
        });
    }
}
