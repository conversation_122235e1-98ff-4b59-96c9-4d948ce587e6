import { CurrencyExchange } from "@skywind-group/sw-currency-exchange";
import { getRedisPool } from "../storage/redis";
import logger from "../utils/logger";
import { lazy } from "@skywind-group/sw-utils";
import { createCurrencyExchange } from "@skywind-group/sw-currency-exchange";

const Redis = getRedisPool();

const log = logger("currency-exchange");

const currencyExchange = lazy(() => init());

export function getCurrencyExchange(): Promise<CurrencyExchange> {
    return currencyExchange.get();
}

async function init(): Promise<CurrencyExchange> {
    log.info("Init currency exchange");
    return createCurrencyExchange(Redis);
}
