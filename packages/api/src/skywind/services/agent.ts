import { ItemInfo } from "../entities/agent";
import { AgentModel as IAgentModel } from "../models/agent";
import { literal, Op, WhereOptions } from "sequelize";
import * as Errors from "../errors";
import { PagingHelper } from "../utils/paginghelper";
import * as FilterService from "./filter";
import { Models } from "../models/models";

export const queryParamsKeys = [
    "affiliateCode",
    "domain",
    "title",
    "status",
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
];
const sortableKeys = ["affiliateCode", "domain", "title", "status"];
const DEFAULT_SORT_KEY = "brandId";
const GROUP_ACTION_MAX_ITEMS = 100;

export interface CreateData {
    brandId?: number;
    domain: string;
    title?: string;
    affiliateCode?: string;
    description?: string;
    status?: string;
}

export type UpdateData = Partial<Omit<CreateData, "brandId">>;

export const updatableKeys = ["affiliateCode", "domain", "title", "status", "domain"];

export interface UpdateStatusesData {
    status: string;
    id: number[];
}

export class AgentService {
    public static model =  Models.AgentModel;

    public static async create(data: CreateData): Promise<ItemInfo> {
        data.status = data.status || "normal";
        try {
            const agent = await AgentService.model.create(data);
            return agent.toInfo();
        } catch (err) {
            return Promise.reject(new Errors.AgentBadBrandId());
        }
    }

    public static async update(brandId: number, agentId: number, data: UpdateData): Promise<ItemInfo> {
        const agent = await AgentService.findById(brandId, agentId);
        Object.assign(agent, data);
        try {
            await AgentService.model.update(data, { where: { brandId: brandId, id: agent.id } });
        } catch (err) {
            return Promise.reject(new Errors.AgentUpdateFail());
        }
        return agent.toInfo();
    }

    public static async updateStatuses(brandId: number, data: UpdateStatusesData): Promise<void> {
        // TODO: think about possible deadlocks
        // https://bitbucket.skywindgroup.com/projects/SWS/repos/sw-management-api/pull-requests/261/overview?commentId=3045
        if (data.id.length > GROUP_ACTION_MAX_ITEMS) {
            return Promise.reject(new Errors.BulkActionLimitError(GROUP_ACTION_MAX_ITEMS, data.id.length));
        }
        data.id.sort();
        try {
            await AgentService.model.update(
                { status: data.status },
                { where: { brandId, id: { [Op.in]: data.id } } }
            );
        } catch (err) {
            return Promise.reject(new Errors.BulkActionDbError());
        }
    }

    public static async listForBrand(brandId: number, query?: WhereOptions): Promise<ItemInfo[]> {
        query = query || {};
        const sortBy = FilterService.getSortKey(query, sortableKeys, DEFAULT_SORT_KEY);
        const sortOrder = FilterService.valueFromQuery(query, "sortOrder") || "ASC";
        query["brandId"] = brandId;
        return PagingHelper.findAndCountAll(AgentService.model, {
            where: query,
            offset: FilterService.valueFromQuery(query, "offset"),
            limit: FilterService.valueFromQuery(query, "limit"),
            order: literal(`"${sortBy}" ${sortOrder}`),
            include: [AgentService.model.associations.entity],
        }, agent => agent.toInfo());
    }

    public static async findById(brandId: number, id: number): Promise<IAgentModel> {
        const item = await AgentService.model.findOne({
            where: { brandId: brandId, id: id },
            include: [AgentService.model.associations.entity],
        });
        return item || Promise.reject(new Errors.AgentDomainNotExist());
    }

    public static async changeStatus(brandId: number, id: number, status: string): Promise<ItemInfo> {
        try {
            const agentRecord = await AgentService.findById(brandId, id);
            agentRecord.status = status;
            await AgentService.model.update({ status: status }, { where: { brandId: brandId, id: id } });
            return agentRecord.toInfo();
        } catch (err) {
            return Promise.reject(new Errors.AgentChangeStatusFail());
        }
    }
}
