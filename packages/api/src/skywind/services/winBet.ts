import { Currencies } from "@skywind-group/sw-currency-exchange";
import { BaseEntity } from "../entities/entity";
import * as Sequelize from "sequelize";
import { PagingHelper } from "../utils/paginghelper";
import * as FilterService from "../services/filter";
import { ValidationError } from "../errors";
import { literal, WhereOptions } from "sequelize";
import { Models } from "../models/models";

const DEFAULT_SORT_KEY = "payment_date_hour";

export const queryParamsKeys = [
    "playerCode",
    "gameCode",
    "currency",
    "paymentDate",
    "playedGames",
    "paymentDateHour",
    "totalBets",
    "totalWins",
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
];

export interface WinBetInfo {
    playerCode: string;
    currency: string;
    playedGames?: number;
    totalBets: number;
    totalWins: number;
    totalJpWins?: number;
    totalFreebetWins?: number;
    GGR: number;
    RTP: number;
    debits: number;
    credits: number;
}

export class PlayerReportWinBetImpl {
    private playerCode;
    private currency;
    private playedGames;
    private totalBets;
    private totalWins;
    private totalJpWins;
    private totalFreebetWins;
    private GGR;
    private RTP;
    private debits: number;
    private credits: number;

    constructor(item) {
        if (!item) {
            return;
        }

        this.playerCode = item.get("player_code");
        this.currency = item.get("currency_code");

        const currency = Currencies.get(this.currency);

        this.playedGames = item.get("played_games");
        this.totalBets = currency.toMajorUnits(parseInt(item.get("total_bets"), 10));
        this.totalWins = currency.toMajorUnits(parseInt(item.get("total_wins"), 10));
        this.totalJpWins = currency.toMajorUnits(parseInt(item.get("total_jp_wins") || 0, 10));
        this.totalFreebetWins = currency.toMajorUnits(parseInt(item.get("total_freebet_wins") || 0, 10));
        this.GGR = currency.toMajorUnits(parseInt(item.get("GGR"), 10));
        this.RTP = item.get("RTP");
        this.debits = currency.toMajorUnits(parseInt(item.get("debits") || 0, 10));
        this.credits = currency.toMajorUnits(parseInt(item.get("credits") || 0, 10));
    }

    public toInfo(): WinBetInfo {
        return {
            playerCode: this.playerCode,
            currency: this.currency,
            playedGames: this.playedGames,
            totalBets: this.totalBets,
            totalWins: this.totalWins,
            totalJpWins: this.totalJpWins,
            totalFreebetWins: this.totalFreebetWins,
            GGR: this.GGR,
            RTP: this.RTP,
            credits: this.credits,
            debits: this.debits
        };
    }
}

// TODO: I think this can be removed
// TODO: need to fix order if it is needed
export async function getPlayerReports(brand: BaseEntity, filter: WhereOptions<any>): Promise<WinBetInfo[]> {
    try {
        filter["brand_id"] = brand.id;
        const sortOrder = FilterService.valueFromQuery(filter, "sortOrder") || "DESC";
        return await PagingHelper.findAndCountAll(Models.AggrWinBetModel, {
            attributes: [
                [Sequelize.fn("SUM", Sequelize.col("played_games_qty")), "played_games"],
                "player_code",
                "currency_code",
                [Sequelize.fn("SUM", Sequelize.col("total_bets")), "total_bets"],
                [Sequelize.fn("SUM", Sequelize.col("total_wins")), "total_wins"],
                [Sequelize.fn("SUM", Sequelize.col("total_jp_wins")), "total_jp_wins"],
                [Sequelize.fn("SUM", Sequelize.col("total_freebet_wins")), "total_freebet_wins"],
                [Sequelize.literal("SUM(total_bets) - SUM(total_wins)") as any, "GGR"],
                [Sequelize.literal("SUM(total_wins)/GREATEST(SUM(total_bets),1)") as any, "RTP"],
                [Sequelize.fn("MAX", Sequelize.col("payment_date_hour")), "payment_date_hour"],
                [Sequelize.fn("SUM", Sequelize.col("credit")), "credits"],
                [Sequelize.fn("SUM", Sequelize.col("debit")), "debits"]
            ],
            group: ["player_code", "currency_code"],
            where: filter,
            offset: FilterService.valueFromQuery(filter, "offset") || FilterService.DEFAULT_OFFSET,
            limit: FilterService.valueFromQuery(filter, "limit") || FilterService.DEFAULT_LIMIT,
            order: literal(`${DEFAULT_SORT_KEY} ${sortOrder}, player_code`),
        }, item => (new PlayerReportWinBetImpl(item)).toInfo());
    } catch (error) {
        if (error instanceof Sequelize.DatabaseError) {
            const dbError = error["original"] as Sequelize.DatabaseError;
            if (dbError && dbError["routine"] === "DateTimeParseError") {
                return Promise.reject(new ValidationError(dbError["message"]));
            }
        }
        return Promise.reject(error);
    }
}
