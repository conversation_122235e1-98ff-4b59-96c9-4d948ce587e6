import { LobbyExtendedMenuItem, LobbyExtendedMenuSubItem, LobbyMenuItem } from "../../entities/lobby";
import { FavoriteGame, LobbyGameInfo } from "../../entities/game";

export function getMenuItemGames(menuItems: LobbyMenuItem[]) {
    return Object.values(menuItems
        .reduce<LobbyGameInfo[]>((result, { subcategories, ...item }) => [
            ...result,
            ...getGames(item),
            ...(subcategories ? subcategories.reduce<LobbyGameInfo[]>((subResult, subItem) => [
                ...subResult,
                ...getGames(subItem)
            ], []) : [])
        ], [])
        .reduce<Record<string, LobbyGameInfo>>((result, gameInfo) => ({
            ...result,
            [gameInfo.code]: gameInfo
        }), {}));
}

function getGames({ games, widget, options }: LobbyExtendedMenuSubItem) {
    return [
        ...(games || []),
        ...(widget ? widget.games || [] : []),
        ...(options ? Object.values(options.widgets || {}).reduce<LobbyGameInfo[]>((result, { games: subGames }) => [
            ...result,
            ...(subGames || [])
        ], []) : []),
    ];
}

export function setPlayerGameActivities(menuItems: LobbyExtendedMenuItem[], records: FavoriteGame[]) {
    const games = getMenuItemGames(menuItems);

    const favoriteGameCodes = new Set(records
        .filter(({ isFavorite }) => isFavorite)
        .map(({ gameCode }) => gameCode));
    const favoriteGames = games.filter(({ code }) => favoriteGameCodes.has(code));
    if (favoriteGames.length) {
        for (const menuItem of menuItems) {
            if (menuItem.showFavoriteGames) {
                menuItem.games = favoriteGames;
            } else if (menuItem.subcategories?.length) {
                for (const subMenuItem of menuItem.subcategories) {
                    if (subMenuItem.showFavoriteGames) {
                        subMenuItem.games = favoriteGames;
                    }
                }
            }
        }
    }

    const recentlyPlayed = records
        .filter(({ isRecently }) => isRecently)
        .reduce<Record<string, Date>>((acc, { gameCode, updatedAt }) => ({
            ...acc,
            [gameCode]: updatedAt
        }), {});
    const recentlyPlayedGameCodes = new Set(Object.keys(recentlyPlayed));
    const recentlyPlayedGames = games.filter(({ code }) => recentlyPlayedGameCodes.has(code));
    if (recentlyPlayedGames.length) {
        for (const menuItem of menuItems) {
            if (menuItem.showRecentGames) {
                menuItem.games = recentlyPlayedGames;
            } else if (menuItem.subcategories?.length) {
                for (const subMenuItem of menuItem.subcategories) {
                    if (subMenuItem.showRecentGames) {
                        subMenuItem.games = recentlyPlayedGames;
                    }
                }
            }
        }
    }
    updateMenuItems(menuItems, (game) => {
        const isFavorite = favoriteGameCodes.has(game.code);
        const playedDate = recentlyPlayed[game.code];
        delete game.isFavorite;
        return ({
            ...game,
            ...(isFavorite ? { isFavorite } : {}),
            ...(playedDate ? { playedDate } : {}),
        });
    });
}

export function updateMenuItems(menuItems: LobbyExtendedMenuItem[], fn: (game: LobbyGameInfo) => LobbyGameInfo) {
    const updateMenuItem = (menuItem: LobbyExtendedMenuSubItem) => {
        if (menuItem.games?.length) {
            menuItem.games = menuItem.games.map(fn);
        }
        if (menuItem.widget?.games?.length) {
            menuItem.widget.games = menuItem.widget.games.map(fn);
        }
        const widgetNames = Object.keys(menuItem.options?.widgets ?? {});
        if (widgetNames.length) {
            for (const widgetName of widgetNames) {
                const widget = menuItem.options.widgets[widgetName];
                if (widget.games?.length) {
                    widget.games = widget.games.map(fn);
                }
            }
        }
    };

    for (const menuItem of menuItems) {
        updateMenuItem(menuItem);
        if (menuItem.subcategories?.length) {
            for (const subMenuItem of menuItem.subcategories) {
                updateMenuItem(subMenuItem);
            }
        }
    }
}
