import { LobbyMenuItem } from "../../entities/lobby";

export function toMenuItems(menuItems: LobbyMenuItem[], categoryIds: Set<string>): LobbyMenuItem[] {
    if (!Array.isArray(menuItems) || menuItems.length === 0) {
        return [];
    }
    return menuItems
        .map(({ gameCategoryId, subcategories, ...menuItem }) => {
            if (gameCategoryId && !categoryIds.has(gameCategoryId)) {
                return null;
            }
            const newSubcategories = (subcategories || [])
                .filter(({ gameCategoryId: id }) => !id || categoryIds.has(id));
            return ({
                ...(gameCategoryId ? { gameCategoryId } : {}),
                ...(newSubcategories.length ? { subcategories: newSubcategories } : {}),
                ...menuItem
            });
        })
        .filter(Boolean);
}
