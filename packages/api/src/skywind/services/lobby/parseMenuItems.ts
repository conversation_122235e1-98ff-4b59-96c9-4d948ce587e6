import {
    LobbyExtendedMenuItem,
    LobbyExtendedMenuSubItem,
    LobbyExternalWidget,
    LobbyInternalExternalWidget,
    LobbyMenuItem,
    LobbyMenuSubItem
} from "../../entities/lobby";
import {
    EntityGame,
    GameLimitRange,
    LobbyGameInfo,
    LobbyGamesLimitParams,
    LobbyGamesParams
} from "../../entities/game";
import { GameCategory, GameCategoryItemFilters } from "../../entities/gamecategory";
import { encodeId } from "../../utils/publicid";
import { GameCategoryGamesService } from "../gameCategory/gameCategoryGamesService";
import { ENTITY_GAME_STATUS, GAME_IMAGES, GAME_TYPES } from "../../utils/common";
import { GameCategoryItemsService } from "../gameCategory/gameCategoryItemsService";
import { findGameLimitsByCurrencyCode } from "../limits";
import { BaseEntity } from "../../entities/entity";
import { LimitsByCurrencyCode, MultiRoomGameLimits, RoomGameLimits, TableGameLimits } from "../../entities/gamegroup";
import { getEntitySettings } from "../settings";

export async function parseMenuItems(entity: BaseEntity,
                                     categories: GameCategory[],
                                     menuItems?: LobbyMenuItem[],
                                     params: LobbyGamesParams = {}): Promise<LobbyExtendedMenuItem[]> {
    const encodedCategories = categories.reduce<Record<string, GameCategory>>((result, item) => ({
        ...result,
        [encodeId(item.id)]: item
    }), {});

    const newMenuItems = getMenuItems(menuItems, categories);
    const entityGames = await getEntityGames(entity.id, newMenuItems, encodedCategories, params.gameStatuses);
    const gamesLimits = await getGamesLimits(entity, Object.values(entityGames), params);

    return newMenuItems.map<LobbyExtendedMenuItem>(({ subcategories, ...item }) => ({
        ...setGames(item, encodedCategories, entityGames, gamesLimits, params),
        ...(subcategories ? {
            subcategories: subcategories.map<LobbyExtendedMenuSubItem>(subItem =>
                setGames(subItem, encodedCategories, entityGames, gamesLimits, params))
        } : {})
    }));
}

function getMenuItems(menuItems: LobbyMenuItem[] | undefined, categories: GameCategory[]): LobbyMenuItem[] {
    if (Array.isArray(menuItems) && menuItems.length) {
        return menuItems;
    }
    return categories.map<LobbyMenuItem>(({ icon, id, title, translations }) => ({
        title,
        gameCategoryId: encodeId(id),
        ...(icon ? { icon } : {}),
        ...(Object.keys(translations || {}).length ? { translations } : {})
    }));
}

async function getEntityGames(entityId: number,
                              menuItems: LobbyMenuItem[],
                              categories: Record<string, GameCategory>,
                              gameStatuses: ENTITY_GAME_STATUS[] = [ENTITY_GAME_STATUS.NORMAL]) {
    const { gameIds, labelIds, providerIds } = getGameCategoryItemFilters(menuItems, categories);
    const gameCodes = [...gameIds, ...getWidgetGameCodes(menuItems)];
    const entityGames = await GameCategoryGamesService.findAllEntityGames(entityId, gameCodes, providerIds, labelIds);
    return entityGames
        .filter(({ status }) => gameStatuses.includes(status as ENTITY_GAME_STATUS))
        .reduce<Record<string, EntityGame>>((result, entityGame) => ({
            ...result,
            [entityGame.game.code]: entityGame
        }), {});
}

function getGameCategoryItemFilters(menuItems: LobbyMenuItem[],
                                    categories: Record<string, GameCategory>): GameCategoryItemFilters {
    return menuItems
        .reduce<string[]>((result, { gameCategoryId, subcategories }) => [
            ...result,
            ...(gameCategoryId ? [gameCategoryId] : []),
            ...((subcategories || []).map(({ gameCategoryId: id }) => id).filter(Boolean))
        ], [])
        .map<GameCategory>(categoryId => categories[categoryId])
        .filter(Boolean)
        .map<GameCategoryItemFilters>(({ items }) => GameCategoryItemsService.parseGameCategoryItem(items))
        .reduce<GameCategoryItemFilters>((result, data) => {
            return {
                gameIds: result.gameIds.concat(data.gameIds),
                labelIds: result.labelIds.concat(data.labelIds),
                providerIds: result.providerIds.concat(data.providerIds)
            };
        }, { gameIds: [], labelIds: [], providerIds: [] });
}

function getWidgetGameCodes(menuItems: LobbyMenuItem[]) {
    return menuItems
        .reduce<string[]>((result, { widget, options, subcategories }) => [
            ...result,
            ...(widget ? getExternalWidgetGameCodes(widget) : []),
            ...(options && options.widgets ? getExternalWidgetsGameCodes(options.widgets) : []),
            ...(subcategories || []).reduce<string[]>((subResult, { widget: subWidget, options: subOptions }) => [
                ...subResult,
                ...(subWidget ? getExternalWidgetGameCodes(subWidget) : []),
                ...(subOptions && subOptions.widgets ? getExternalWidgetsGameCodes(subOptions.widgets) : []),
            ], [])
        ], []);
}

function getExternalWidgetGameCodes(widget: LobbyExternalWidget): string[] {
    const codes: string[] = [];
    JSON.stringify(widget.options || {}, (key, value) => {
        if (key === "gameCode" && value) {
            codes.push(value);
        }
        return value;
    });
    return codes;
}

function getExternalWidgetsGameCodes(widgets: Record<string, LobbyExternalWidget>): string[] {
    return Object.values(widgets).reduce<string[]>((result, widget) => [
        ...result,
        ...getExternalWidgetGameCodes(widget)
    ], []);
}

function setGames({ gameCategoryId, widget, options, ...data }: LobbyMenuSubItem,
                  categories: Record<string, GameCategory>,
                  entityGames: Record<string, EntityGame>,
                  limits: Record<string, LimitsByCurrencyCode>,
                  params: LobbyGamesLimitParams): LobbyExtendedMenuSubItem {
    return ({
        ...data,
        ...(gameCategoryId ? { gameCategoryId } : {}),
        ...setCategoryGames(gameCategoryId, categories, Object.values(entityGames), limits, params),
        ...(widget ? {
            widget: {
                ...widget,
                ...setWidgetGames(widget, entityGames, limits, params)
            }
        } : {}),
        ...(options ? {
            options: {
                ...options,
                ...setWidgetsGames(options.widgets, entityGames, limits, params)
            }
        } : {}),
    });
}

function setCategoryGames(gameCategoryId: string | undefined,
                          categories: Record<string, GameCategory>,
                          entityGames: EntityGame[],
                          limits: Record<string, LimitsByCurrencyCode>,
                          params: LobbyGamesLimitParams): { games?: LobbyGameInfo[] } {
    if (gameCategoryId) {
        const gameCategory = categories[gameCategoryId];
        if (gameCategory) {
            const games = GameCategoryItemsService.filterGamesByItems(entityGames, gameCategory.items);
            if (games.length) {
                return {
                    games: games.map(game => toLobbyGameInfo(game, limits, params))
                };
            }
        }
    }
    return {};
}

function setWidgetsGames(widgets: Record<string, LobbyExternalWidget> | undefined,
                         entityGames: Record<string, EntityGame>,
                         limits: Record<string, LimitsByCurrencyCode>,
                         params: LobbyGamesLimitParams): {
    widgets?: Record<string, LobbyInternalExternalWidget<LobbyGameInfo>>
} {
    if (!widgets) {
        return {};
    }
    return {
        widgets: Object.entries(widgets)
            .reduce<Record<string, LobbyInternalExternalWidget<LobbyGameInfo>>>((result, [key, widget]) => ({
                ...result,
                [key]: {
                    ...widget,
                    ...setWidgetGames(widget, entityGames, limits, params)
                }
            }), {})
    };
}

function setWidgetGames(widget: LobbyExternalWidget | undefined,
                        entityGames: Record<string, EntityGame>,
                        limits: Record<string, LimitsByCurrencyCode>,
                        params: LobbyGamesLimitParams): { games?: LobbyGameInfo[] } {
    if (widget === undefined) {
        return;
    }
    const codes = getExternalWidgetGameCodes(widget);
    const games = codes.map<EntityGame>(code => entityGames[code]).filter(Boolean);
    if (games.length === codes.length) {
        return {
            games: games.map(game => toLobbyGameInfo(game, limits, params))
        };
    }
    return {};
}

export function toLobbyGameInfo(entityGame: EntityGame,
                                limits: Record<string, LimitsByCurrencyCode>,
                                params: LobbyGamesLimitParams = {}): LobbyGameInfo {
    const info = entityGame.toInfo(true);
    const live: LobbyGameInfo["features"]["live"] = info.features?.live;
    if (live && params?.includeGamesLimitRanges && params?.currency) {
        const tableGameLimits = limits[info.code][params.currency] as TableGameLimits;
        if (tableGameLimits) {
            live.limits = toGameLimitRanges(tableGameLimits);
        }
    }
    return {
        code: info.code,
        type: info.type,
        gameModule: info["gameModule"],
        title: info.title,
        providerTitle: info.providerTitle,
        defaultInfo: {
            ...(GAME_IMAGES[info.code] || {}),
            ...(info.defaultInfo || {}),
        },
        features: {
            isComission: info.features?.["isComission"],
            ...(live ? { live } : {}),
            translations: info.features?.translations
        },
        ...(params.includeGamesLimits ? { limits: limits[info.code] } : {})
    };
}

async function getGamesLimits(entity: BaseEntity, entityGames: EntityGame[], params: LobbyGamesLimitParams) {
    const result: Record<string, LimitsByCurrencyCode> = {};
    const includeGamesLimits = params.includeGamesLimits === true;
    const includeGamesLimitRanges = params.includeGamesLimitRanges === true;
    if (includeGamesLimits || (includeGamesLimitRanges && params.currency)) {
        const TYPES = [GAME_TYPES.table, GAME_TYPES.live];
        const tableGames = entityGames.filter(({ game: { type } }) => TYPES.includes(type));
        if (tableGames.length) {
            const settings = await getEntitySettings(entity.path);
            for (const entityGame of tableGames) {
                const currencies = params.currency ? [params.currency] : entity.getCurrencies();
                result[entityGame.game.code] = await findGameLimitsByCurrencyCode(
                    entity,
                    entityGame,
                    params.gameGroupName,
                    params.gameGroupId,
                    currencies,
                    settings
                );
            }
        }
    }
    return result;
}

function toGameLimitRanges(value: TableGameLimits): GameLimitRange[] {
    if (value.stakeMax) {
        return [toGameLimitRange(value as RoomGameLimits)];
    }
    return Object.entries(value as MultiRoomGameLimits)
        .map<GameLimitRange>(([name, limits]) => toGameLimitRange(limits, name));
}

function toGameLimitRange({ stakeMax, stakeMin, order, isDefaultRoom }: RoomGameLimits, name?: string): GameLimitRange {
    return { name, stakeMin, stakeMax, order, isDefaultRoom };
}
