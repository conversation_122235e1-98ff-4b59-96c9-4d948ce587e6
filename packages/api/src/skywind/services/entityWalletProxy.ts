import { EntityWallet } from "@skywind-group/sw-management-wallet";
import { ITransaction } from "@skywind-group/sw-wallet";
import { BaseEntity } from "../entities/entity";
import * as Errors from "../errors";

export class EntityWalletProxy implements EntityWallet {
    constructor(private readonly entity: BaseEntity,
                private readonly origin: EntityWallet) {
    }

    public async credit(transaction: ITransaction, currency: string, amount: number): Promise<void> {
        this.validate(currency);
        return this.origin.credit(transaction, currency, amount);
    }

    public async debit(transaction: ITransaction, currency: string, amount: number): Promise<void> {
        this.validate(currency);
        return this.origin.debit(transaction, currency, amount);
    }

    public async debitFreeBetAmount(transaction: ITransaction, currency: string, amount: number): Promise<void> {
        this.validate(currency);
        return this.origin.debitFreeBetAmount(transaction, currency, amount);
    }

    public async getBalance(currency: string): Promise<number> {
        return this.origin.getBalance(currency);
    }

    private validate(currencyCode) {
        if (!this.entity.currencyExists(currencyCode)) {
            throw new Errors.CurrencyNotExistError(currencyCode);
        }
        if (this.entity.isSuspended()) {
            throw new Errors.ParentSuspendedError();
        }
    }
}
