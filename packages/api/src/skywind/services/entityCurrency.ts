import { BaseEntity, ChildEntity } from "../entities/entity";
import { getChildEntities } from "./entity";
import { sequelize as db } from "../storage/db";
import { Transaction } from "sequelize";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { getEntitySettings } from "./settings";
import { uniq } from "lodash";
import * as Errors from "../errors";

interface CurrencyInfo {
    displayName: string;
    code: string;
    isSocial?: true;
}

export default class EntityCurrencyService {
    constructor(public entity: BaseEntity) {
    }

    public getList(): Array<CurrencyInfo> {
        return this.entity.getCurrencies().map(code => {
            const currency = Currencies.value(code);
            return ({
                code,
                displayName: currency?.name || "",
                ...(currency.isSocial ? { isSocial: true } : {}),
            });
        });
    }

    public async add(values: string | string[]): Promise<BaseEntity> {
        const codes = uniq(Array.isArray(values) ? values : [ values ]);

        const childEntity = this.entity as ChildEntity;
        const { social } = await getEntitySettings(childEntity.path);
        for (const code of codes) {
            if (!Currencies.value(code)?.isSocial && social) {
                throw new Errors.CurrencyNotSocialError(code);
            }
            if (Currencies.value(code)?.isSocial && !social) {
                throw new Errors.SocialCurrencyNotAllowedError(code);
            }
            childEntity.addCurrency(code);
        }
        return childEntity.save();
    }

    public async remove(codeOrCodes: string | string[], force?: boolean): Promise<BaseEntity> {
        return db.transaction(async (transaction: Transaction): Promise<any> => {
            const currencies = Array.isArray(codeOrCodes) ? codeOrCodes : [codeOrCodes];

            if (force) {
                const children = getChildEntities(this.entity)
                    .filter(entity => !!currencies.find(currency => entity.currencyExists(currency)));

                const reversedChildren = children.reverse();

                for (const child of reversedChildren) {
                    for (const currency of currencies) {
                        await child.removeCurrency(currency);
                    }
                }

                for (const child of reversedChildren) {
                    await child.save(transaction);
                }
            }

            for (const currency of currencies) {
                await this.entity.removeCurrency(currency);
            }

            return this.entity.save(transaction);
        });
    }
}
