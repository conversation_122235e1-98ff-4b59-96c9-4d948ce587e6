import { FindOptions, Op, WhereOptions } from "sequelize";
import * as Errors from "../errors";
import { cloneDeep, isString } from "lodash";
import config from "../config";
import { MAX_INT_VALUE } from "../utils/common";

export const DEFAULT_OFFSET = 0;
export const DEFAULT_LIMIT = 20;
export const MAX_LIMIT = config.limits.apiFilterMaxLimit;

const caseInsensitiveKeys = ["firstName", "lastName"];

const POSTFIXES_OPERATORS = {
    "contains": Op.iLike,
    "gte": Op.gte,
    "lte": Op.lte,
    "lt": Op.lt,
    "gt": Op.gt,
    "in": Op.in,
    "in!": Op.notIn,
    "ne": Op.ne,
    "contains!": Op.notILike,
    "are": Op.and,
    "or": Op.or,
    "not": Op.not
};

const SEQUELIZE_OPS: symbol[] = [...Object.values(POSTFIXES_OPERATORS), Op.eq];

export interface Mapping {
    [fieldname: string]: string;
}

export interface StoredFunctionInput {
    whereFilters: string;
    sortOrder: string;
    limit: number;
    offset: number;
}

type ReqQuery = Record<string, any>;

function parseKey(reqQuery: ReqQuery, key: string): WhereOptions<any> {
    const where: WhereOptions<any> = {};
    let value: any = trimIfString(reqQuery[key]);

    if (typeof value !== "undefined") {
        where[key] = { [Op.eq]: value };
        if (caseInsensitiveKeys.indexOf(key) !== -1) {
            where[key] = { [Op.iLike]: value };
        }
    }

    for (const op in POSTFIXES_OPERATORS) {
        if (!POSTFIXES_OPERATORS.hasOwnProperty(op)) {
            continue;
        }
        const operator = POSTFIXES_OPERATORS[op];

        const fullKeyName: string = key + "__" + op;
        value = trimIfString(reqQuery[fullKeyName]);

        if (typeof value === "undefined" || value === null) {
            continue;
        }
        if (op === "or") {
            where[operator] = where[operator]
                ? { ...where[operator] as object, ...value }
                : value;
            return where;
        }
        where[key] = where[key] || {};
        switch (op) {
            case "in":
            case "in!":
            case "are":
                if (value) {
                    where[key][operator] = value.split(",").map(v => v.trim());
                } else {
                    where[key] = { [Op.eq]: null };
                }
                break;
            case "contains":
            case "contains!":
                where[key][operator] = "%" + value.replace(/_/g, "\\_").replace(/%/g, "\\%") + "%";
                break;
            case "gte":
            case "lte":
            case "lt":
            case "gt":
            case "ne":
                where[key][operator] = value;
                break;
            case "not":
                where[key] = { [Op.not]: value === "true" };
                break;
            default:
                break;
        }
    }
    return where;
}

function trimIfString(value: any): any {
    return isString(value) ? value.trim() : value;
}

function getFullTextSearchFilter({ fields, q }: ReqQuery, keys: string[]): WhereOptions<any> {
    if (!(fields && typeof fields === "string" && q && typeof q === "string")) {
        return {};
    }
    const ftsQuery = fields
        .split(",")
        .map(field => field.trim())
        .filter(field => keys.indexOf(field) !== -1)
        .map(field => ({
            [field]: {
                [Op.iLike]: `%${q.trim()}%`
            }
        }));
    return ftsQuery.length ? { [Op.or]: ftsQuery } : {} as any;
}

export function parseFilter(reqQuery: ReqQuery,
                            keys: string[],
                            enableFullTextSearch: boolean = false): WhereOptions<any> {
    const where: WhereOptions<any> = {};
    for (const key of keys) {
        Object.assign(where, parseKey(reqQuery, key));
    }
    if (!enableFullTextSearch) {
        return where;
    }
    return Object.assign(where, getFullTextSearchFilter(reqQuery, keys));
}

function omitKeys(reqQuery: ReqQuery, fields: Mapping): ReqQuery {
    const operators = Object.keys(POSTFIXES_OPERATORS);
    const mapping = new Map<string, string>(Object.entries(fields).reduce((r, [source, target]) => [
        ...r,
        ...[[`${source}`, `${target}`]], // without postfix
        ...operators.map(op => [`${source}__${op}`, `${target}__${op}`]),
    ], []));
    return Object.entries(cloneDeep(reqQuery)).reduce<ReqQuery>((r, [key, value]) => ({
        ...r,
        ...(mapping.has(key) ? { [mapping.get(key)]: value } : {})
    }), {});
}

export function mapFilter(reqQuery: ReqQuery, fields: Mapping): WhereOptions<any> {
    return parseFilter(omitKeys(reqQuery, fields), Object.values(fields));
}

const conditions = {
    positiveInteger: {
        optional: true,
        isInt: { options: { min: 1, max: MAX_INT_VALUE } },
        errorMessage: "should be a positive number"
    },
    nonNegativeInteger: {
        optional: true,
        isInt: { options: { min: 0, max: MAX_INT_VALUE } },
        errorMessage: "should be a non-negative number"
    },
    number: { optional: true, isFloat: true },
    timestampISO8601: { optional: true, isTimestampIso8601: true },
    amount: { optional: true, isDecimal: true }
};

const schemas = {
    format: {
        format: { optional: true, isCSVFormat: true }
    },

    offset: {
        offset: conditions.nonNegativeInteger,
    },

    limit: {
        limit: conditions.positiveInteger,
    },

    status: {
        status: { optional: true, isStatus: true },
    },

    sortOrder: {
        sortOrder: { optional: true, isSortOrder: true },
    },

    roundId: {
        roundId: conditions.nonNegativeInteger,
        roundId__in: { optional: true, isCommaSeparatedIntegers: true },
    },

    totalBets: {
        totalBets: conditions.number,
        totalBet__gt: conditions.number,
        totalBet__lt: conditions.number,
        totalBet__gte: conditions.number,
        totalBet__lte: conditions.number,
    },

    totalWins: {
        totalWins: conditions.number,
        totalWins__gt: conditions.number,
        totalWins__lt: conditions.number,
        totalWins__gte: conditions.number,
        totalWins__lte: conditions.number,
    },

    bet: {
        bet: conditions.number,
        bet__gt: conditions.number,
        bet__lt: conditions.number,
        bet__gte: conditions.number,
        bet__lte: conditions.number,
    },

    win: {
        win: conditions.number,
        win__gt: conditions.number,
        win__lt: conditions.number,
        win__gte: conditions.number,
        win__lte: conditions.number,
    },

    revenue: {
        revenue: conditions.number,
        revenue__gt: conditions.number,
        revenue__lt: conditions.number,
        revenue__gte: conditions.number,
        revenue__lte: conditions.number,
    },

    balance: {
        balance: conditions.number,
        balance__gt: conditions.number,
        balance__lt: conditions.number,
        balance__gte: conditions.number,
        balance__lte: conditions.number,
        balanceBefore: conditions.number,
        balanceBefore__gt: conditions.number,
        balanceBefore__lt: conditions.number,
        balanceBefore__gte: conditions.number,
        balanceBefore__lte: conditions.number,
        balanceAfter: conditions.number,
        balanceAfter__gt: conditions.number,
        balanceAfter__lt: conditions.number,
        balanceAfter__gte: conditions.number,
        balanceAfter__lte: conditions.number,
    },

    playedGames: {
        playedGames: conditions.nonNegativeInteger,
        playedGames__gt: conditions.nonNegativeInteger,
        playedGames__lt: conditions.nonNegativeInteger,
        playedGames__gte: conditions.nonNegativeInteger,
        playedGames__lte: conditions.nonNegativeInteger,
    },

    from: {
        from: conditions.nonNegativeInteger,
    },

    to: {
        to: conditions.nonNegativeInteger,
    },

    currency: {
        currency: { optional: true, isCurrency: true },
        currency__in: { optional: true, isCommaSeparatedCurrencies: true },
    },

    currencyCode: {
        currencyCode: { optional: true, isCurrency: true },
        currencyCode__in: { optional: true, isCommaSeparatedCurrencies: true },
    },

    orderStatus: {
        orderStatus: { optional: true, isStatus: true },
    },

    amount: {
        amount: conditions.amount,
        amount__gt: conditions.amount,
        amount__lt: conditions.amount,
        amount__gte: conditions.amount,
        amount__lte: conditions.amount
    },

    recoveryType: {
        recoveryType: { optional: true, isRecoveryType: true },
        recoveryType__in: { optional: true, isCommaSeparatedRecoveryType: true }
    },

    playerCode: {
        playerCode: { optional: true, isMerchantPlayerCode: true },
        playerCode__in: { optional: true, isMerchantPlayerCode: true }
    },

    type: {
        type: { optional: true, nonSqlString: true },
        type__in: { optional: true, nonSqlStringCommaSeparated: true }
    },

    gameCode: {
        gameCode: { optional: true, nonSqlString: true },
        gameCode__in: { optional: true, nonSqlStringCommaSeparated: true }
    },

    device: {
        device: { optional: true, nonSqlString: true },
        device__in: { optional: true, nonSqlStringCommaSeparated: true }
    },

    isTest: {
        isTest: { optional: true, isBoolean: true }
    }

};

(function addValidatorsTimestampISO8601(keys: string[]) {
    for (const key of keys) {
        schemas[key] = {};
        schemas[key][key] = schemas[key][key + "__lt"] = schemas[key][key + "__gt"] =
            schemas[key][key + "__lte"] = schemas[key][key + "__gte"] = conditions.timestampISO8601;
    }
})([
    "ts", "firstTs", "paymentDate", "paymentDateHour", "dateHour", "createdAt", "updatedAt", "lastLogin", "expired",
    "insertedAt"
]);

export function prepareScheme(keys: string[]) {
    const schema = {};
    let i: number;
    for (i = 0; i < keys.length; i++) {
        Object.assign(schema, schemas[keys[i]]);
    }
    return schema;
}

export function valueFromQuery<T = any>(query, name, deleteValue: boolean = true): T {
    if (typeof query[name] === "undefined") {
        return undefined;
    }
    let value = query[name];
    if (typeof value === "object") {
        if (Op.eq in value) {
            value = value[Op.eq];
        } else if (Op.in in value) {
            value = value[Op.in];
        } else if (Op.notIn in value) {
            value = value[Op.notIn];
        }
    }
    if (deleteValue) {
        delete query[name];
    }
    return value;
}

export function getSortKey(query, sortableKeys, defaultKey, sortKey = "sortBy") {
    const key = valueFromQuery(query, sortKey);
    if (!key) {
        return defaultKey;
    }
    if (sortableKeys.indexOf(key) === -1) {
        throw new Errors.SortByInvalidError(key);
    }
    return key;
}

export function nativeFilters(filter: WhereOptions<any>,
                              tableAlias: string,
                              filters: string[],
                              mapFields?: { [fieldname: string]: string }): string {
    let result = "";

    const AND = (predicate: string): string => result ? result + ` AND ${predicate}` : predicate;

    const fieldFilter = (field: string) => {
        if (!filter[field]) {
            return;
        }
        const name = () => `${tableAlias}."${mapFields && mapFields[field] || field}"`;
        const symbol = (type: symbol) => filter[field]?.[type];

        if (symbol(Op.gt) !== undefined) {
            result = AND(`${name()} > '${symbol(Op.gt)}'`);
        }
        if (symbol(Op.gte) !== undefined) {
            result = AND(`${name()} >= '${symbol(Op.gte)}'`);
        }
        if (symbol(Op.lt) !== undefined) {
            result = AND(`${name()} < '${symbol(Op.lt)}'`);
        }
        if (symbol(Op.lte) !== undefined) {
            result = AND(`${name()} <= '${symbol(Op.lte)}'`);
        }
        if (symbol(Op.eq) !== undefined) {
            result = AND(`${name()} = '${symbol(Op.eq)}'`);
        }
        if (symbol(Op.in) !== undefined) {
            result = AND(`${name()} IN ('${(symbol(Op.in) as string[]).join("', '")}')`);
        }
        if (symbol(Op.notIn) !== undefined) {
            result = AND(`${name()} NOT IN ('${(symbol(Op.notIn) as string[]).join("', '")}')`);
        }
        if (filter[field] !== undefined && typeof filter[field] !== "object") {
            result = AND(`${name()} = '${filter[field]}'`);
        }
    };

    filters.forEach((field) => fieldFilter(field));

    return result;
}

export function sequelizeFindToStoredFunctionInput(findOptions: FindOptions<any>,
                                                   mapping: Mapping): StoredFunctionInput {
    const whereOptions: WhereOptions<any> = findOptions.where as WhereOptions<any>;
    const whereFilters: string[] = [];
    const escapeSpecialChars = v => {
        if (typeof v === "string") {
            return v.replace(/'/g, "''").replace(/"/g, "\\\"");
        }
        return v;
    };
    for (const field in mapping) {
        if (!mapping.hasOwnProperty(field)) {
            continue;
        }
        let fieldFilters: any = whereOptions[field];
        if (fieldFilters === undefined) {
            continue;
        }
        if (fieldFilters === null) {
            whereFilters.push(`${mapping[field]} IS NULL`);
            continue;
        }
        if (fieldFilters.logic) {
            fieldFilters = fieldFilters.logic;
        }
        if (typeof fieldFilters !== "object") {
            whereFilters.push(`${mapping[field]} = '${escapeSpecialChars(fieldFilters)}'`);
            continue;
        }
        if (isJsonField(fieldFilters)) {
            const [firstKey] = Object.keys(fieldFilters);
            const filterValue = escapeSpecialChars(fieldFilters[firstKey][Op.iLike]);
            whereFilters.push(`${field}#>>'{${firstKey.toString()}}' ILIKE '${filterValue}'`);
        } else {
            for (const op of SEQUELIZE_OPS) {
                const filterValue: any = escapeSpecialChars(fieldFilters[op]);
                if (filterValue === undefined) {
                    continue;
                }
                switch (op) {
                    case Op.eq:
                        if (filterValue === null) {
                            whereFilters.push(`${mapping[field]} IS NULL`);
                        } else {
                            whereFilters.push(`${mapping[field]} = '${filterValue}'`);
                        }
                        break;
                    case Op.in:
                        whereFilters.push(`${mapping[field]} IN ('${filterValue.map(v => escapeSpecialChars(
                            trimIfString(v))).join("', '")}')`
                        );
                        break;
                    case Op.notIn:
                        whereFilters.push(`${mapping[field]} NOT IN ('${filterValue.map(v => escapeSpecialChars(
                            trimIfString(v))).join("', '")}')`
                        );
                        break;
                    case Op.iLike:
                        whereFilters.push(`${mapping[field]} ILIKE '${filterValue}'`);
                        break;
                    case Op.notILike:
                        whereFilters.push(`${mapping[field]} NOT ILIKE '${filterValue}'`);
                        break;
                    case Op.gte:
                        whereFilters.push(`${mapping[field]} >= '${filterValue}'`);
                        break;
                    case Op.lte:
                        whereFilters.push(`${mapping[field]} <= '${filterValue}'`);
                        break;
                    case Op.lt:
                        whereFilters.push(`${mapping[field]} < '${filterValue}'`);
                        break;
                    case Op.gt:
                        whereFilters.push(`${mapping[field]} > '${filterValue}'`);
                        break;
                    case Op.ne:
                        if (filterValue === null) {
                            whereFilters.push(`${mapping[field]} IS NOT NULL`);
                        } else {
                            whereFilters.push(`${mapping[field]} <> '${filterValue}'`);
                        }
                        break;
                    case Op.not:
                        whereFilters.push(`${mapping[field]} IS NOT ${filterValue}`);
                        break;
                }
            }
        }
    }
    const whereStr: string = conditionsArrayToSqlString(whereFilters);
    const sorting: string[] = [];
    const order = findOptions.order as Array<[{ col: string; }, string]>;
    if (order?.length) {
        const [{ col: sortKey }, sortOrder] = order[0];
        sorting.push(`${mapping[sortKey.replace(/"/g, "")]} ${sortOrder}`);
    }
    const sortOrderStr: string = conditionsArrayToSqlString(sorting);
    return { whereFilters: whereStr, sortOrder: sortOrderStr, limit: findOptions.limit, offset: findOptions.offset };
}

function isJsonField(value): boolean {
    const [firstKey] = Object.getOwnPropertySymbols(value);
    return !SEQUELIZE_OPS.includes(firstKey);
}

function conditionsArrayToSqlString(arr: string[]): string {
    if (arr.length === 0) {
        return "";
    }
    return `"${arr.join("\", \"").replace(/'/g, "''")}"`;
}

export interface LiveStudioQuery {
    where?: any;
    orderBy?: {
        [key: string]: "ASC" | "DESC"
    };
    offset?: number;
    limit?: number;
}

export function convertLiveStudioQuery(query: LiveStudioQuery, keys: string[]): WhereOptions<any> {
    const where: WhereOptions<any> = {};

    for (const key of keys) {
        Object.assign(where, parseKey(query.where, key));
    }
    if (query.orderBy) {
        const [sortBy, sortOrder] = Object.entries(query.orderBy)[0];
        where.sortBy = sortBy;
        where.sortOrder = sortOrder;
    }
    if (query.offset) {
        where.offset = query.offset;
    }
    if (query.limit) {
        where.limit = query.limit;
    }
    return where;
}
