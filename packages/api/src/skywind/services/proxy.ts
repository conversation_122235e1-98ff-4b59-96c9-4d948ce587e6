import { CRUDServiceImpl } from "./crudService";
import { ProxyDBInstance, ProxyModel } from "../models/proxy";
import { ValidationError } from "../errors";
import { Transaction } from "sequelize";
import { VARCHAR_DEFAULT_LENGTH } from "../utils/common";
import { EntityInfo, Includable } from "../entities/entity";
import { getMerchantEntities } from "./entity";
import { getMerchantSearchService } from "./merchant";
import { Models } from "../models/models";
import { isValidUrl } from "../utils/validateUrl";
import { Proxy } from "../entities/proxy";

const model = Models.ProxyModel;
const merchantModel = Models.MerchantModel;

export class ProxyService extends CRUDServiceImpl<ProxyDBInstance, Proxy, ProxyModel>
    implements Includable<{ proxy: Proxy }> {

    public getModel(): ProxyModel {
        return model;
    }

    protected validateCreateData(data: Proxy): Proxy {
        const errorMessages = [];
        if (!(typeof data.url === "string" &&
            isValidUrl(data.url, { supportDockerDomain: true }))) {
            errorMessages.push(`proxy ${data.url} should be a url`);
        } else if (data.url.length > VARCHAR_DEFAULT_LENGTH) {
            errorMessages.push(`proxy url is too long value, max length - ${VARCHAR_DEFAULT_LENGTH} chars`);
        }

        if ("description" in data) {
            if (typeof data.description !== "string") {
                errorMessages.push("description should be a string");
            } else if (data.description.length > VARCHAR_DEFAULT_LENGTH) {
                errorMessages.push(`proxy description is too long value, max length - ${VARCHAR_DEFAULT_LENGTH} chars`);
            }
        }

        if (errorMessages.length) {
            throw new ValidationError(errorMessages);
        }

        return {
            url: data.url,
            description: data.description,
        };
    }

    protected validateUpdateData(data: Partial<Proxy>): Partial<Proxy> {
        const proxy: Partial<Proxy> = {};

        const errorMessages = [];
        if ("url" in data) {
            if (!(typeof data.url === "string" &&
                isValidUrl(data.url, { supportDockerDomain: true }))) {
                errorMessages.push(`proxy ${data.url} should be a url`);
            } else if (data.url.length > VARCHAR_DEFAULT_LENGTH) {
                errorMessages.push(`proxy url is too long value, max length - ${VARCHAR_DEFAULT_LENGTH} chars`);
            } else {
                proxy.url = data.url;
            }
        }

        if ("description" in data) {
            if (typeof data.description !== "string") {
                errorMessages.push("description should be a string");
            } else if (data.description.length > VARCHAR_DEFAULT_LENGTH) {
                errorMessages.push(`proxy description is too long value, max length - ${VARCHAR_DEFAULT_LENGTH} chars`);
            } else {
                proxy.description = data.description;
            }
        }

        if (!Object.keys(data).length) {
            errorMessages.push("data is empty");
        }

        if (errorMessages.length) {
            throw new ValidationError(errorMessages);
        }

        return proxy;
    }

    protected async performDestroy(instance: ProxyDBInstance, transaction: Transaction): Promise<void> {
        const merchant = await merchantModel.findOne({ where: { proxyId: instance.id }});
        if (merchant) {
            return Promise.reject(new ValidationError("Proxy belongs to merchant"));
        }

        return super.performDestroy(instance, transaction);
    }

    public async includeTo(info: EntityInfo): Promise<EntityInfo & { proxy: Proxy }> {
        const entities = getMerchantEntities(info);
        const merchants = await getMerchantSearchService().findAllByEntityKeys(entities.map(merchant => merchant.key));

        for (const { proxy, brandKey } of merchants) {
            const merchant = entities.find(entity => entity.key === brandKey);
            (merchant as  EntityInfo & { proxy: Proxy }).proxy = proxy;
        }

        return info as EntityInfo & { proxy: Proxy };
    }
}
