import {
    Model,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { gameServerDB as db } from "../storage/db";
/**
 * Game init settings
 */
export class GameInitSettingsModel extends Model<
    InferAttributes<GameInitSettingsModel>,
    InferCreationAttributes<GameInitSettingsModel>
> {
    declare gameId: string;
    declare version: string;
    declare data: any;
}

GameInitSettingsModel.init(
    {
        gameId: { field: "game_id", type: DataTypes.STRING, primaryKey: true },
        version: { field: "version", type: DataTypes.STRING, primaryKey: true },
        data: { field: "data", type: DataTypes.JSONB, allowNull: false },
    },
    {
        modelName: "game_init_settings",
        sequelize: db,
        timestamps: false,
        freezeTableName: true
    }
);

export function getGameInitSettingsModel() {
    return GameInitSettingsModel;
}
