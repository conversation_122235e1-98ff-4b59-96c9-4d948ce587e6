import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    Association,
    NonAttribute,
    CreationOptional,
    ForeignKey,
    Sequelize,
} from "sequelize";
import { EntityModel } from "./entity";
import { sequelize as db } from "../storage/db";
import type { ItemInfo } from "../entities/agent";

export class AgentModel extends Model<
    InferAttributes<AgentModel, { omit: "entity" }>,
    InferCreationAttributes<AgentModel, { omit: "entity" }>
> {
    declare id: CreationOptional<number>;
    declare brandId: ForeignKey<EntityModel["id"]>;
    declare status: string;
    declare domain: string;
    declare title: string | null;
    declare affiliateCode: string | null;
    declare description: string | null;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    declare entity?: NonAttribute<EntityModel>;

    declare static associations: {
        entity: Association<AgentModel, EntityModel>;
    };

    public toInfo(): ItemInfo {
        return {
            id: this.id,
            domain: this.domain,
            title: this.title,
            affiliateCode: this.affiliateCode,
            status: this.status,
            description: this.description,
            brandId: this.entity?.id || undefined,
            brandTitle: this.entity?.title || undefined,
        };
    }

    static initModel(sequelize: Sequelize): IAgentModel {
        const schema = {
            id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
            brandId: {
                type: DataTypes.INTEGER,
                field: "brand_id",
                unique: "domainForBrand",
                allowNull: false,
                references: {
                    model: EntityModel,
                    key: "id",
                },
            },
            domain: {
                type: DataTypes.STRING,
                field: "domain",
                unique: "domainForBrand",
                allowNull: false,
                validate: { notEmpty: true },
            },
            status: {
                type: DataTypes.ENUM("normal", "suspended"),
                field: "status",
                allowNull: false,
            },
            title: {
                type: DataTypes.STRING,
                field: "title",
                allowNull: true,
            },
            affiliateCode: {
                type: DataTypes.STRING,
                field: "affiliate_code",
            },
            description: {
                type: DataTypes.STRING,
                field: "description",
            },
            createdAt: {
                type: DataTypes.DATE,
                field: "created_at",
            },
            updatedAt: {
                type: DataTypes.DATE,
                field: "updated_at",
            },
        };
        return AgentModel.init(
            schema,
            {
                modelName: "agent",
                tableName: "agents",
                sequelize,
                underscored: true,
                indexes: [
                    { fields: ["brand_id"] },
                ],
            }
        );
    }
}

export type IAgentModel = ModelStatic<AgentModel>;
AgentModel.initModel(db);

AgentModel.belongsTo(EntityModel, { foreignKey: "brandId" });

export function get() {
    return AgentModel;
}
