import { CreationOptional, DataTypes, InferAttributes, InferCreationAttributes, Model, } from "sequelize";
import { sequelize as db } from "../storage/db";
import { lazy } from "@skywind-group/sw-utils";
import { getGameModel } from "./game";
import { getDeploymentGroupModel } from "./deploymentGroup";
import { GameClientFeatures } from "../entities/game";
import { GameVersionInfo } from "../entities/gameClientVersion";

const deploymentGroupModel = getDeploymentGroupModel();
const gameModel = getGameModel();

export class GameVersionModel extends Model<
    InferAttributes<GameVersionModel>,
    InferCreationAttributes<GameVersionModel>
> {
    declare id: CreationOptional<number>;
    declare deploymentGroupId: number;
    declare gameCode: string;
    declare clientVersion: string;
    declare clientFeatures: GameClientFeatures;

    public toInfo(): GameVersionInfo {
        return {
            deploymentGroupId: this.deploymentGroupId,
            gameCode: this.gameCode,
            clientVersion: this.clientVersion,
            clientFeatures: this.clientFeatures,
        };
    }
}

const gameVersionSchema = {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true
    },
    deploymentGroupId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        field: "deployment_group_id"
    },
    gameCode: {
        type: DataTypes.STRING,
        allowNull: false,
        field: "game_code"
    },
    clientVersion: {
        type: DataTypes.STRING,
        allowNull: false,
        field: "client_version"
    },
    clientFeatures: {
        type: DataTypes.JSONB,
        field: "client_features",
    },
};

const gameVersionsModel = lazy(
    () => {
        GameVersionModel.init(
            gameVersionSchema,
            {
                modelName: "game_versions",
                tableName: "game_versions",
                sequelize: db,
                timestamps: false,
                indexes: [
                    { name: "idx_game_code", unique: false, fields: [ "game_code" ] },
                    { name: "idx_game_code_deployment_group_id", unique: true, fields: [ "game_code", "deployment_group_id" ] },

                ],
            }
        );
        return GameVersionModel;
    }
);

getGameVersionModel().belongsTo(deploymentGroupModel,
    { foreignKey: "deploymentGroupId", targetKey: "id", onDelete: "CASCADE" });
getGameVersionModel().belongsTo(gameModel,
    { foreignKey: "game_code", targetKey: "code", onDelete: "CASCADE" });

export function getGameVersionModel() {
    return gameVersionsModel.get();
}
