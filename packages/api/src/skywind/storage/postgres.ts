import * as pg from "pg";
import config from "../config";

pg.types.setTypeParser(1114, function(stringValue) {
    return new Date(Date.parse(stringValue + "+0000"));
});

let db = config.db;
const poolConfig = {
    user: db.user,
    database: db.database,
    password: db.password,
    host: db.host,
    port: db.port,
    max: db.maxConnections, // max number of clients in the pool
    idleTimeoutMillis: db.maxIdleTime, // how long a client is allowed to remain idle before being closed
    schema: db.schema,
};

export const postgres: pg.Pool = new pg.Pool(poolConfig);

db = config.dbForReportingSlave;
const poolConfigSlave = {
    user: db.user,
    database: db.database,
    password: db.password,
    host: db.host,
    port: db.port,
    max: db.maxConnections, // max number of clients in the pool
    idleTimeoutMillis: db.maxIdleTime, // how long a client is allowed to remain idle before being closed
    schema: db.schema,
};

export const postgresSlave: pg.Pool = new pg.Pool(poolConfigSlave);
