import { expect, should, use } from "chai";
import { testAnonymousPlayFacade as AnonymousPlayService, testPlayFacade as PlayService } from "../testPlayFacade";
import * as GameService from "../../skywind/services/game";
import { GameCodeInfo } from "../../skywind/entities/game";
import {
    BY_IP,
    CN_IP,
    complexStructure,
    createComplexStructure,
    createRandomGameProvider,
    flushAll,
    registerGame,
    setDynamicDomain,
    truncate,
    US_IP
} from "./helper";
import * as Errors from "../../skywind/errors";
import { NumberOfTestPlayersIsExceeded, PlayerIsSuspended } from "../../skywind/errors";
import { BaseEntity, ChildEntity } from "../../skywind/entities/entity";
import { Limits, LimitsByCurrencyCode } from "../../skywind/entities/gamegroup";
import { BrandEntity } from "../../skywind/entities/brand";
import { Player } from "../../skywind/entities/player";
import * as TokenUtils from "../../skywind/utils/token";
import getPlayerService, { getBrandPlayerService } from "../../skywind/services/brandPlayer";
import {
    AccountPropertiesFilter,
    AccountProperty,
    BALANCE_WITH_RT_FILTER,
    ONLY_BALANCE_FILTER,
    ONLY_RT_FILTER,
    WalletErrors,
    WalletFacade
} from "@skywind-group/sw-management-wallet";
import * as sinon from "sinon";
import { mock, SinonStub, stub } from "sinon";
import * as jwt from "jsonwebtoken";
import { AnyMerchantAdapter } from "@skywind-group/sw-management-adapters";
import PlayerResponsibleGamingServiceImpl, {
    PlayerResponsibleGamingUpdateSettings
} from "../../skywind/services/playerResponsibleGaming";
import {
    DetailedStartGameResponse,
    GameLogoutType,
    GameProviderInfo,
    GamesListRequest,
    PlayerGameInfoRequest
} from "../../skywind/entities/gameprovider";
import { getDomainService } from "../../skywind/services/domain";
import EntitySettingsService, * as entitySettingsService from "../../skywind/services/settings";
import EntityCountryService from "../../skywind/services/entityCountry";
import getUserService from "../../skywind/services/user/user";
import PlayerGameSessionService from "../../skywind/services/player/playerGameSessionService";
import * as EntityService from "../../skywind/services/entity";
import { findOne } from "../../skywind/services/entity";
import { TrxId } from "@skywind-group/sw-wallet/lib/skywind/services/trxId";
import { CreateData } from "../../skywind/entities/jurisdiction";
import { getEntityJurisdictionService } from "../../skywind/services/entityJurisdiction";
import * as GameProviderService from "../../skywind/services/gameprovider";
import config from "../../skywind/config";
import { getGameGroupGameService, getGameGroupService } from "../../skywind/services/gamegroup";
import { reset } from "../../skywind/cache/merchant";
import {
    FreeBetInfoRequest,
    GameLogoutGameState,
    GameLogoutOptions,
    GameTokenData,
    GGRCalculationType,
    MerchantGameTokenData,
    MerchantStartGameTokenData,
    PlayMode,
    StartGameTokenData
} from "@skywind-group/sw-wallet-adapter-core";
import { getMerchantCRUDService } from "../../skywind/services/merchant";
import { RegulatoryLink } from "../../skywind/entities/settings";
import { DeferredPayment, DeferredPaymentMethod } from "@skywind-group/sw-deferred-payment";
import { getEntityGameService } from "../../skywind/services/entityGameService";
import getBlockPlayerService from "../../skywind/services/blockedPlayer";
import getMerchantTestPlayerService from "../../skywind/services/merchantTestPlayer";
import { createPlayerSessionFacade } from "../../skywind/services/player/playerSessionFacade";
import * as request from "request";
import { get as getJurisdictionModel } from "../../skywind/models/jurisdiction";
import { PromoWalletErrors } from "@skywind-group/sw-management-promo-wallet";
import {
    GamePaymentAction,
    GamePaymentOperation,
    PaymentActionType,
    PlayServiceErrors
} from "@skywind-group/sw-management-playservice";
import { PlayerSessionErrors } from "@skywind-group/sw-management-playersession";
import { register } from "../helper";
import { DisabledDeferredPaymentFacade } from "@skywind-group/sw-management-deferredpayment";
import { GameProviderErrors } from "@skywind-group/sw-management-gameprovider-core";
import { token as swUtilsToken } from "@skywind-group/sw-utils";
import { getGames, getPlayerGameURLInfo } from "../../skywind/services/playService";
import { ProviderAuthDetails, StartGameResponse } from "@skywind-group/sw-management-gameprovider";
import { factory } from "factory-girl";
import { FACTORY } from "../factories/common";
import { GAME_TYPES } from "../../skywind/utils/common";
import { DeploymentGroupRoute } from "../../skywind/services/deploymentGroup";
import { encodeId } from "../../skywind/utils/publicid";
import * as EntityJurisdictionCache from "../../skywind/cache/entityJurisdiction";
import * as DynamicDomainCache from "../../skywind/cache/dynamicDomainCache";

const chaiAsPromise = require("chai-as-promised");

should();
use(chaiAsPromise);

describe("Play service", function () {
    this.timeout(20000);

    let master: BaseEntity;
    let brandEntity: BrandEntity;
    let brandWithDomain: BrandEntity;
    let merchBrandEntity: BrandEntity;
    let brandEntityWithPlayerPrefix: BrandEntity;
    let player: Player;
    let player2: Player;
    let gameCodeInfo2: GameCodeInfo;
    let gameCodeInfoTestGame: GameCodeInfo;
    let gameCodeInfo: GameCodeInfo;
    let externalGameInfo: GameCodeInfo;
    let authTokenForBrand: GameTokenData;
    let authTokenForBrandWithDomain: GameTokenData;
    let authTokenForMerchant: GameTokenData;
    let p2: GameProviderInfo;
    let entityDomain;
    let deferredFacadeLoad: SinonStub;
    let deferredFacadeGet: SinonStub;
    let requestMock: SinonStub;
    let masterSettingsService: EntitySettingsService;

    const limitsData: LimitsByCurrencyCode = {
        "USD": {
            maxTotalStake: 100,
            stakeAll: [0.1, 0.5, 1, 2, 3, 5],
            stakeDef: 1,
            stakeMax: 5,
            stakeMin: 0.1,
            winMax: 2000,
        },
    };

    const PUBLIC_ID = encodeId(1000);

    before(async () => {
        await truncate();
        master = await createComplexStructure();
        const p1 = await createRandomGameProvider();
        p2 = await createRandomGameProvider();

        gameCodeInfo = await registerGame(p1.code, "GAME001", "Game 1", undefined, undefined,
            {
                isFreebetSupported: false, highestPrizeProbability: 99, isGRCGame: false, gamble: true,
                validateRequestsExtensionEnabled: true, zeroBetCheckEnabled: true
            });
        gameCodeInfo2 = await registerGame(p2.code, "GAME002", "Game 2", undefined, undefined,
            { isFreebetSupported: true });

        gameCodeInfoTestGame = await registerGame(p2.code, "GAME003", "Game 3", undefined, undefined,
            { isFreebetSupported: false });

        externalGameInfo = await registerGame(p2.code, "GAME004_ext", "Game 4", undefined, undefined,
            { isFreebetSupported: false }, undefined, GAME_TYPES.external);

        await (new EntityCountryService(master.find({ key: complexStructure.tle1.key }) as ChildEntity))
            .add(["US", "CN"]);
        await (new EntityCountryService(master.find({ key: complexStructure.tle1ent1.key }) as ChildEntity))
            .add(["US", "CN"]);

        brandEntity = await factory.create(FACTORY.BRAND, {}, {
            parent: master.find({ path: ":TLE1:ENT1:" }),
            countries: ["US", "CN"]
        });

        brandEntityWithPlayerPrefix = await factory.create(FACTORY.BRAND, {}, {
            parent: master.find({ path: ":TLE1:ENT1:" }),
            countries: ["US", "CN"]
        });

        entityDomain = await getDomainService().create({
            domain: "gc.gaming.skywindgroup.com",
            environment: "gc"
        });

        brandWithDomain = await factory.create(FACTORY.BRAND, {}, {
            parent: master.find({ path: ":TLE1:ENT1:" }),
            countries: ["US", "CN"]
        });

        await setDynamicDomain(brandWithDomain, entityDomain);
        brandWithDomain.addCountry("CN");
        await brandWithDomain.save();

        await GameService.addGameToEntity(master,
            { path: ":TLE1:" },
            gameCodeInfo.code, false, { status: "normal" });
        await GameService.addGameToEntity(master,
            { path: ":TLE1:" },
            gameCodeInfoTestGame.code, false, { status: "test" });
        await GameService.addGameToEntity(master,
            { path: ":TLE1:" },
            externalGameInfo.code, false, { status: "test" });
        await GameService.addGameToEntity(master, { path: ":TLE1:" }, gameCodeInfo2.code, false, { status: "normal" });
        await GameService.addGameToEntity(master,
            { path: ":TLE1:ENT1:" },
            gameCodeInfo.code,
            false,
            { status: "normal" });
        await GameService.addGameToEntity(master,
            { path: ":TLE1:ENT1:" },
            gameCodeInfoTestGame.code,
            false,
            { status: "test" });
        await GameService.addGameToEntity(master,
            { path: ":TLE1:ENT1:" },
            externalGameInfo.code,
            false,
            { status: "test" });
        await GameService.addGameToEntity(master,
            { path: ":TLE1:ENT1:" },
            gameCodeInfo2.code,
            false,
            { status: "normal" });
        await GameService.addGameToEntity(master,
            { path: brandEntity.path }, gameCodeInfo.code, false, { status: "normal" });
        await GameService.addGameToEntity(master,
            { path: brandEntity.path }, gameCodeInfo2.code, false, { status: "normal" });
        await GameService.addGameToEntity(master,
            { path: brandEntity.path }, gameCodeInfoTestGame.code, false, { status: "test" });
        await GameService.addGameToEntity(master,
            { path: brandEntity.path }, externalGameInfo.code, false, { status: "test" });

        await GameService.addGameToEntity(master,
            { path: brandWithDomain.path }, gameCodeInfo.code, false, { status: "normal" });
        await GameService.addGameToEntity(master,
            { path: brandWithDomain.path }, gameCodeInfo2.code, false, { status: "normal" });
        await GameService.addGameToEntity(master,
            { path: brandWithDomain.path }, gameCodeInfoTestGame.code, false, { status: "test" });
        player = await getPlayerService().create(brandEntity,
            {
                code: "PL00001",
                password: "ACAB!Area51",
                firstName: "f_name",
                lastName: "l_name",
                email: "<EMAIL>",
                customData: ["The roof the roof the roof in on fire"],
                country: "US",
                currency: "USD",
                language: "en",
            });

        player2 = await getPlayerService().create(brandWithDomain,
            {
                code: "PL00001",
                password: "ACAB!Area51",
                firstName: "f_name",
                lastName: "l_name",
                email: "<EMAIL>",
                customData: ["The roof the roof the roof in on fire"],
                country: "US",
                currency: "USD",
                language: "en",
            });

        const testAdapter: any = {
            validate: function (): string[] {
                return [];
            },
            getGameTokenInfo: function (merchant, tokenData, currency) {
                return {
                    gameTokenData: {
                        desc: "Custom merchant game token",
                        playerCode: "SOME_CUSTOMER",
                        merchantSessionId: "SOME_SESSION_ID",
                        merchantType: "test",
                        merchantCode: "merch_cooool",
                        currency: currency
                    }
                };
            },
            getBalances: () => {
                return { "USD": { "main": 100 } };
            }
        };

        await register("test", testAdapter as AnyMerchantAdapter);

        requestMock = stub(request, "post");
        masterSettingsService = new EntitySettingsService(master);
        const settings = await masterSettingsService.get();
        settings.bonusPaymentMethod = DeferredPaymentMethod.CREDIT;
        settings.maxTestPlayers = 1;

        const gameLogoutOptions: GameLogoutOptions = {
            type: GameLogoutType.ALL,
            maxSessionTimeout: 5,
            maxRetryAttempts: 10
        };

        const createdMerchBrandEntity = await factory.create(FACTORY.MERCHANT, {}, {
            entityBuildOptions: {
                parent: master,
                name: "brand2",
                description: "brand2 description",
                defaultCurrency: "USD",
                defaultCountry: "US",
                defaultLanguage: "en",
            },
            code: "merch1_chebureck",
            type: "test",
            params: { serverUrl: "http://google.com", password: "Qwerty1234!", gameLogoutOptions }
        });
        merchBrandEntity = await findOne({ id: createdMerchBrandEntity.brandId }) as BrandEntity;

        await GameService.addGameToEntity(master,
            { path: merchBrandEntity.path }, gameCodeInfo.code, false, { status: "normal" });
        await GameService.addGameToEntity(master,
            { path: merchBrandEntity.path }, gameCodeInfo2.code, false, { status: "normal" });

        await GameService.addGameToEntity(master,
            { path: merchBrandEntity.path }, gameCodeInfoTestGame.code, false, { status: "test" });

        await flushAll();

        authTokenForBrand = {
            gameCode: "GAME001zzz",
            brandId: brandEntity.id,
            playerCode: "PL00001",
            currency: "USD"
        };

        authTokenForMerchant = {
            gameCode: "GAME001zzz",
            brandId: merchBrandEntity.id,
            playerCode: "PL00001",
            currency: "USD"
        };

        authTokenForBrandWithDomain = {
            gameCode: "GAME001zzz",
            brandId: brandWithDomain.id,
            playerCode: "PL00001",
            currency: "USD",
            envId: entityDomain.environment
        };

        deferredFacadeLoad = stub(DisabledDeferredPaymentFacade.prototype, "load");
        deferredFacadeGet = stub(DisabledDeferredPaymentFacade.prototype, "get");

        await factory.create(FACTORY.DEPLOYMENT_GROUP_GAME, {}, { route: DeploymentGroupRoute.live });
    });

    after(async () => {
        const settingsService = new EntitySettingsService(brandEntity);
        const settings: any = {};
        settings.responsibleGaming = { enabled: false, jurisdiction: "UK" };
        await settingsService.patch(settings);
        deferredFacadeLoad.restore();
        deferredFacadeGet.restore();

        new EntitySettingsService(merchBrandEntity).patch({
            isAccountBlockingEnabled: false
        });
        requestMock.restore();
    });

    beforeEach(async () => {
        await flushAll();

        let trx = await WalletFacade.startTransaction();
        await player.wallet.deposit(trx, { currency: "USD", amount: 1000 });
        await trx.commit();

        trx = await WalletFacade.startTransaction();
        await player2.wallet.deposit(trx, { currency: "USD", amount: 1000 });
        await trx.commit();
        await PlayerGameSessionService.create(
            authTokenForBrand.brandId, authTokenForBrand.playerCode, authTokenForBrand.gameCode, { isGRCGame: true });
        await PlayerGameSessionService.create(
            authTokenForMerchant.brandId, authTokenForMerchant.playerCode, authTokenForMerchant.gameCode,
            { isGRCGame: true });
        await PlayerGameSessionService.create(authTokenForBrandWithDomain.brandId,
            authTokenForBrandWithDomain.playerCode,
            authTokenForBrandWithDomain.gameCode,
            { isGRCGame: true });
        deferredFacadeGet.returns(undefined);
        deferredFacadeLoad.returns(undefined);
    });

    afterEach(async () => {
        requestMock.resetBehavior();
        deferredFacadeGet.resetBehavior();
        deferredFacadeLoad.resetBehavior();
    });

    async function setResponsibleGaming(playerSettings: PlayerResponsibleGamingUpdateSettings): Promise<void> {
        const settingsService = new EntitySettingsService(brandEntity);
        await settingsService.patch({ responsibleGaming: { enabled: true, jurisdiction: "UK" } } as any);

        await new PlayerResponsibleGamingServiceImpl(brandEntity).updatePlayerResponsibleGamingSettings(
            player.code, playerSettings);
    }

    it("Full flow: authenticate/balance/generateTransactinId/commitPayment", async () => {
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        const response = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);

        const balance = await PlayService.getGameBalance(response.gameToken);

        expect(balance).deep.equal({ main: 1000 });

        const balances = await PlayService.getPlayerBalances(response.gameToken);
        expect(balances).deep.equal({ USD: { main: 1000 } });

        const transactionId = await PlayService.generateTransactionId(response.gameToken);
        const trxId = await WalletFacade.parseTransactionId(transactionId);

        const result = await PlayService.commitGamePayment({
            gameToken: response.gameToken,
            transactionId: trxId,
            roundId: "1000",
            gameSessionId: "1",
            bet: 100,
            win: 200,
            ts: new Date().toISOString()
        });
        expect(result).deep.equal({
                main: 1100,
                previousValue: 1000,
                extraData: {
                    roundPID: PUBLIC_ID
                }
            }
        );
        const balancesAfter = await PlayService.getPlayerBalances(response.gameToken);
        expect(balancesAfter).deep.equal({ USD: { main: 1100 } });

        expect(response.region).to.equal(config.region);
    });

    it("Start game request without currency in request", async () => {
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        const response = await PlayService.startGame({
            startGameToken: startGameToken,
        }, US_IP);

        const limits: Limits = limitsData["USD"];
        expect(response.limits).deep.equal(limits);
    });

    it("Merchant start game request - validate player is blocked", async () => {
        const blockedPlayer = "merchant_player";
        await getBlockPlayerService(merchBrandEntity).block(blockedPlayer);

        new EntitySettingsService(merchBrandEntity).patch({
            isAccountBlockingEnabled: true,
        });
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: merchBrandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: blockedPlayer,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        await PlayService.startGame({
            startGameToken: startGameToken,
        }, US_IP).should.eventually.rejectedWith(PlayerIsSuspended);
    });

    it("Merchant start game request - test player", async () => {
        let startGameToken = await TokenUtils.generateStartGameToken({
            brandId: merchBrandEntity.id,
            gameCode: "GAME003",
            providerCode: "providerCode1",
            providerGameCode: "GAME003",
            playerCode: "custom_pl",
            currency: player.currency,
            test: true,
            playmode: PlayMode.REAL
        });
        await new EntitySettingsService(merchBrandEntity).patch({
            ggrCalculation: GGRCalculationType.ROUND
        });
        const response = await PlayService.startGame({
            startGameToken: startGameToken,
        }, US_IP);
        const tokenData = jwt.decode(response.gameToken) as any;
        expect(tokenData.ggrCalculation).to.equal("round");
        expect(tokenData.freeBetsDisabled).to.equal(true);

        const limits: Limits = limitsData["USD"];
        expect(response.limits).deep.equal(limits);

        const numberOfTestPlayers = await getMerchantTestPlayerService(merchBrandEntity).count();
        expect(numberOfTestPlayers).to.be.equal(1);

        new EntitySettingsService(merchBrandEntity).patch({
            maxTestPlayers: 1,
        });

        startGameToken = await TokenUtils.generateStartGameToken({
            brandId: merchBrandEntity.id,
            gameCode: "GAME003",
            providerCode: "providerCode1",
            providerGameCode: "GAME003",
            playerCode: "custom_pl_2",
            currency: player.currency,
            test: true,
            playmode: PlayMode.REAL
        });

        await PlayService.startGame({
            startGameToken: startGameToken,
        }, US_IP).should.eventually.rejectedWith(NumberOfTestPlayersIsExceeded);

    });

    it("Merchant start game request - zero entity balance", async () => {
        let startGameToken = await TokenUtils.generateStartGameToken({
            brandId: merchBrandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: "custom_pl_4",
            currency: player.currency,
            test: false,
            playmode: PlayMode.REAL
        });

        const response = await PlayService.startGame({
            startGameToken: startGameToken,
        }, US_IP);

        const limits: Limits = limitsData["USD"];
        expect(response.limits).deep.equal(limits);

        startGameToken = await TokenUtils.generateStartGameToken({
            brandId: merchBrandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: "custom_pl_4",
            currency: player.currency,
            test: true,
            playmode: PlayMode.REAL
        });

        new EntitySettingsService(merchBrandEntity).patch({
            isAccountBlockingEnabled: true
        });

        await PlayService.startGame({
            startGameToken: startGameToken,
        }, US_IP).should.eventually.rejectedWith(WalletErrors.InsufficientEntityBalanceError);

    });

    it("Start game return deferred payments", async () => {
        const getEntitySettingsStub = sinon.stub(entitySettingsService, "getEntitySettings");
        getEntitySettingsStub.resolves({ bonusPaymentMethod: "credit" } as any);
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        const deferredPayment: DeferredPayment = {
            id: "1",
            brandId: 1,
            playerCode: "playerCode",
            amount: 1000,
            currencyCode: "USD"
        };

        deferredFacadeGet.returns([deferredPayment]);
        deferredFacadeLoad.returns([deferredPayment]);

        const response = await PlayService.startGame({
            startGameToken: startGameToken,
        }, US_IP);

        const limits: Limits = limitsData["USD"];
        expect(response.limits).deep.equal(limits);
        expect(response.balance.deferredPayments).deep.equals([deferredPayment]);
        const tokenData = jwt.decode(response.gameToken) as any;
        expect(tokenData.freeBetsDisabled).to.equal(true);
        getEntitySettingsStub.restore();
    });

    it("commitPayment returns deferred payments", async () => {
        const deferredPayment: DeferredPayment = {
            id: "1",
            brandId: 1,
            playerCode: "playerCode",
            amount: 1000,
            currencyCode: "USD"
        };

        deferredFacadeGet.returns([deferredPayment]);
        deferredFacadeLoad.returns([deferredPayment]);

        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        const response = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);
        const transactionId = await PlayService.generateTransactionId(response.gameToken);
        const trxId = await WalletFacade.parseTransactionId(transactionId);

        const result = await PlayService.commitGamePayment({
            gameToken: response.gameToken,
            transactionId: trxId,
            roundId: "1000",
            gameSessionId: "1",
            bet: 100,
            win: 200,
            ts: new Date().toISOString()
        });
        expect(result).deep.equal({
            main: 1100,
            previousValue: 1000,
            deferredPayments: [deferredPayment],
            extraData: {
                roundPID: PUBLIC_ID
            }
        });
        const balancesAfter = await PlayService.getPlayerBalances(response.gameToken);
        expect(balancesAfter).deep.equal({ USD: { main: 1100 } });

        expect(response.region).to.equal(config.region);
    });

    it("Full flow: authenticate/balance/generateTransactinId/commitBetAndWinPayment", async () => {
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        await (new EntitySettingsService(brandEntity)).patch({
            autoPlaySettings: [
                { label: "5", value: 5 },
                { label: "until feature", value: 0, isUntilFeature: true }
            ]
        });

        await getEntityGameService(brandEntity).update("GAME001", { settings: null });

        const response = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);

        expect(response.balance).deep.equal({ main: 1000 });
        expect(response.player).contain({
            "brandId": 8,
            "code": "PL00001",
            "country": "US",
            "currency": "USD",
            "id": 1,
            "isTest": false,
            "language": "en",
            "status": "normal"
        });
        expect(response.settings.autoSpinsOptions).deep.equal([
            { label: "5", value: 5 },
            { label: "until feature", value: 0, isUntilFeature: true }
        ]);

        const balance = await PlayService.getGameBalance(response.gameToken);

        expect(balance).deep.equal({ main: 1000 });

        const balances = await PlayService.getPlayerBalances(response.gameToken);
        expect(balances).deep.equal({ USD: { main: 1000 } });

        const transactionId = await PlayService.generateTransactionId(response.gameToken);
        const trxId = await WalletFacade.parseTransactionId(transactionId);

        const betResult = await PlayService.commitBetPayment({
            gameToken: response.gameToken,
            transactionId: trxId,
            roundId: "1000",
            gameSessionId: "1",
            amount: 100,
            ts: new Date().toISOString()
        });
        expect(betResult)
            .deep
            .equal({ main: 900, previousValue: 1000, extraData: { roundPID: PUBLIC_ID } });
        const balancesAfterBet = await PlayService.getPlayerBalances(response.gameToken);
        expect(balancesAfterBet).deep.equal({ USD: { main: 900 } });
        const winResult = await PlayService.commitWinPayment({
            gameToken: response.gameToken,
            transactionId: trxId,
            roundId: "1000",
            gameSessionId: "1",
            amount: 200,
            ts: new Date().toISOString()
        });
        expect(winResult)
            .deep
            .equal({ main: 1100, previousValue: 900, extraData: { roundPID: PUBLIC_ID } });
        const balancesAfterWin = await PlayService.getPlayerBalances(response.gameToken);
        expect(balancesAfterWin).deep.equal({ USD: { main: 1100 } });

        expect(response.region).to.equal(config.region);
    });

    it("Start game request - get group limits", async () => {
        await getGameGroupService().create(brandEntity, { name: "Default" });
        await getGameGroupGameService().add(brandEntity,
            "Default",
            "GAME001",
            {
                "USD": {
                    "maxTotalStake": 300,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 5,
                    "stakeMin": 0.1,
                    "winMax": 3000,
                },
            });

        await getBrandPlayerService().updateGameGroup(brandEntity,
            { code: player.code },
            "Default");
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        const response = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);
        const gameTokenData = jwt.decode(response.gameToken) as GameTokenData;

        expect(gameTokenData).contain(
            {
                "brandId": brandEntity.id,
                "currency": "USD",
                "gameCode": "GAME001",
                "playerCode": "PL00001",
            });
        const limits: Limits = {
            "maxTotalStake": 300,
            "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
            "stakeDef": 1,
            "stakeMax": 5,
            "stakeMin": 0.1,
            "winMax": 3000
        };
        expect(response.limits).deep.equal(limits);
        expect(response.balance).deep.equal({ main: 1000 });
    });

    it("Start game when new limits enabled - get group limits as entity game doesn't support new limits", async () => {
        await new EntitySettingsService(brandEntity).patch({ newLimitsEnabled: true });

        await getEntityGameService(brandEntity).update("GAME001", { settings: { newLimitsDisabled: true } });
        await getGameGroupService().create(brandEntity, { name: "VIP-10" });
        await getGameGroupGameService().add(brandEntity,
            "VIP-10",
            "GAME001",
            {
                "USD": {
                    "maxTotalStake": 300,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 5,
                    "stakeMin": 0.1,
                    "winMax": 3000,
                },
            });

        await getBrandPlayerService().updateGameGroup(brandEntity,
            { code: player.code },
            "VIP-10");
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        const response = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);
        const gameTokenData = jwt.decode(response.gameToken) as GameTokenData;

        expect(gameTokenData).contain(
            {
                "brandId": brandEntity.id,
                "currency": "USD",
                "gameCode": "GAME001",
                "playerCode": "PL00001",
            });
        const limits: Limits = {
            "maxTotalStake": 300,
            "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
            "stakeDef": 1,
            "stakeMax": 5,
            "stakeMin": 0.1,
            "winMax": 3000
        };
        expect(response.limits).deep.equal(limits);
        expect(response.balance).deep.equal({ main: 1000 });
    });

    it("Start game request - get group limits and override default", async () => {
        await getGameGroupService().create(brandEntity, { name: "Override" });
        await getGameGroupGameService().add(brandEntity,
            "Override",
            "GAME001",
            {
                "USD": {
                    "maxTotalStake": 300,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 5,
                    "stakeMin": 0.1,
                    "winMax": 3000,
                },
            }, true);

        await getBrandPlayerService().updateGameGroup(brandEntity,
            { code: player.code },
            "Override");
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        const response = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);
        const gameTokenData = jwt.decode(response.gameToken) as GameTokenData;

        expect(gameTokenData).contain(
            {
                "brandId": brandEntity.id,
                "currency": "USD",
                "gameCode": "GAME001",
                "playerCode": "PL00001",
            });
        const limits: Limits = {
            "maxTotalStake": 300,
            "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
            "stakeDef": 1,
            "stakeMax": 5,
            "stakeMin": 0.1,
            "winMax": 3000
        };
        expect(response.limits).deep.equal(limits);
        expect(response.balance).deep.equal({ main: 1000 });
    });

    it("Start game request - get group limits from reseller", async () => {
        const parent = await master.find({ path: ":TLE1:ENT1:" });
        await new EntitySettingsService(parent).patch({ gameGroupsInheritance: true });

        await getGameGroupService().create(parent, { name: "VIP-1" });
        await getGameGroupGameService().add(parent,
            "VIP-1",
            "GAME001",
            {
                "USD": {
                    "maxTotalStake": 300,
                    "stakeAll": [0.1, 0.3, 1, 2, 3, 7],
                    "stakeDef": 1,
                    "stakeMax": 7,
                    "stakeMin": 0.1,
                    "winMax": 3030,
                },
            });

        await getBrandPlayerService().updateGameGroup(brandEntity,
            { code: player.code },
            "VIP-1");
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        const response = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);
        const gameTokenData = jwt.decode(response.gameToken) as GameTokenData;

        expect(gameTokenData).contain(
            {
                "brandId": brandEntity.id,
                "currency": "USD",
                "gameCode": "GAME001",
                "playerCode": "PL00001",
            });
        const limits: Limits = {
            "maxTotalStake": 300,
            "stakeAll": [0.1, 0.3, 1, 2, 3, 7],
            "stakeDef": 1,
            "stakeMax": 7,
            "stakeMin": 0.1,
            "winMax": 3030,
        };
        expect(response.limits).deep.equal(limits);
        expect(response.balance).deep.equal({ main: 1000 });
    });

    it("Start game request - get group limits and override default from reseller", async () => {
        const parent = await master.find({ path: ":TLE1:ENT1:" });
        await new EntitySettingsService(parent).patch({ gameGroupsInheritance: true });

        await getGameGroupService().create(brandEntity, { name: "Override-reseller" });
        await getGameGroupGameService().add(brandEntity,
            "Override-reseller",
            "GAME001",
            {
                "USD": {
                    "maxTotalStake": 300,
                    "stakeAll": [0.1, 0.3, 1, 2, 13, 50],
                    "stakeDef": 1,
                    "stakeMax": 50,
                    "stakeMin": 0.1,
                    "winMax": 3050,
                },
            }, true);

        await getBrandPlayerService().updateGameGroup(brandEntity,
            { code: player.code },
            "Override-reseller");
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        const response = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);
        const gameTokenData = jwt.decode(response.gameToken) as GameTokenData;

        expect(gameTokenData).contain(
            {
                "brandId": brandEntity.id,
                "currency": "USD",
                "gameCode": "GAME001",
                "playerCode": "PL00001",
            });
        const limits: Limits = {
            "maxTotalStake": 300,
            "stakeAll": [0.1, 0.3, 1, 2, 13, 50],
            "stakeDef": 1,
            "stakeMax": 50,
            "stakeMin": 0.1,
            "winMax": 3050,
        };
        expect(response.limits).deep.equal(limits);
        expect(response.balance).deep.equal({ main: 1000 });
    });

    it("Start game request - envId should be validated in start token and be present in game token", async () => {
        DynamicDomainCache.reset();
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandWithDomain.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player2.code,
            currency: player2.currency,
            envId: entityDomain.environment,
            playmode: PlayMode.REAL
        });

        const response = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);
        const gameTokenData = jwt.decode(response.gameToken) as GameTokenData;

        expect(gameTokenData).contain(
            {
                "brandId": brandWithDomain.id,
                "currency": "USD",
                "gameCode": "GAME001",
                "playerCode": "PL00001",
                "envId": entityDomain.environment
            });
    });

    it("Authentication failed - token expired", async () => {

        const clock = sinon.useFakeTimers();
        try {
            const startGameToken = await TokenUtils.generateStartGameToken(
                {
                    brandId: brandEntity.id,
                    gameCode: "GAME001",
                    providerCode: "providerCode1",
                    providerGameCode: "GAME001",
                    playerCode: player.code,
                    currency: player.currency,
                    playmode: PlayMode.REAL
                });

            clock.tick(100000000);

            await PlayService.startGame({ startGameToken: startGameToken }, US_IP)
                .should
                .eventually
                .rejectedWith(Errors.StartGameTokenExpired);
        } finally {
            clock.restore();
        }
    });

    it("Authentication failed - envId has changed", async () => {
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandWithDomain.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player2.code,
            currency: player2.currency,
            envId: "some-env-id",
            playmode: PlayMode.REAL
        });

        return PlayService.startGame({ startGameToken: startGameToken }, US_IP)
            .should
            .eventually
            .rejectedWith(Errors.EntityEnvIdChangedError);
    });

    it("Authentication should not fail if envId is not in the token", async () => {
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandWithDomain.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player2.code,
            currency: player2.currency,
            playmode: PlayMode.REAL
        });

        return PlayService.startGame({ startGameToken: startGameToken }, US_IP)
            .should
            .not
            .eventually
            .rejectedWith(Errors.EntityEnvIdChangedError);
    });

    it("Authentication failed - wrong player code", async () => {
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: "WRONGPLAYERCODE",
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        return PlayService.startGame({ startGameToken: startGameToken }, US_IP)
            .should
            .eventually
            .rejectedWith(Errors.PlayerNotFoundError);
    });

    it("Authentication failed - wrong IP", async () => {
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        return PlayService.startGame({ startGameToken: startGameToken }, "unknown")
            .should
            .eventually
            .rejectedWith(Errors.UnknownIpAddress);
    });

    it("Authentication failed - restricted country", async () => {
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        return PlayService.startGame({ startGameToken: startGameToken }, BY_IP)
            .should
            .eventually
            .rejectedWith(Errors.CountryIsRestricted);
    });

    it("Authentication failed - restricted currency", async () => {
        const service = new EntitySettingsService(brandEntity);
        await service.patch({
            restrictions: {
                countries: {
                    "CN": ["CNY"]
                }
            }
        });
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        return PlayService.startGame({ startGameToken: startGameToken }, CN_IP)
            .should
            .eventually
            .rejectedWith(Errors.CurrencyIsRestricted);
    });

    it("Start game request - authenticate through merchant", async () => {
        const testAdapter: any = {
            validate: function (): string[] {
                return [];
            },
            getGameTokenInfo: function (merchant, tokenData, currency) {
                return {
                    gameTokenData: {
                        desc: "Custom merchant game token",
                        playerCode: "SOME_CUSTOMER",
                        merchantSessionId: "SOME_SESSION_ID",
                        merchantType: "test",
                        merchantCode: "merch_cooool",
                        currency: currency
                    }
                };
            },
            getBalances: () => {
                return { "USD": { "main": 100 } };
            }
        };

        await register("test", testAdapter as AnyMerchantAdapter);

        await (new EntitySettingsService(merchBrandEntity)).patch({
            autoPlaySettings: [
                { label: "5", value: 5 },
                { label: "until feature", value: 0, isUntilFeature: true }
            ]
        });

        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: merchBrandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: "SOME_CUSTOMER",
            merchantSessionId: "SOME_SESSION_ID",
            merchantType: "test",
            merchantCode: "merch",
            currency: "USD",
            country: "US",
            language: "EN",
            playmode: PlayMode.REAL
        } as StartGameTokenData);

        const response = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);

        const gameTokenData = jwt.decode(response.gameToken) as GameTokenData;

        expect(gameTokenData).contain({
            "desc": "Custom merchant game token",
            "merchantCode": "merch_cooool",
            "merchantSessionId": "SOME_SESSION_ID",
            "merchantType": "test",
            "playerCode": "SOME_CUSTOMER",
        });

        expect(response.balance).deep.equal({ main: 100 });
        expect(response.player).contain({
            "code": "SOME_CUSTOMER",
            "country": "US",
            "currency": "USD",
            "language": "EN"
        });

        expect(response.region).to.equal(config.region);

        expect(response.logoutOptions).deep.equal({
            type: GameLogoutType.ALL,
            maxSessionTimeout: 5,
            maxRetryAttempts: 10
        });
        expect(response.settings.autoSpinsOptions).deep.equal([
            { label: "5", value: 5 },
            { label: "until feature", value: 0, isUntilFeature: true }
        ]);
    });

    it("Gets balance of merchant player", async () => {
        const testAdapter: any = {
            getBalances: () => {
                return { "USD": { "main": 100 } };
            },
        };

        await register("test", testAdapter as AnyMerchantAdapter);

        const gameToken: MerchantGameTokenData = {
            gameCode: "GAME001",
            brandId: merchBrandEntity.id,
            playerCode: "SOME_CUSTOMER",
            currency: "USD",
            merchantType: "test",
            merchantCode: "merch",
            isPromoInternal: false
        };

        const token = await TokenUtils.generateGameToken(gameToken);

        await PlayerGameSessionService.create(gameToken.brandId, gameToken.playerCode, gameToken.gameCode, {});

        const balances = await PlayService.getPlayerBalances(token);
        expect(balances).deep.equal({ "USD": { "main": 100 } });

        const balance = await PlayService.getGameBalance(token);
        expect(balance).deep.equal({ "main": 100 });
    });

    it("Commit game payment of merchant player", async () => {
        const gameToken: MerchantGameTokenData = {
            gameCode: "GAME001",
            brandId: merchBrandEntity.id,
            playerCode: "SOME_CUSTOMER",
            currency: "USD",
            merchantType: "test",
            merchantCode: "merch",
            isPromoInternal: false
        };

        const token = await TokenUtils.generateGameToken(gameToken);
        await PlayerGameSessionService.create(gameToken.brandId, gameToken.playerCode, "GAME001", {});
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);

        const testAdapter: any = {
            commitPayment: (m, t, request) => {
                expect(request).to.deep.include({
                    "bet": 50,
                    "win": 150,
                    "gameToken": token,
                    "roundId": "1000",
                    "transactionId": trxId
                });
                return { "main": 200 };
            },
        };

        await register("test", testAdapter as AnyMerchantAdapter);

        const balance = await PlayService.commitGamePayment({
            gameToken: token,
            transactionId: trxId,
            roundId: "1000",
            gameSessionId: "1",
            bet: 50,
            win: 150,
            ts: new Date().toISOString()
        });
        expect(balance).deep.equal({ "main": 200, extraData: { roundPID: PUBLIC_ID } });
    });

    it("LoginGame/LogoutGame", async () => {
        const gameToken: MerchantGameTokenData = {
            gameCode: "GAME001",
            brandId: merchBrandEntity.id,
            playerCode: "SOME_CUSTOMER",
            currency: "USD",
            merchantType: "test",
            merchantCode: "merch",
            isPromoInternal: false
        };

        const token = await TokenUtils.generateGameToken(gameToken);
        await PlayerGameSessionService.create(gameToken.brandId, gameToken.playerCode, "GAME001", {});
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);

        const testAdapter: any = {
            validate: function (): string[] {
                return [];
            },
            commitPayment: (m, t, request) => {
                expect(request).to.deep.equal({
                    "bet": 50,
                    "win": 150,
                    "gameToken": token,
                    "roundId": "1000",
                    "transactionId": trxId
                });
                return { "main": 200 };
            },
            loginGame: stub(),
            logoutGame: stub(),
        };

        await register("test", testAdapter as AnyMerchantAdapter);

        testAdapter.loginGame.returns({
            requireLogin: true
        });
        testAdapter.logoutGame.returns(undefined);

        expect(await PlayService.loginGame(
            {
                roundId: 1,
                roundPID: "round_pid",
                logoutId: "some_logout_id",
                gameContextId: "gameContext_id",
                gameToken: token
            })).deep.equal({ requireLogin: true });

        expect((testAdapter.loginGame as SinonStub).args[0][2]).deep.equal({
            roundId: 1,
            roundPID: "round_pid",
            logoutId: "some_logout_id",
            gameContextId: "gameContext_id",
            gameToken: token,
        });

        await PlayService.logoutGame(
            {
                roundId: 1,
                roundPID: "round_pid",
                gameContextId: "gameContext_id",
                logoutId: "some_logout_id",
                gameToken: token,
                state: GameLogoutGameState.FINISHED
            });

        expect((testAdapter.logoutGame as SinonStub).args[0][2]).deep.equal(
            {
                roundId: 1,
                roundPID: "round_pid",
                logoutId: "some_logout_id",
                gameContextId: "gameContext_id",
                gameToken: token,
                state: GameLogoutGameState.FINISHED
            });
    });

    it("Commit game payment of merchant player with 0 bet", async () => {
        const gameToken: MerchantGameTokenData = {
            gameCode: "GAME001",
            brandId: merchBrandEntity.id,
            playerCode: "SOME_CUSTOMER",
            currency: "USD",
            merchantType: "test",
            merchantCode: "merch",
            isPromoInternal: false
        };

        const token = await TokenUtils.generateGameToken(gameToken);
        await PlayerGameSessionService.create(gameToken.brandId, gameToken.playerCode, "GAME001", {});
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);

        const testAdapter: any = {
            validate: function (): string[] {
                return [];
            },
            commitPayment: (m, t, request) => {
                expect(request).to.deep.include({
                    "bet": 0,
                    "win": 150,
                    "gameToken": token,
                    "roundId": "1000",
                    "transactionId": trxId
                });
                return { "main": 200 };
            },
        };

        await register("test", testAdapter as AnyMerchantAdapter);

        const balance = await PlayService.commitGamePayment({
            gameToken: token,
            transactionId: trxId,
            roundId: "1000",
            gameSessionId: "1",
            bet: 0,
            win: 150,
            ts: new Date().toISOString()
        });
        expect(balance).deep.equal({ "main": 200, extraData: { roundPID: PUBLIC_ID } });
    });

    it("Commit game payment of merchant player with 0 win", async () => {
        const gameToken: MerchantGameTokenData = {
            gameCode: "GAME001",
            brandId: merchBrandEntity.id,
            playerCode: "SOME_CUSTOMER",
            currency: "USD",
            merchantType: "test",
            merchantCode: "merch",
            isPromoInternal: false
        };

        const token = await TokenUtils.generateGameToken(gameToken);
        await PlayerGameSessionService.create(gameToken.brandId, gameToken.playerCode, "GAME001", {});
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);

        const testAdapter: any = {
            validate: function (): string[] {
                return [];
            },
            commitPayment: (m, t, request) => {
                expect(request).to.deep.include({
                    "bet": 50,
                    "win": 0,
                    "gameToken": token,
                    "roundId": "1000",
                    "transactionId": trxId
                });
                return { "main": 200 };
            },
        };

        await register("test", testAdapter as AnyMerchantAdapter);

        const balance = await PlayService.commitGamePayment({
            gameToken: token,
            transactionId: trxId,
            roundId: "1000",
            gameSessionId: "1",
            bet: 50,
            win: 0,
            ts: new Date().toISOString()
        });
        expect(balance).deep.equal({ "main": 200, extraData: { roundPID: PUBLIC_ID } });
    });

    it("Commit game payment of merchant player with 0 bet and undefined win", async () => {
        const gameToken: MerchantGameTokenData = {
            gameCode: "GAME001",
            brandId: merchBrandEntity.id,
            playerCode: "SOME_CUSTOMER",
            currency: "USD",
            merchantType: "test",
            merchantCode: "merch",
            isPromoInternal: false
        };

        const token = await TokenUtils.generateGameToken(gameToken);
        await PlayerGameSessionService.create(gameToken.brandId, gameToken.playerCode, "GAME001", {});
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);

        const testAdapter: any = {
            validate: function (): string[] {
                return [];
            },
            commitBetPayment: (m, t, request) => {
                expect(request).to.deep.include({
                    "bet": 0,
                    "gameToken": token,
                    "roundId": "1000",
                    "transactionId": trxId
                });
                return { "main": 200 };
            },
        };

        await register("test", testAdapter as AnyMerchantAdapter);

        const balance = await PlayService.commitGamePayment({
            gameToken: token,
            transactionId: trxId,
            roundId: "1000",
            gameSessionId: "1",
            bet: 0,
            ts: new Date().toISOString()
        });
        expect(balance).deep.equal({ "main": 200, extraData: { roundPID: PUBLIC_ID } });
    });

    it("Commit game payment of merchant player with 0 win and undefined bet", async () => {
        const gameToken: MerchantGameTokenData = {
            gameCode: "GAME001",
            brandId: merchBrandEntity.id,
            playerCode: "SOME_CUSTOMER",
            currency: "USD",
            merchantType: "test",
            merchantCode: "merch",
            isPromoInternal: false
        };

        const token = await TokenUtils.generateGameToken(gameToken);
        await PlayerGameSessionService.create(gameToken.brandId, gameToken.playerCode, "GAME001", {});
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);

        const testAdapter: any = {
            validate: function (): string[] {
                return [];
            },
            commitWinPayment: (m, t, request) => {
                expect(request).to.deep.include({
                    "win": 0,
                    "gameToken": token,
                    "roundId": "1000",
                    "transactionId": trxId
                });
                return { "main": 200 };
            },
        };

        await register("test", testAdapter as AnyMerchantAdapter);

        const balance = await PlayService.commitGamePayment({
            gameToken: token,
            transactionId: trxId,
            roundId: "1000",
            gameSessionId: "1",
            win: 0,
            ts: new Date().toISOString()
        });
        expect(balance).deep.equal({ "main": 200, extraData: { roundPID: PUBLIC_ID } });
    });

    it("Commit game bet payment of merchant player", async () => {
        const gameToken: MerchantGameTokenData = {
            gameCode: "GAME001",
            brandId: merchBrandEntity.id,
            playerCode: "SOME_CUSTOMER",
            currency: "USD",
            merchantType: "test",
            merchantCode: "merch",
            isPromoInternal: false
        };

        const token = await TokenUtils.generateGameToken(gameToken);
        await PlayerGameSessionService.create(gameToken.brandId, gameToken.playerCode, "GAME001", {});
        const trxId = await WalletFacade.generateTrxId();

        const testAdapter: any = {
            validate: function (): string[] {
                return [];
            },
            commitBetPayment: (m, t, request) => {
                expect(request).to.deep.equal({
                    "amount": 50,
                    "bet": 50,
                    "gameToken": token,
                    "roundId": "1000",
                    "roundPID": PUBLIC_ID,
                    "gameSessionId": "1",
                    "transactionId": trxId,
                    "ts": request.ts
                });
                return { "main": 200 };
            },
        };

        await register("test", testAdapter as AnyMerchantAdapter);

        const balance = await PlayService.commitBetPayment({
            gameToken: token,
            transactionId: trxId,
            roundId: "1000",
            gameSessionId: "1",
            amount: 50,
            ts: new Date().toISOString()
        });
        expect(balance).deep.equal({ "main": 200, extraData: { roundPID: PUBLIC_ID } });
    });

    it("Commit game bet payment of merchant player with 0 amount", async () => {
        const gameToken: MerchantGameTokenData = {
            gameCode: "GAME001",
            brandId: merchBrandEntity.id,
            playerCode: "SOME_CUSTOMER",
            currency: "USD",
            merchantType: "test",
            merchantCode: "merch",
            isPromoInternal: false
        };

        const token = await TokenUtils.generateGameToken(gameToken);
        await PlayerGameSessionService.create(gameToken.brandId, gameToken.playerCode, "GAME001", {});
        const trxId = await WalletFacade.generateTrxId();

        const testAdapter: any = {
            validate: function (): string[] {
                return [];
            },
            commitBetPayment: (m, t, request) => {
                expect(request).to.deep.equal({
                    "amount": 0,
                    "bet": 0,
                    "gameToken": token,
                    "roundId": "1000",
                    "roundPID": PUBLIC_ID,
                    "gameSessionId": "1",
                    "transactionId": trxId,
                    "ts": request.ts
                });
                return { "main": 200 };
            },
        };

        await register("test", testAdapter as AnyMerchantAdapter);

        const balance = await PlayService.commitBetPayment({
            gameToken: token,
            transactionId: trxId,
            roundId: "1000",
            roundPID: PUBLIC_ID,
            gameSessionId: "1",
            amount: 0,
            ts: new Date().toISOString()
        });
        expect(balance).deep.equal({ "main": 200, extraData: { roundPID: PUBLIC_ID } });
    });

    it("Commit game win payment of merchant player", async () => {
        const gameToken: MerchantGameTokenData = {
            gameCode: "GAME001",
            brandId: merchBrandEntity.id,
            playerCode: "SOME_CUSTOMER",
            currency: "USD",
            merchantType: "test",
            merchantCode: "merch",
            isPromoInternal: false
        };

        const token = await TokenUtils.generateGameToken(gameToken);
        await PlayerGameSessionService.create(gameToken.brandId, gameToken.playerCode, "GAME001", {});
        const trxId = await WalletFacade.generateTrxId();

        const testAdapter: any = {
            validate: function (): string[] {
                return [];
            },
            commitWinPayment: (m, t, request) => {
                expect(request).to.deep.equal({
                    "win": 150,
                    "amount": 150,
                    "gameToken": token,
                    "roundId": "1000",
                    "roundPID": PUBLIC_ID,
                    "gameSessionId": "1",
                    "transactionId": trxId,
                    "ts": request.ts
                });
                return { "main": 200 };
            },
        };

        await register("test", testAdapter as AnyMerchantAdapter);

        const balance = await PlayService.commitWinPayment({
            gameToken: token,
            transactionId: trxId,
            roundId: "1000",
            roundPID: PUBLIC_ID,
            gameSessionId: "1",
            amount: 150,
            ts: new Date().toISOString()
        });
        expect(balance).deep.equal({ "main": 200, extraData: { roundPID: PUBLIC_ID } });
    });

    it("Commit finalization bet payment of merchant player with 0 amount", async () => {
        const gameToken: MerchantGameTokenData = {
            gameCode: "GAME001",
            brandId: merchBrandEntity.id,
            playerCode: "SOME_CUSTOMER",
            currency: "USD",
            merchantType: "test",
            merchantCode: "merch",
            isPromoInternal: false
        };

        const token = await TokenUtils.generateGameToken(gameToken);
        await PlayerGameSessionService.create(gameToken.brandId, gameToken.playerCode, "GAME001", {});
        const trxId = await WalletFacade.generateTrxId();

        const balance = await PlayService.commitBetPayment({
            gameToken: token,
            transactionId: trxId,
            roundId: "1000",
            roundPID: PUBLIC_ID,
            gameSessionId: "1",
            finalizationType: "roundStatistics",
            amount: 0,
            ts: new Date().toISOString()
        } as any);
        expect(balance).deep.equal({ "main": 0, extraData: { roundPID: PUBLIC_ID } });
    });

    it("Commit finalization win payment of merchant player", async () => {
        const gameToken: MerchantGameTokenData = {
            gameCode: "GAME001",
            brandId: merchBrandEntity.id,
            playerCode: "SOME_CUSTOMER",
            currency: "USD",
            merchantType: "test",
            merchantCode: "merch",
            isPromoInternal: false
        };

        const token = await TokenUtils.generateGameToken(gameToken);
        await PlayerGameSessionService.create(gameToken.brandId, gameToken.playerCode, "GAME001", {});
        const trxId = await WalletFacade.generateTrxId();

        const balance = await PlayService.commitWinPayment({
            gameToken: token,
            transactionId: trxId,
            roundId: "1000",
            roundPID: PUBLIC_ID,
            gameSessionId: "1",
            amount: 150,
            finalizationType: "manualPayments",
            ts: new Date().toISOString()
        } as any);
        expect(balance).deep.equal({ "main": 0, extraData: { roundPID: PUBLIC_ID } });
    });

    it("Commit game payment of merchant player with 0 amount", async () => {
        const gameToken: MerchantGameTokenData = {
            gameCode: "GAME001",
            brandId: merchBrandEntity.id,
            playerCode: "SOME_CUSTOMER",
            currency: "USD",
            merchantType: "test",
            merchantCode: "merch",
            isPromoInternal: false
        };

        const token = await TokenUtils.generateGameToken(gameToken);
        await PlayerGameSessionService.create(gameToken.brandId, gameToken.playerCode, "GAME001", {});
        const trxId = await WalletFacade.generateTrxId();

        const testAdapter: any = {
            validate: function (): string[] {
                return [];
            },
            commitWinPayment: (m, t, request) => {
                expect(request).to.deep.equal({
                    "win": 0,
                    "amount": 0,
                    "gameToken": token,
                    "roundId": "1000",
                    "roundPID": PUBLIC_ID,
                    "gameSessionId": "1",
                    "transactionId": trxId,
                    "ts": request.ts
                });
                return { "main": 200 };
            },
        };

        await register("test", testAdapter as AnyMerchantAdapter);

        const balance = await PlayService.commitWinPayment({
            gameToken: token,
            transactionId: trxId,
            gameSessionId: "1",
            roundId: "1000",
            roundPID: PUBLIC_ID,
            amount: 0,
            ts: new Date().toISOString()
        });
        expect(balance).deep.equal({ "main": 200, extraData: { roundPID: PUBLIC_ID } });
    });

    it("Commit game payment - failed, no session", async () => {
        const gameToken: MerchantGameTokenData = {
            gameCode: "GAME001",
            brandId: merchBrandEntity.id,
            playerCode: "SOME_CUSTOMER",
            currency: "USD",
            merchantType: "test",
            merchantCode: "merch",
            isPromoInternal: false
        };

        const token = await TokenUtils.generateGameToken(gameToken);
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);

        const testAdapter: any = {
            validate: function (): string[] {
                return [];
            },
            commitPayment: (m, t, request) => {
                expect(request).to.deep.equal({
                    "bet": 50,
                    "win": 150,
                    "gameToken": token,
                    "roundId": "1000",
                    "transactionId": trxId
                });
                return { "main": 200 };
            },
        };

        await register("test", testAdapter as AnyMerchantAdapter);

        return PlayService.commitGamePayment({
            gameToken: token,
            transactionId: trxId,
            roundId: "1000",
            gameSessionId: "1",
            bet: 50,
            win: 150,
            ts: new Date().toISOString()
        }).should.eventually.rejectedWith(PlayerSessionErrors.PlayerSessionExpiredError);
    });

    it("Commit PaymentOperation  - negative, session killed", async () => {
        const token = await TokenUtils.generateGameToken(authTokenForBrand);
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);
        const actions: GamePaymentAction[] = [
            {
                action: "credit",
                attribute: "balance",
                amount: 1000.00
            }
        ];
        const request: GamePaymentOperation = {
            roundId: "1000",
            gameSessionId: "1",
            gameToken: token,
            transactionId: trxId,
            actions: actions,
            ts: new Date().toISOString()
        };
        const balance1 = await PlayService.getGameBalance(token);
        expect(balance1).deep.equal({ "main": 1000 });

        await createPlayerSessionFacade().kill(
            { brandId: authTokenForBrand.brandId, playerCode: authTokenForBrand.playerCode, reason: "some reason" }
        );
        return PlayService.commitPaymentOperation(request, ONLY_BALANCE_FILTER)
            .should.eventually.rejectedWith(PlayerSessionErrors.PlayerSessionExpiredError);
    });

    it("Commit PaymentOperation  - offlineRetry, restore session", async () => {
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        const response = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);

        const balance = await PlayService.getGameBalance(response.gameToken);

        expect(balance).deep.equal({ main: 1000 });

        const balances = await PlayService.getPlayerBalances(response.gameToken);
        expect(balances).deep.equal({ USD: { main: 1000 } });

        const transactionId = await PlayService.generateTransactionId(response.gameToken);
        const trxId = await WalletFacade.parseTransactionId(transactionId);
        await createPlayerSessionFacade().kill(
            { brandId: authTokenForBrand.brandId, playerCode: authTokenForBrand.playerCode, reason: "some reason" }
        );

        await expect(PlayService.commitGamePayment({
            gameToken: response.gameToken,
            transactionId: trxId,
            roundId: "1000",
            gameSessionId: "1",
            bet: 100,
            win: 200,
            ts: new Date().toISOString()
        })).to.be.rejectedWith(PlayerSessionErrors.PlayerSessionExpiredError);

        const result = await PlayService.commitGamePayment({
            gameToken: response.gameToken,
            transactionId: trxId,
            roundId: "1000",
            gameSessionId: "1",
            offlineRetry: true,
            bet: 100,
            win: 200,
            ts: new Date().toISOString()
        });
        expect(result)
            .deep
            .equal({ main: 1100, previousValue: 1000, extraData: { roundPID: PUBLIC_ID } });
    });

    it("Commit PaymentOperation for balance only", async () => {
        const token = await TokenUtils.generateGameToken(authTokenForBrand);
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);
        const actions: GamePaymentAction[] = [
            {
                action: "credit",
                attribute: "balance",
                amount: 1000.00
            }
        ];
        const request: GamePaymentOperation = {
            roundId: "1000",
            gameSessionId: "1",
            gameToken: token,
            transactionId: trxId,
            actions: actions,
            ts: new Date().toISOString()
        };
        const balance1 = await PlayService.getGameBalance(token);
        expect(balance1).deep.equal({ "main": 1000 });

        const balance2 = await PlayService.commitPaymentOperation(request, ONLY_BALANCE_FILTER);
        expect(balance2)
            .deep
            .equal({ "main": 2000, "previousValue": 1000, extraData: { roundPID: PUBLIC_ID } });
    });

    it("Commit PaymentOperation for balance only", async () => {
        const token = await TokenUtils.generateGameToken(authTokenForBrand);
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);
        const actions: GamePaymentAction[] = [
            {
                action: "credit",
                attribute: "balance",
                amount: 1000.00
            }
        ];
        const request: GamePaymentOperation = {
            roundId: "1000",
            gameSessionId: "1",
            gameToken: token,
            transactionId: trxId,
            actions: actions,
            ts: new Date().toISOString()
        };
        const balance1 = await PlayService.getGameBalance(token);
        expect(balance1).deep.equal({ "main": 1000 });

        const balance2 = await PlayService.commitPaymentOperation(request, ONLY_BALANCE_FILTER);
        expect(balance2)
            .deep
            .equal({ "main": 2000, "previousValue": 1000, extraData: { roundPID: PUBLIC_ID } });
    });

    it("Commit PaymentOperation for balance only with 'bet' and 'win' ", async () => {
        const token = await TokenUtils.generateGameToken(authTokenForBrand);
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);
        const betRequest: GamePaymentOperation = {
            roundId: "1000",
            gameSessionId: "1",
            operation: "bet",
            gameToken: token,
            transactionId: trxId,
            actions: [
                {
                    action: "debit",
                    attribute: "balance",
                    amount: 10.00
                }
            ],
            ts: new Date().toISOString()
        };

        const winRequest: GamePaymentOperation = {
            roundId: "1000",
            gameSessionId: "1",
            operation: "win",
            gameToken: token,
            transactionId: trxId,
            actions: [
                {
                    action: "credit",
                    attribute: "balance",
                    amount: 1000.00
                }
            ],
            ts: new Date().toISOString()
        };

        const balance1 = await PlayService.getGameBalance(token);
        expect(balance1).deep.equal({ "main": 1000 });

        const balance2 = await PlayService.commitPaymentOperation(betRequest, ONLY_BALANCE_FILTER);
        expect(balance2)
            .deep
            .equal({ "main": 990, "previousValue": 1000, extraData: { roundPID: PUBLIC_ID } });

        const balance3 = await PlayService.commitPaymentOperation(winRequest, ONLY_BALANCE_FILTER);
        expect(balance3)
            .deep
            .equal({ "main": 1990, "previousValue": 990, extraData: { roundPID: PUBLIC_ID } });
    });

    it("Commit PaymentOperation for balance only of brand with domain", async () => {
        const token = await TokenUtils.generateGameToken(authTokenForBrandWithDomain);
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);
        const actions: GamePaymentAction[] = [
            {
                action: "credit",
                attribute: "balance",
                amount: 1000.00
            }
        ];
        const request: GamePaymentOperation = {
            roundId: "1000",
            gameSessionId: "1",
            gameToken: token,
            transactionId: trxId,
            actions: actions,
            ts: new Date().toISOString()
        };
        const balance1 = await PlayService.getGameBalance(token);
        expect(balance1).deep.equal({ "main": 1000 });

        const balance2 = await PlayService.commitPaymentOperation(request, ONLY_BALANCE_FILTER);
        expect(balance2)
            .deep
            .equal({ "main": 2000, "previousValue": 1000, extraData: { roundPID: PUBLIC_ID } });
    });

    it("Commit PaymentOperation for balance debit and credit", async () => {
        const token = await TokenUtils.generateGameToken(authTokenForBrand);
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);
        const actions: GamePaymentAction[] = [
            {
                action: "debit",
                attribute: "balance",
                amount: 500.00
            },
            {
                action: "credit",
                attribute: "balance",
                amount: 600.00
            }
        ];
        const request: GamePaymentOperation = {
            roundId: "1000",
            gameSessionId: "1",
            gameToken: token,
            transactionId: trxId,
            actions: actions,
            ts: new Date().toISOString()
        };
        const balance1 = await PlayService.getGameBalance(token);
        expect(balance1).deep.equal({ "main": 1000 });
        // 1000 - 500 + 600
        const balance2 = await PlayService.commitPaymentOperation(request, ONLY_BALANCE_FILTER);
        expect(balance2)
            .deep
            .equal({ "main": 1100, "previousValue": 1000, extraData: { roundPID: PUBLIC_ID } });
    });

    it("Commit PaymentOperation for balance debit and credit of brand with domain", async () => {
        const token = await TokenUtils.generateGameToken(authTokenForBrandWithDomain);
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);
        const actions: GamePaymentAction[] = [
            {
                action: "debit",
                attribute: "balance",
                amount: 500.00
            },
            {
                action: "credit",
                attribute: "balance",
                amount: 600.00
            }
        ];
        const request: GamePaymentOperation = {
            roundId: "1000",
            gameSessionId: "1",
            gameToken: token,
            transactionId: trxId,
            actions: actions,
            ts: new Date().toISOString()
        };
        const balance1 = await PlayService.getGameBalance(token);
        expect(balance1).deep.equal({ "main": 1000 });
        // 1000 - 500 + 600
        const balance2 = await PlayService.commitPaymentOperation(request, ONLY_BALANCE_FILTER);
        expect(balance2)
            .deep
            .equal({ "main": 1100, "previousValue": 1000, extraData: { roundPID: PUBLIC_ID } });
    });

    it("Commit PaymentOperation with multiple debit and credit", async () => {
        const token = await TokenUtils.generateGameToken(authTokenForBrand);
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);
        const actions: GamePaymentAction[] = [
            {
                action: "debit",
                attribute: "balance",
                amount: 500.00
            },
            {
                action: "credit",
                attribute: "balance",
                amount: 600.00
            },
            {
                action: "debit",
                attribute: "balance",
                amount: 700.00
            },
            {
                action: "credit",
                attribute: "balance",
                amount: 800.00
            }
        ];
        const request: GamePaymentOperation = {
            roundId: "1000",
            gameSessionId: "1",
            gameToken: token,
            transactionId: trxId,
            actions: actions,
            ts: new Date().toISOString()
        };
        const balance1 = await PlayService.getGameBalance(token);
        expect(balance1).deep.equal({ "main": 1000 });
        // 1000 - 500 + 600
        const balance2 = await PlayService.commitPaymentOperation(request, ONLY_BALANCE_FILTER);
        expect(balance2)
            .deep
            .equal({ "main": 1200, "previousValue": 1000, extraData: { roundPID: PUBLIC_ID } });
    });

    it("Commit PaymentOperation with zero changes, prevValue should be equal to main", async () => {
        const token = await TokenUtils.generateGameToken(authTokenForBrand);
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);
        const actions: GamePaymentAction[] = [
            {
                action: "debit",
                attribute: "balance",
                amount: 0
            },
            {
                action: "credit",
                attribute: "balance",
                amount: 0
            }
        ];
        const request: GamePaymentOperation = {
            roundId: "1000",
            gameSessionId: "1",
            gameToken: token,
            transactionId: trxId,
            actions: actions,
            ts: new Date().toISOString()
        };
        const balance1 = await PlayService.getGameBalance(token);
        expect(balance1).deep.equal({ "main": 1000 });
        // 1000 - 500 + 600
        const balance2 = await PlayService.commitPaymentOperation(request, ONLY_BALANCE_FILTER);
        expect(balance2)
            .deep
            .equal({ "main": 1000, "previousValue": 1000, extraData: { roundPID: PUBLIC_ID } });
    });

    it("Commit PaymentOperation for balance credit and debit and valid retention attribute", async () => {
        const token = await TokenUtils.generateGameToken(authTokenForBrand);
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);
        const actions: GamePaymentAction[] = [
            {
                action: "debit",
                attribute: "balance",
                amount: 500.00
            },
            {
                action: "credit",
                attribute: "balance",
                amount: 600.00
            },
            {
                action: "credit",
                attribute: "stars",
                amount: 1
            }
        ];
        const request: GamePaymentOperation = {
            roundId: "1000",
            gameSessionId: "1",
            gameToken: token,
            transactionId: trxId,
            actions: actions,
            ts: new Date().toISOString()
        };
        const balance1 = await PlayService.getGameBalance(token);
        expect(balance1).deep.equal({ "main": 1000 });
        // 1000 - 500 + 600
        const balance2 = await PlayService.commitPaymentOperation(request, BALANCE_WITH_RT_FILTER);
        expect(balance2)
            .deep
            .equal({
                "main": 1100,
                "previousValue": 1000,
                "extraBalances": { "stars": 1 },
                extraData: { roundPID: PUBLIC_ID }
            });
    });

    it("Commit PaymentOperation full balance with retention should be returned", async () => {
        const token1 = await TokenUtils.generateGameToken(authTokenForBrand);
        const transactionId1 = await PlayService.generateTransactionId(token1);
        const trxId1 = await WalletFacade.parseTransactionId(transactionId1);
        const actions1: GamePaymentAction[] = [
            {
                action: "debit",
                attribute: "balance",
                amount: 500.00
            },
            {
                action: "credit",
                attribute: "balance",
                amount: 600.00
            },
            {
                action: "credit",
                attribute: "stars",
                amount: 1
            }
        ];
        const request: GamePaymentOperation = {
            roundId: "1000",
            gameSessionId: "1",
            gameToken: token1,
            transactionId: trxId1,
            actions: actions1,
            ts: new Date().toISOString()
        };
        const balance1 = await PlayService.commitPaymentOperation(request, BALANCE_WITH_RT_FILTER);
        expect(balance1)
            .deep
            .equal({
                "main": 1100,
                "previousValue": 1000,
                "extraBalances": { "stars": 1 },
                extraData: { roundPID: PUBLIC_ID }
            });

        const actions2: GamePaymentAction[] = [
            {
                action: "credit",
                attribute: "levelPrize",
                amount: 1
            }
        ];

        const token2 = await TokenUtils.generateGameToken(authTokenForBrand);
        const transactionId2 = await PlayService.generateTransactionId(token2);
        const trxId2 = await WalletFacade.parseTransactionId(transactionId2);
        const request2: GamePaymentOperation = {
            roundId: "1000",
            gameSessionId: "1",
            gameToken: token2,
            transactionId: trxId2,
            actions: actions2,
            ts: new Date().toISOString()
        };

        const balance2 = await PlayService.commitPaymentOperation(request2, BALANCE_WITH_RT_FILTER);
        expect(balance2)
            .deep
            .equal({
                "main": 1100,
                "previousValue": 1100,
                "extraBalances": { "stars": 1, "levelPrize": 1 },
                extraData: { roundPID: PUBLIC_ID }
            });
    });

    it("Commit PaymentOperation for retention only", async () => {
        const token = await TokenUtils.generateGameToken(authTokenForBrand);
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);
        const actions: GamePaymentAction[] = [
            {
                action: "credit",
                attribute: "xp",
                amount: 123
            },
            {
                action: "credit",
                attribute: "levelPrize",
                amount: 231
            },
            {
                action: "credit",
                attribute: "stars",
                amount: 1
            },
            {
                action: "credit",
                attribute: "vipContribution",
                amount: 987
            }
        ];

        const request: GamePaymentOperation = {
            roundId: "1000",
            gameSessionId: "1",
            gameToken: token,
            transactionId: trxId,
            actions: actions,
            ts: new Date().toISOString()
        };

        const balance = await PlayService.commitPaymentOperation(request, ONLY_RT_FILTER);
        expect(balance).deep.equal({
            "extraBalances": {
                "levelPrize": 231,
                "stars": 1,
                "xp": 123,
                "vipContribution": 987
            },
            "main": undefined,
            extraData: { roundPID: PUBLIC_ID }
        });
    });

    it("Commit PaymentOperation for invalid stats attribute - negative", async () => {
        const token = await TokenUtils.generateGameToken(authTokenForBrand);
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);
        const actions: GamePaymentAction[] = [
            {
                action: "debit",
                attribute: "balance",
                amount: 500.00
            },
            {
                action: "credit",
                attribute: "balance",
                amount: 600.00
            },
            {
                action: "credit",
                attribute: "invalid_attribute",
                amount: 1
            }
        ];
        const request: GamePaymentOperation = {
            roundId: "1000",
            gameSessionId: "1",
            gameToken: token,
            transactionId: trxId,
            actions: actions,
            ts: new Date().toISOString()
        };
        await PlayService.commitPaymentOperation(request, BALANCE_WITH_RT_FILTER)
            .should.be.rejectedWith(PlayServiceErrors.InvalidPaymentActionAttributeError);
    });

    it("Commit PaymentOperation for invalid action - negative", async () => {
        const token = await TokenUtils.generateGameToken(authTokenForBrand);
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);
        const actions: GamePaymentAction[] = [
            {
                action: "invalid_action" as PaymentActionType,
                attribute: "balance",
                amount: 1
            }
        ];
        const request: GamePaymentOperation = {
            roundId: "1000",
            gameSessionId: "1",
            gameToken: token,
            transactionId: trxId,
            actions: actions,
            ts: new Date().toISOString()
        };
        await PlayService.commitPaymentOperation(request, BALANCE_WITH_RT_FILTER)
            .should.be.rejectedWith(PlayServiceErrors.InvalidPaymentActionTypeError);
    });

    it("Commit PaymentOperation for negative value - negative", async () => {
        const token = await TokenUtils.generateGameToken(authTokenForBrand);
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);
        const actions: GamePaymentAction[] = [
            {
                action: "credit",
                attribute: "balance",
                amount: -1
            }
        ];
        const request: GamePaymentOperation = {
            roundId: "1000",
            gameSessionId: "1",
            gameToken: token,
            transactionId: trxId,
            actions: actions,
            ts: new Date().toISOString()
        };
        await PlayService.commitPaymentOperation(request, BALANCE_WITH_RT_FILTER)
            .should.be.rejectedWith(PlayServiceErrors.InvalidPaymentActionValueError);
    });

    it("Commit PaymentOperation for null value - negative", async () => {
        const token = await TokenUtils.generateGameToken(authTokenForBrand);
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);
        const actions: GamePaymentAction[] = [
            {
                action: "credit",
                attribute: "balance",
                amount: null
            }
        ];
        const request: GamePaymentOperation = {
            roundId: "1000",
            gameSessionId: "1",
            gameToken: token,
            transactionId: trxId,
            actions: actions,
            ts: new Date().toISOString()
        };
        await PlayService.commitPaymentOperation(request, BALANCE_WITH_RT_FILTER)
            .should.be.rejectedWith(PlayServiceErrors.InvalidPaymentActionValueError);
    });

    it("Commit PaymentOperation with restricted filter and invalid attribute for this filter - negative",
        async () => {
            const token = await TokenUtils.generateGameToken(authTokenForBrand);
            const transactionId = await PlayService.generateTransactionId(token);
            const trxId = await WalletFacade.parseTransactionId(transactionId);
            const actions: GamePaymentAction[] = [
                {
                    action: "credit",
                    attribute: "balance",
                    amount: 1000.00
                },
                {
                    action: "credit",
                    attribute: "stars",
                    amount: 1000.00
                },
                {
                    action: "credit",
                    attribute: "xp",
                    amount: 1000.00
                }
            ];

            const request: GamePaymentOperation = {
                roundId: "1000",
                gameSessionId: "1",
                gameToken: token,
                transactionId: trxId,
                actions: actions,
                ts: new Date().toISOString()
            };

            const BALANCE_AND_STARS_FILTER: AccountPropertiesFilter[] = [
                {
                    account: "main",
                    properties: [new AccountProperty("balance")]
                },
                {
                    account: "rt",
                    properties: [new AccountProperty("stars")]
                }
            ];
            await PlayService.commitPaymentOperation(request, BALANCE_AND_STARS_FILTER)
                .should.be.rejectedWith(PlayServiceErrors.InvalidPaymentActionAttributeError);
        });

    it("Commit PaymentOperation return balance with filter",
        async () => {
            const token = await TokenUtils.generateGameToken(authTokenForBrand);
            const transactionId = await PlayService.generateTransactionId(token);
            const trxId = await WalletFacade.parseTransactionId(transactionId);
            const actions: GamePaymentAction[] = [
                {
                    action: "credit",
                    attribute: "balance",
                    amount: 1000.00
                },
                {
                    action: "credit",
                    attribute: "stars",
                    amount: 1000.00
                },
                {
                    action: "credit",
                    attribute: "xp",
                    amount: 1000.00
                }
            ];

            const request: GamePaymentOperation = {
                roundId: "1000",
                gameSessionId: "1",
                gameToken: token,
                transactionId: trxId,
                actions: actions,
                ts: new Date().toISOString()
            };

            const BALANCE_AND_STARS_FILTER: AccountPropertiesFilter[] = [
                {
                    account: "main",
                    properties: [new AccountProperty("balance")]
                },
                {
                    account: "rt",
                    properties: [new AccountProperty("stars")]
                }
            ];
            await PlayService.commitPaymentOperation(request, BALANCE_WITH_RT_FILTER);

            const balance = await PlayService.getGameBalance(token, BALANCE_AND_STARS_FILTER);
            expect(balance).deep.equal({ "main": 2000, "extraBalances": { "stars": 1000 } });
        });

    it("Commit PaymentOperation with insufficient balance for retention",
        async () => {
            const token = await TokenUtils.generateGameToken(authTokenForBrand);
            const transactionId = await PlayService.generateTransactionId(token);
            const trxId = await WalletFacade.parseTransactionId(transactionId);
            const actions: GamePaymentAction[] = [
                {
                    action: "debit",
                    attribute: "stars",
                    amount: 1
                }
            ];

            const request: GamePaymentOperation = {
                roundId: "1000",
                gameSessionId: "1",
                gameToken: token,
                transactionId: trxId,
                actions: actions,
                ts: new Date().toISOString()
            };
            await PlayService.commitPaymentOperation(request, BALANCE_WITH_RT_FILTER)
                .should.be.rejectedWith(WalletErrors.InsufficientBalanceError);
        });

    it("Doesn't make play requests in fun mode", async () => {
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.FUN
        });

        await expect(PlayService.startGame({ startGameToken: startGameToken }, US_IP))
            .to
            .be
            .rejectedWith(GameProviderErrors.OperationForbidden);
    });

    it("Refund bet", async () => {
        const gameToken: MerchantGameTokenData = {
            gameCode: "GAME001",
            brandId: merchBrandEntity.id,
            playerCode: "SOME_CUSTOMER",
            currency: "USD",
            merchantType: "test",
            merchantCode: "merch",
            isPromoInternal: false
        };

        const token = await TokenUtils.generateGameToken(gameToken);
        await PlayerGameSessionService.create(gameToken.brandId, gameToken.playerCode, "GAME001", {});
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);

        const testAdapter: any = {
            validate: function (): string[] {
                return [];
            },
            refundBetPayment: mock(),
        };

        await register("test", testAdapter as AnyMerchantAdapter);
        const balance = { main: 1000, previousValue: 500 };
        testAdapter.refundBetPayment.once().returns(Promise.resolve(balance));
        const result = await PlayService.refundBet({
            gameToken: token,
            transactionId: trxId,
            roundId: "1000",
            bet: 500,
            ts: new Date().toISOString()
        } as any);
        expect(result).deep.equal(balance);
    });

    describe("MultiPaymentOperations for Merchants", () => {
        it("Commit operation for bet and win only", async () => {

            const token = await TokenUtils.generateGameToken(authTokenForMerchant);
            const transactionId = await PlayService.generateTransactionId(token);
            const trxId = await WalletFacade.parseTransactionId(transactionId);

            const testAdapter: any = {
                validate: function (): string[] {
                    return [];
                },
                commitPayment: (m, t, request) => {
                    expect(request).to.deep.equal({
                        "actions": [
                            {
                                "action": "credit",
                                "amount": 100,
                                "attribute": "balance"
                            },
                            {
                                "action": "debit",
                                "amount": 50,
                                "attribute": "balance"
                            }
                        ],
                        "bet": 50,
                        "win": 100,
                        "gameToken": token,
                        "isJPWin": false,
                        "roundId": "1000",
                        "roundPID": PUBLIC_ID,
                        "gameSessionId": "1",
                        "transactionId": trxId,
                        "ts": request.ts
                    });
                    return Promise.resolve({ "main": 50 });
                },
                getBalances: () => {
                    return Promise.resolve({ "main": 100 });
                }
            };

            await register("test", testAdapter as AnyMerchantAdapter);
            const actions: GamePaymentAction[] = [
                {
                    action: "credit",
                    attribute: "balance",
                    amount: 100
                },
                {
                    action: "debit",
                    attribute: "balance",
                    amount: 50
                }
            ];

            const request: GamePaymentOperation = {
                roundId: "1000",
                gameSessionId: "1",
                gameToken: token,
                transactionId: trxId,
                actions: actions,
                ts: new Date().toISOString()
            };

            const balance = await PlayService.commitPaymentOperation(request, BALANCE_WITH_RT_FILTER);

            expect(balance).deep.equal({ "main": 50, extraData: { roundPID: PUBLIC_ID } });
        });

        it("Commit operation with negative bet/win", async () => {

            const token = await TokenUtils.generateGameToken(authTokenForMerchant);
            const transactionId = await PlayService.generateTransactionId(token);
            const trxId = await WalletFacade.parseTransactionId(transactionId);

            const actions: GamePaymentAction[] = [
                {
                    action: "credit",
                    attribute: "balance",
                    amount: -1
                },
                {
                    action: "debit",
                    attribute: "balance",
                    amount: -1
                }
            ];

            const request: GamePaymentOperation = {
                roundId: "1000",
                gameSessionId: "1",
                gameToken: token,
                transactionId: trxId,
                actions: actions,
                ts: new Date().toISOString()
            };

            await expect(PlayService.commitPaymentOperation(request, BALANCE_WITH_RT_FILTER))
                .to.be.rejectedWith(PlayServiceErrors.InvalidPaymentActionValueError);
        });

        it("Commit operation with null bet/win", async () => {

            const token = await TokenUtils.generateGameToken(authTokenForMerchant);
            const transactionId = await PlayService.generateTransactionId(token);
            const trxId = await WalletFacade.parseTransactionId(transactionId);

            const actions: GamePaymentAction[] = [
                {
                    action: "credit",
                    attribute: "balance",
                    amount: null
                },
                {
                    action: "debit",
                    attribute: "balance",
                    amount: null
                }
            ];

            const request: GamePaymentOperation = {
                roundId: "1000",
                gameSessionId: "1",
                gameToken: token,
                transactionId: trxId,
                actions: actions,
                ts: new Date().toISOString()
            };

            await expect(PlayService.commitPaymentOperation(request, BALANCE_WITH_RT_FILTER))
                .to.be.rejectedWith(PlayServiceErrors.InvalidPaymentActionValueError);
        });

        it("Commit operation for bet and win and stat", async () => {
            const token = await TokenUtils.generateGameToken(authTokenForMerchant);
            const transactionId = await PlayService.generateTransactionId(token);
            const trxId = await WalletFacade.parseTransactionId(transactionId);

            const testAdapter: any = {
                validate: function (): string[] {
                    return [];
                },
                commitPayment: (m, t, request) => {
                    expect(request).to.deep.equal({
                        "actions": [
                            {
                                "action": "credit",
                                "amount": 100,
                                "attribute": "balance"
                            },
                            {
                                "action": "debit",
                                "amount": 50,
                                "attribute": "balance"
                            }
                        ],
                        "bet": 50,
                        "win": 100,
                        "gameToken": token,
                        "isJPWin": false,
                        "roundId": "1000",
                        "roundPID": PUBLIC_ID,
                        "gameSessionId": "1",
                        "transactionId": trxId,
                        "ts": request.ts
                    });
                    return Promise.resolve({ "main": 50 });
                },
                getBalances: () => {
                    return Promise.resolve({ "USD": { "main": 50 } });
                }
            };

            await register("test", testAdapter as AnyMerchantAdapter);
            const actions: GamePaymentAction[] = [
                {
                    action: "credit",
                    attribute: "balance",
                    amount: 100
                },
                {
                    action: "debit",
                    attribute: "balance",
                    amount: 50
                },
                {
                    action: "credit",
                    attribute: "stars",
                    amount: 1
                },
            ];

            const request: GamePaymentOperation = {
                roundId: "1000",
                gameSessionId: "1",
                gameToken: token,
                transactionId: trxId,
                actions: actions,
                ts: new Date().toISOString()
            };

            const balance = await PlayService.commitPaymentOperation(request, BALANCE_WITH_RT_FILTER);

            expect(balance)
                .deep
                .equal({
                    "main": 50,
                    "extraBalances": { "stars": 1 },
                    extraData: { roundPID: PUBLIC_ID }
                });

            const balance2 = await PlayService.getGameBalance(token, BALANCE_WITH_RT_FILTER);
            expect(balance2)
                .deep
                .equal({
                    "main": 50,
                    "extraBalances": { "stars": 1 }
                });
        });

        it("Commit operation with 2 bets and wins", async () => {
            const token = await TokenUtils.generateGameToken(authTokenForMerchant);
            const transactionId = await PlayService.generateTransactionId(token);
            const trxId = await WalletFacade.parseTransactionId(transactionId);

            const testAdapter: any = {
                commitPayment: (m, t, request) => {
                    expect(request).to.deep.equal({
                        "actions": [
                            {
                                "action": "credit",
                                "amount": 100,
                                "attribute": "balance"
                            },
                            {
                                "action": "credit",
                                "amount": 50,
                                "attribute": "balance"
                            },
                            {
                                action: "debit",
                                attribute: "balance",
                                amount: 200
                            },
                            {
                                action: "debit",
                                attribute: "balance",
                                amount: 100
                            }
                        ],
                        "bet": 300,
                        "win": 150,
                        "gameToken": token,
                        "isJPWin": false,
                        "roundId": "1000",
                        "roundPID": PUBLIC_ID,
                        "gameSessionId": "1",
                        "transactionId": trxId,
                        "ts": request.ts
                    });
                    return Promise.resolve({ "main": 150 });
                }
            };

            await register("test", testAdapter as AnyMerchantAdapter);

            const actions: GamePaymentAction[] = [
                {
                    action: "credit",
                    attribute: "balance",
                    amount: 100
                },
                {
                    action: "credit",
                    attribute: "balance",
                    amount: 50
                },
                {
                    action: "debit",
                    attribute: "balance",
                    amount: 200
                },
                {
                    action: "debit",
                    attribute: "balance",
                    amount: 100
                }
            ];

            const request: GamePaymentOperation = {
                roundId: "1000",
                gameSessionId: "1",
                gameToken: token,
                transactionId: trxId,
                actions: actions,
                ts: new Date().toISOString()
            };

            const balance = await PlayService.commitPaymentOperation(request, BALANCE_WITH_RT_FILTER);
            expect(balance).deep.equal({ "main": 150, extraData: { roundPID: PUBLIC_ID } });
        });

        it("Commit operation with insufficient balance for bet and valid retention- negative", async () => {
            const token = await TokenUtils.generateGameToken(authTokenForMerchant);
            const transactionId = await PlayService.generateTransactionId(token);
            const trxId = await WalletFacade.parseTransactionId(transactionId);

            const testAdapter: any = {
                validate: function (): string[] {
                    return [];
                },
                commitPayment: () => {
                    throw new WalletErrors.InsufficientBalanceError();
                },
                getBalances: () => {
                    return Promise.resolve({ "main": 100 });
                }
            };

            await register("test", testAdapter as AnyMerchantAdapter);
            const actions: GamePaymentAction[] = [
                {
                    action: "debit",
                    attribute: "balance",
                    amount: 100000
                },
                {
                    action: "credit",
                    attribute: "stars",
                    amount: 50
                }
            ];

            const request: GamePaymentOperation = {
                roundId: "1000",
                gameSessionId: "1",
                gameToken: token,
                transactionId: trxId,
                actions: actions,
                ts: new Date().toISOString()
            };

            await PlayService.commitPaymentOperation(request, BALANCE_WITH_RT_FILTER)
                .should.be.rejectedWith(WalletErrors.InsufficientBalanceError);
        });

        it("Commit only retention for merchant", async () => {
            const token = await TokenUtils.generateGameToken(authTokenForMerchant);
            const transactionId = await PlayService.generateTransactionId(token);
            const trxId = await WalletFacade.parseTransactionId(transactionId);

            const testAdapter: any = {
                validate: function (): string[] {
                    return [];
                },
                commitPayment: () => {
                    throw new Error("SHOULD_NOT_BE_HERE");
                },
                getBalances: () => {
                    return { "USD": { "main": 999 } };
                }
            };

            await register("test", testAdapter as AnyMerchantAdapter);
            const actions: GamePaymentAction[] = [
                {
                    action: "credit",
                    attribute: "stars",
                    amount: 100
                },
                {
                    action: "credit",
                    attribute: "levelPrize",
                    amount: 50
                },
                {
                    action: "credit",
                    attribute: "xp",
                    amount: 1
                },
            ];

            const request: GamePaymentOperation = {
                roundId: "1000",
                roundPID: PUBLIC_ID,
                gameSessionId: "1",
                gameToken: token,
                transactionId: trxId,
                actions: actions,
                ts: new Date().toISOString()
            };

            const balance = await PlayService.commitPaymentOperation(request, BALANCE_WITH_RT_FILTER);

            const EXPECTED_BALANCE = {
                "main": 999,
                "extraBalances": {
                    "levelPrize": 50,
                    "stars": 100,
                    "xp": 1,
                }
            };

            expect(balance).deep.equal({ ...EXPECTED_BALANCE, extraData: { roundPID: PUBLIC_ID } });

            const balance2 = await PlayService.getGameBalance(token, BALANCE_WITH_RT_FILTER);
            expect(balance2).deep.equal({ ...EXPECTED_BALANCE });
        });

        it("Commit operation for free bet and stat", async () => {
            const token = await TokenUtils.generateGameToken(authTokenForMerchant);
            const transactionId = await PlayService.generateTransactionId(token);
            const trxId = await WalletFacade.parseTransactionId(transactionId);

            const testAdapter: any = {
                validate: function (): string[] {
                    return [];
                },
                commitPayment: (m, t, request) => {
                    expect(request).to.deep.equal({
                        "actions": [
                            {
                                "action": "credit",
                                "amount": 100,
                                "attribute": "balance"
                            },
                            {
                                "action": "freebet",
                                "amount": 50,
                                "attribute": "balance"
                            }
                        ],
                        "bet": 50,
                        "win": 100,
                        "freeBetCoin": 0.1,
                        "gameToken": token,
                        "isJPWin": false,
                        "roundId": "1000",
                        "roundPID": PUBLIC_ID,
                        "gameSessionId": "1",
                        "transactionId": trxId,
                        "ts": request.ts
                    });
                    return Promise.resolve({ "main": 50, "freeBets": { "amount": 5 } });
                },
                getBalances: () => {
                    return Promise.resolve({ "USD": { "main": 50, "freeBets": { "amount": 5 } } });
                }
            };

            await register("test", testAdapter as AnyMerchantAdapter);
            const actions: GamePaymentAction[] = [
                {
                    action: "credit",
                    attribute: "balance",
                    amount: 100
                },
                {
                    action: "freebet",
                    attribute: "balance",
                    amount: 50
                },
                {
                    action: "credit",
                    attribute: "stars",
                    amount: 1
                },
            ];

            const request: GamePaymentOperation = {
                roundId: "1000",
                roundPID: PUBLIC_ID,
                gameSessionId: "1",
                gameToken: token,
                transactionId: trxId,
                actions: actions,
                freeBetCoin: 0.1,
                ts: new Date().toISOString()
            };

            const balance = await PlayService.commitPaymentOperation(request);

            expect(balance).deep.equal({
                "main": 50,
                "extraBalances": { "stars": 1 },
                "freeBets": {
                    "amount": 5
                },
                extraData: { roundPID: PUBLIC_ID }
            });

            const balance2 = await PlayService.getGameBalance(token, BALANCE_WITH_RT_FILTER);
            expect(balance2).deep.equal({
                "main": 50,
                "extraBalances": { "stars": 1 },
                "freeBets": {
                    "amount": 5
                }
            });
        });

        it("Commit operation - insufficient free bet", async () => {
            const token = await TokenUtils.generateGameToken(authTokenForMerchant);
            const transactionId = await PlayService.generateTransactionId(token);
            const trxId = await WalletFacade.parseTransactionId(transactionId);

            const testAdapter: any = {
                validate: function (): string[] {
                    return [];
                },
                commitPayment: () => {
                    return Promise.reject(new PromoWalletErrors.InsufficientFreebet());
                },
            };

            await register("test", testAdapter as AnyMerchantAdapter);
            const actions: GamePaymentAction[] = [
                {
                    action: "credit",
                    attribute: "balance",
                    amount: 100
                },
                {
                    action: "freebet",
                    attribute: "balance",
                    amount: 50
                },
            ];

            const request: GamePaymentOperation = {
                roundId: "1000",
                gameSessionId: "1",
                gameToken: token,
                transactionId: trxId,
                actions: actions,
                freeBetCoin: 0.1,
                ts: new Date().toISOString()
            };

            await expect(PlayService.commitPaymentOperation(request))
                .to.be.rejectedWith(PromoWalletErrors.InsufficientFreebet);
        });

    });

    it("Commit operation - insufficient free bet", async () => {
        const token = await TokenUtils.generateGameToken(authTokenForMerchant);
        const transactionId = await PlayService.generateTransactionId(token);
        const trxId = await WalletFacade.parseTransactionId(transactionId);

        const testAdapter: any = {
            validate: function (): string[] {
                return [];
            },
            commitPayment: () => {
                return Promise.reject(new PromoWalletErrors.InsufficientFreebet());
            },
        };

        await register("test", testAdapter as AnyMerchantAdapter);
        const actions: GamePaymentAction[] = [
            {
                action: "credit",
                attribute: "balance",
                amount: 100
            },
            {
                action: "freebet",
                attribute: "balance",
                amount: 50
            },
        ];

        const request: GamePaymentOperation = {
            roundId: "1000",
            gameSessionId: "1",
            gameToken: token,
            transactionId: trxId,
            actions: actions,
            freeBetCoin: 0.1,
            ts: new Date().toISOString()
        };

        await expect(PlayService.commitPaymentOperation(request))
            .to.be.rejectedWith(PromoWalletErrors.InsufficientFreebet);
    });

    describe("Merchant play services operations without token", () => {
        it("commit win", async () => {
            const gameProvider = await GameProviderService.findOne({ code: p2.code });
            const auth: ProviderAuthDetails = {
                providerUser: gameProvider.user,
                providerSecret: gameProvider.secret,
                providerCode: gameProvider.code
            };
            const transactionId = await AnonymousPlayService.generateTransactionId(auth);
            const trxId = await WalletFacade.parseTransactionId(transactionId);

            const testAdapter: any = {
                validate: function (): string[] {
                    return [];
                },
                commitWinWithoutToken: (merch, request) => {
                    expect(request).to.deep.equal({
                        "amount": 200,
                        "brandId": merchBrandEntity.id,
                        "code": "PLAYER_CODE",
                        "currency": "USD",
                        "gameCode": null,
                        "roundId": "1000",
                        "transactionId": trxId
                    });
                    return { "main": 200 };
                }
            };

            await register("test", testAdapter as AnyMerchantAdapter);

            const balance = await AnonymousPlayService.commitWinPayment(auth, {
                code: "PLAYER_CODE",
                currency: "USD",
                brandId: merchBrandEntity.id,
                transactionId: trxId,
                roundId: "1000",
                amount: 200,
                gameCode: null
            } as any);
            expect(balance).deep.equal({ "main": 200 });
        });
    });

    describe("Integrations after successful authentication", () => {
        let gameToken: string;
        let gameTokenWithBadUser: string;

        before(async () => {
            const startGameToken = await TokenUtils.generateStartGameToken(
                {
                    brandId: brandEntity.id,
                    gameCode: "GAME001",
                    providerCode: "providerCode1",
                    providerGameCode: "GAME001",
                    playerCode: player.code,
                    currency: player.currency,
                    playmode: PlayMode.REAL
                });

            const response = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);
            gameToken = response.gameToken;

            gameTokenWithBadUser = await TokenUtils.generateGameToken({
                brandId: brandEntity.id,
                currency: "USD",
                playerCode: "BADUSERTOKEN",
                gameCode: gameCodeInfo.code
            });
        });

        beforeEach(async () => {
            await PlayerGameSessionService.create(brandEntity.id, player.code, gameCodeInfo.code, { isGRCGame: true });
        });

        it("Full flow: start game token returns jurisdiction and responsible gaming settings as well", async () => {
            EntityJurisdictionCache.reset();
            const service = getUserService(master);
            const user = (await service.findOne("SUPERADMIN")).toDetailedInfo();

            const createJurisdictionData: CreateData = {
                title: "jurisdiction_title",
                code: "JT",
                description: "some description",
                createdUserId: user.id,
                updatedUserId: user.id,
                settings: { a: 1, b: 2 }
            };

            const entityTle1 = await findOne({ key: complexStructure.tle1.key });
            const { jurisdictionId } = await factory.create("EntityJurisdiction", {}, {
                jurisdictionBuildOptions: createJurisdictionData,
                entityId: entityTle1.id
            });
            const jurisdictionInstance = await getJurisdictionModel()
                .findByPk(jurisdictionId);

            const entityTle1Ent1 = await findOne({ key: complexStructure.tle1ent1.key });
            await factory.create("EntityJurisdiction", {}, {
                jurisdictionId,
                entityId: entityTle1Ent1.id
            });

            await getEntityJurisdictionService().add(
                brandEntity,
                jurisdictionInstance.get("code")
            );

            await setResponsibleGaming({ casino: { realityCheck: 120 } });

            const startGameToken = await TokenUtils.generateStartGameToken({
                brandId: brandEntity.id,
                gameCode: "GAME001",
                providerCode: "providerCode1",
                providerGameCode: "GAME001",
                playerCode: player.code,
                currency: player.currency,
                playmode: PlayMode.REAL
            });

            const response = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);

            await TokenUtils.verifyGameToken(response.gameToken);
            expect(response.jrsdSettings["a"]).to.eq(1);
            expect(response.jrsdSettings["b"]).to.eq(2);
            expect(response.jrsdSettings["realityCheck"]).to.eq(120);
            expect(response.jurisdictionCode).to.be.equal(createJurisdictionData.code);
        });

        it("getGameBalance failed - wrong token", async () => {
            return PlayService.getGameBalance("WRONGPLAYERTOKEN")
                .should
                .eventually
                .rejectedWith(GameProviderErrors.GameTokenError);
        });

        it("getBalances failed - wrong token", async () => {
            return PlayService.getPlayerBalances(
                "WRONGPLAYERTOKEN")
                .should
                .eventually
                .rejectedWith(GameProviderErrors.GameTokenError);
        });

        it("generatePaymentTransactionId failed - wrong token", async () => {
            return PlayService.generateTransactionId(
                "WRONGPLAYERTOKEN")
                .should
                .eventually
                .rejectedWith(GameProviderErrors.GameTokenError);
        });

        it("commitPayment failed - wrong token", async () => {
            const transactionId = await PlayService.generateTransactionId(gameToken);
            const trxId = await WalletFacade.parseTransactionId(transactionId);
            return PlayService.commitGamePayment({
                gameToken: "WRONGPLAYERTOKEN",
                transactionId: trxId,
                roundId: "1000",
                gameSessionId: "1",
                bet: 100,
                win: 200,
                ts: new Date().toISOString()
            })
                .should
                .eventually
                .rejectedWith(GameProviderErrors.GameTokenError);
        });

        it("commitPayment failed - insufficient balance", async () => {
            const transactionId = await PlayService.generateTransactionId(gameToken);
            const trxId = await WalletFacade.parseTransactionId(transactionId);
            return PlayService.commitGamePayment({
                gameToken: gameToken,
                transactionId: trxId,
                roundId: "1000",
                gameSessionId: "1",
                bet: 10000,
                win: 1,
                ts: new Date().toISOString()
            })
                .should
                .eventually
                .rejectedWith(WalletErrors.InsufficientBalanceError);
        });

        it("commitBetPayment failed - insufficient balance", async () => {
            const transactionId = await PlayService.generateTransactionId(gameToken);
            const trxId = await WalletFacade.parseTransactionId(transactionId);
            return PlayService.commitBetPayment({
                gameToken: gameToken,
                transactionId: trxId,
                roundId: "1000",
                gameSessionId: "1",
                amount: 10000,
                ts: new Date().toISOString()
            })
                .should
                .eventually
                .rejectedWith(WalletErrors.InsufficientBalanceError);
        });

        it("commitPayment failed - wrong transaction id", async () => {
            const transactionId = "badTrxId";
            const trxId = TrxId.create(transactionId);
            return PlayService.commitGamePayment({
                gameToken: gameToken,
                transactionId: trxId,
                roundId: "1000",
                gameSessionId: "1",
                bet: 10000,
                win: 1,
                ts: new Date().toISOString()
            })
                .should
                .eventually
                .rejectedWith(WalletErrors.BadTransactionId);
        });

        it("commitPayment failed - negative win/bet", async () => {
            const transactionId = await WalletFacade.generateTransactionId();
            const trxId = await WalletFacade.parseTransactionId(transactionId);
            return PlayService.commitGamePayment({
                gameToken: gameToken,
                transactionId: trxId,
                roundId: "1000",
                gameSessionId: "1",
                bet: -1,
                win: -1,
                ts: new Date().toISOString()
            }).should
                .eventually
                .rejectedWith(WalletErrors.AmountIsNegativeError);
        });

        it("commitPayment failed - win/bet not set", async () => {
            const transactionId = await WalletFacade.generateTransactionId();
            const trxId = await WalletFacade.parseTransactionId(transactionId);
            return PlayService.commitGamePayment({
                gameToken: gameToken,
                transactionId: trxId,
                roundId: "1000",
                gameSessionId: "1",
                bet: undefined,
                win: undefined,
                ts: new Date().toISOString()
            })
                .should
                .eventually
                .rejectedWith(GameProviderErrors.InvalidGamePayment);
        });

        it("commitPayment failed - - session not found", async () => {
            const trxId = await WalletFacade.generateTrxId();
            return PlayService.commitGamePayment({
                gameToken: gameTokenWithBadUser,
                transactionId: trxId,
                roundId: "1000",
                gameSessionId: "1",
                bet: 100,
                win: 200,
                ts: new Date().toISOString()
            })
                .should
                .eventually
                .rejectedWith(PlayerSessionErrors.PlayerSessionExpiredError);
        });

        it("transfer failed - - not enabled", async () => {
            const trxId = await WalletFacade.generateTrxId();
            return PlayService.transferBalance({
                gameToken: gameTokenWithBadUser,
                transactionId: trxId,
                operation: "transfer-in",
                roundId: "1000",
                gameSessionId: "1",
                amount: 100,
                ts: new Date().toISOString(),
            })
                .should
                .eventually
                .rejectedWith(PlayServiceErrors.UnsupportedPlayMethodError);
        });
    });

    it("Get free bet info", async () => {
        const token = await TokenUtils.generateGameToken(authTokenForMerchant);

        const testAdapter: any = {
            getFreeBetInfo: () => {
                return Promise.resolve({ "amount": 5, "coin": 0.1 });
            }
        };

        await register("test", testAdapter as AnyMerchantAdapter);

        const request: FreeBetInfoRequest = {
            gameToken: token,
            coinMultiplier: 25,
            stakeAll: [0.1, 0.2, 0.5, 1, 2, 10]
        };

        const freeBet = await PlayService.getFreeBetInfo(request);

        expect(freeBet).deep.equal({
            "amount": 5,
            "coin": 0.1
        });
    });

    it("get available games", async () => {
        const startGameToken = await TokenUtils.generateStartGameToken(
            {
                brandId: brandEntity.id,
                gameCode: "GAME001",
                providerCode: "providerCode1",
                providerGameCode: "GAME001",
                playerCode: player.code,
                currency: player.currency,
                playmode: PlayMode.REAL
            });

        const req: GamesListRequest = {
            startGameToken
        };
        const response = await getGames(req);
        expect(response.games.sort()).to.deep.equal(["GAME001", "GAME002"]);
    });

    it("get available games with filter", async () => {
        const game1 = await registerGame(p2.code, "GRC_GAME", "Game 1");
        game1.features = { isGRCGame: true };
        await GameProviderService.update("GRC_GAME", game1);

        const game2 = await registerGame(p2.code, "TRANSFER_GAME", "Game 1");
        game2.features = { transferEnabled: true };
        await GameProviderService.update("TRANSFER_GAME", game2);

        const game3 = await registerGame(p2.code, "TRANSFER_BONUS_GAME", "Game 1");
        game3.features = { transferEnabled: true, isBonusCoinsSupported: true };
        await GameProviderService.update("TRANSFER_BONUS_GAME", game3);

        const game4 = await registerGame(p2.code, "TRANSFER_FB_GAME", "Game 1");
        game4.features = { transferEnabled: true, isFreebetSupported: true };
        await GameProviderService.update("TRANSFER_FB_GAME", game4);

        await GameService.addGamesToAllEntities(master.find({ path: ":TLE1:" }) as ChildEntity,
            { codes: ["GRC_GAME", "TRANSFER_GAME", "TRANSFER_BONUS_GAME", "TRANSFER_FB_GAME"] });

        const startGameToken = await TokenUtils.generateStartGameToken(
            {
                brandId: brandEntity.id,
                gameCode: "GAME001",
                providerCode: "providerCode1",
                providerGameCode: "GAME001",
                playerCode: player.code,
                currency: player.currency,
                playmode: PlayMode.REAL
            });

        const req1: GamesListRequest = {
            startGameToken,
            filter: { isGRCGame: true }
        };
        const response1 = await getGames(req1);
        expect(response1).to.deep.equal({
            "games": ["GRC_GAME"]
        });

        const req2: GamesListRequest = {
            startGameToken,
            filter: { isGRCGame: false }
        };

        const response2 = await getGames(req2);
        response2.games.sort();

        expect(response2).to.deep.equal({
            "games": ["GAME001", "GAME002", "TRANSFER_GAME", "TRANSFER_BONUS_GAME", "TRANSFER_FB_GAME"].sort()
        });

        const req3: GamesListRequest = {
            startGameToken,
            filter: { transferEnabled: true }
        };

        const response3 = await getGames(req3);
        response3.games.sort();

        expect(response3).to.deep.equal({
            "games": ["TRANSFER_GAME", "TRANSFER_BONUS_GAME", "TRANSFER_FB_GAME"].sort()
        });

        const req4: GamesListRequest = {
            startGameToken,
            filter: { transferEnabled: true, isBonusCoinsSupported: true }
        };
        const response4 = await getGames(req4);
        expect(response4).to.deep.equal({
            "games": ["TRANSFER_BONUS_GAME"]
        });

        const req5: GamesListRequest = {
            startGameToken,
            filter: { transferEnabled: true, isBonusCoinsSupported: false }
        };

        const response5 = await getGames(req5);
        response5.games.sort();

        expect(response5).to.deep.equal({
            "games": ["TRANSFER_GAME", "TRANSFER_FB_GAME"].sort()
        });
    });

    it("Starts game with live settings", async () => {
        const game = await registerGame(p2.code,
            "LIVE_GAME",
            "Game 1",
            undefined,
            undefined,
            undefined,
            undefined,
            GAME_TYPES.live
        );
        game.features = {
            live: {
                tableId: "123",
                provider: "mock"
            }
        };
        await GameProviderService.update("LIVE_GAME", game);
        await GameService.addGamesToAllEntities(master.find({ path: ":TLE1:" }) as ChildEntity,
            { codes: ["LIVE_GAME"] });

        const startGameToken = await TokenUtils.generateStartGameToken(
            {
                brandId: brandEntity.id,
                gameCode: "LIVE_GAME",
                providerCode: "providerCode1",
                providerGameCode: "LIVE_GAME",
                playerCode: player.code,
                currency: player.currency,
                playmode: PlayMode.REAL
            });

        const response = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);
        expect(response.settings).to.deep.equal({
            possibleLimits: {
                "maxTotalStake": 100,
                "stakeAll": [
                    0.1,
                    0.5,
                    1,
                    2,
                    3,
                    5,
                ],
                "stakeDef": 1,
                "stakeMax": 5,
                "stakeMin": 0.1,
                "winMax": 2000,
            },
            rtpConfigurator: undefined,
            tableId: "123",
            provider: "mock",
            title: "Game 1",
        });
    });

    it("Starts rush game with live settings from game code", async () => {
        const liveGame = await registerGame(p2.code,
            "LIVE_GAME_1",
            "Game 1",
            undefined,
            undefined,
            {
                live: {
                    tableId: "1",
                    provider: "mock",
                    providerSettings: {
                        test: 0
                    }
                }
            },
            "roulette_1",
            GAME_TYPES.live
        );
        const notAssignToEntityGame = await registerGame(p2.code,
            "LIVE_GAME_1",
            "Game 2",
            undefined,
            undefined,
            {
                live: {
                    tableId: "1",
                    provider: "mock",
                    providerSettings: {
                        test: 0
                    }
                }
            },
            "roulette_2",
            GAME_TYPES.live
        );
        const game = await registerGame(p2.code,
            "LIVE_GAME_1",
            "Game Rush",
            undefined,
            undefined,
            undefined,
            undefined,
            GAME_TYPES.live
        );
        game.features = {
            live: {
                tableId: "123",
                provider: "mock",
                type: "roulette",
                tables: [
                    {
                        tableId: "1",
                        provider: "mock",
                        gameCode: liveGame.code,
                        rushOrder: 0,
                    },
                    {
                        tableId: "1",
                        provider: "mock",
                        gameCode: notAssignToEntityGame.code,
                        rushOrder: 1,
                    }
                ]
            }
        };
        await GameProviderService.update("LIVE_GAME_1", game);
        await GameService.addGamesToAllEntities(
            master.find({ path: ":TLE1:" }) as ChildEntity, { codes: ["LIVE_GAME_1", "roulette_1"] }
        );

        const startGameToken = await TokenUtils.generateStartGameToken(
            {
                brandId: brandEntity.id,
                gameCode: "LIVE_GAME_1",
                providerCode: "providerCode1",
                providerGameCode: "LIVE_GAME_1",
                playerCode: player.code,
                currency: player.currency,
                playmode: PlayMode.REAL
            });

        const response = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);
        expect(response.settings).to.deep.equal({
            possibleLimits: {
                "maxTotalStake": 100,
                "stakeAll": [
                    0.1,
                    0.5,
                    1,
                    2,
                    3,
                    5,
                ],
                "stakeDef": 1,
                "stakeMax": 5,
                "stakeMin": 0.1,
                "winMax": 2000,
            },
            rtpConfigurator: undefined,
            tableId: "123",
            provider: "mock",
            title: "Game Rush",
            type: "roulette",
            tables: [
                {
                    tableId: "1",
                    provider: "mock",
                    gameCode: "roulette_1",
                    rushOrder: 0,
                    isAddedToEntity: true,
                    providerSettings: {
                        "test": 0,
                    },
                    tableName: "Game 1",
                },
                {
                    gameCode: "roulette_2",
                    isAddedToEntity: false,
                    provider: "mock",
                    providerSettings: {
                        "test": 0,
                    },
                    rushOrder: 1,
                    tableId: "1",
                    tableName: "Game 2",
                }
            ]
        });
    });

    it("Starts rush game without live settings", async () => {
        const liveGame = await registerGame(p2.code,
            "LIVE_GAME_2",
            "Game 2",
            undefined,
            undefined,
            {
                live: {
                    tableId: "1",
                    provider: "mock",
                    providerSettings: {
                        test: 0
                    }
                }
            },
            undefined,
            GAME_TYPES.live
        );
        const game = await registerGame(p2.code,
            "LIVE_GAME_3",
            "Game Rush",
            undefined,
            undefined,
            undefined,
            undefined,
            GAME_TYPES.live
        );
        game.features = {
            live: {
                tableId: "123",
                provider: "mock",
                type: "roulette",
                tables: [
                    {
                        tableId: "1",
                        provider: "mock",
                        gameCode: liveGame.code,
                        rushOrder: 0,
                        providerSettings: {
                            abc: 123
                        }
                    }
                ]
            }
        };
        await GameProviderService.update("LIVE_GAME_3", game);
        await GameService.addGamesToAllEntities(
            master.find({ path: ":TLE1:" }) as ChildEntity, { codes: ["LIVE_GAME_3"] }
        );

        const startGameToken = await TokenUtils.generateStartGameToken(
            {
                brandId: brandEntity.id,
                gameCode: "LIVE_GAME_3",
                providerCode: "providerCode1",
                providerGameCode: "LIVE_GAME_3",
                playerCode: player.code,
                currency: player.currency,
                playmode: PlayMode.REAL
            });

        const response = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);
        expect(response.settings).to.deep.equal({
            possibleLimits: {
                "maxTotalStake": 100,
                "stakeAll": [
                    0.1,
                    0.5,
                    1,
                    2,
                    3,
                    5,
                ],
                "stakeDef": 1,
                "stakeMax": 5,
                "stakeMin": 0.1,
                "winMax": 2000,
            },
            rtpConfigurator: undefined,
            tableId: "123",
            provider: "mock",
            title: "Game Rush",
            type: "roulette",
            tables: [
                {
                    tableId: "1",
                    provider: "mock",
                    gameCode: liveGame.code,
                    rushOrder: 0,
                    isAddedToEntity: false,
                    providerSettings: {
                        abc: 123
                    },
                    tableName: "Game 2",
                }
            ]
        });
    });

    it("Starts game with forbidden country", async () => {
        const game = await registerGame(p2.code, "game_from_by", "Game BY");
        game.countries = ["BY", "UA"];
        await GameProviderService.update("game_from_by", game);
        await GameService.addGamesToAllEntities(master.find({ path: ":TLE1:" }) as ChildEntity,
            { codes: ["game_from_by"] });

        const startGameToken = await TokenUtils.generateStartGameToken(
            {
                brandId: brandEntity.id,
                gameCode: "game_from_by",
                providerCode: "providerCode1",
                providerGameCode: "game_from_by",
                playerCode: player.code,
                currency: player.currency,
                playmode: PlayMode.REAL
            });

        await PlayService.startGame({ startGameToken: startGameToken }, US_IP)
            .should
            .be
            .rejectedWith(Errors.CountryIsRestricted);
    });

    it("Starts game with forbidden country in entity game [whitelist]", async () => {
        await registerGame(p2.code, "uniqueGameCode", "Cheshire wild");
        const tle = await master.find({ path: ":TLE1:" }) as ChildEntity;
        await GameService.addGamesToAllEntities(tle, { codes: ["uniqueGameCode"] });
        await getEntityGameService(tle).update("uniqueGameCode", { settings: { countries: ["BY", "UA"] } });

        const startGameToken = await TokenUtils.generateStartGameToken(
            {
                brandId: brandEntity.id,
                gameCode: "uniqueGameCode",
                providerCode: "providerCode1",
                providerGameCode: "game_from_by",
                playerCode: player.code,
                currency: player.currency,
                playmode: PlayMode.REAL
            });

        await PlayService.startGame({ startGameToken: startGameToken }, US_IP)
            .should
            .be
            .rejectedWith(Errors.CountryIsRestricted);
    });

    it("Starts game with forbidden country in entity game [blacklist]", async () => {
        await registerGame(p2.code, "uniqueGameCode2", "Cheshire wild");
        const tle = await master.find({ path: ":TLE1:" }) as ChildEntity;
        await GameService.addGamesToAllEntities(tle, { codes: ["uniqueGameCode2"] });
        await getEntityGameService(tle).update("uniqueGameCode2", { settings: { countries: ["!US", "!UA"] } });

        const startGameToken = await TokenUtils.generateStartGameToken(
            {
                brandId: brandEntity.id,
                gameCode: "uniqueGameCode2",
                providerCode: "providerCode1",
                providerGameCode: "game_from_by2",
                playerCode: player.code,
                currency: player.currency,
                playmode: PlayMode.REAL
            });

        await PlayService.startGame({ startGameToken: startGameToken }, US_IP)
            .should
            .be
            .rejectedWith(Errors.CountryIsRestricted);
    });

    it("Start game with marketing jackpots", async () => {
        const rtpGame = await registerGame(p2.code, "MARKETING_JP_GAME", "Game 1");

        await GameService.addGamesToAllEntities(master.find({ path: ":TLE1:" }) as ChildEntity,
            { codes: ["MARKETING_JP_GAME"] });

        await (new EntitySettingsService(brandEntity)).patch({
            marketing: {
                contributions: [
                    {
                        jackpotId: "MARKETING_JP",
                        contribution: 0.5
                    }
                ]
            }
        });

        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "MARKETING_JP_GAME",
            providerCode: "providerCode1",
            providerGameCode: "MARKETING_JP_GAME",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        let response = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);
        expect(response.settings).deep.equal({
            rtpConfigurator: undefined,
        });

        rtpGame.features = {
            supportsMarketingJP: true
        };
        await GameProviderService.update("MARKETING_JP_GAME", rtpGame);

        response = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);
        expect(response.jackpots).deep.equal([
            {
                "id": "MARKETING_JP",
                "jackpotId": "MARKETING_JP",
                "isGameOwned": false,
                allowMissingContribution: false,
                "paymentStatisticEnabled": false,
                "gameHistoryEnabled": false,
                "winPaymentType": "jackpot_win",
                "contribution": 0.5
            }
        ]);
    });

    it("Start table game", async () => {
        const limitsData: LimitsByCurrencyCode = {
            "USD": {
                "level1": {
                    maxTotalStake: 100,
                    stakeAll: [0.1, 0.5, 1, 2, 3, 5],
                    stakeDef: 1,
                    stakeMax: 5,
                    stakeMin: 0.1,
                    winMax: 2000,
                },
                "level2": {
                    maxTotalStake: 100,
                    stakeAll: [0.1, 0.5, 1, 2, 3, 5],
                    stakeDef: 1,
                    stakeMax: 5,
                    stakeMin: 0.1,
                    winMax: 2000,
                },
            },
        };
        const provider = await GameProviderService.findOne({ code: p2.code });
        await GameProviderService.register({
            type: "table",
            title: "TABLE_GAME",
            url: "http://url.test",
            gameCode: "TABLE_GAME",
            providerGameCode: "TABLE_GAME",
            defaultInfo: { name: "Table Game", description: "..." },
            info: {},
            limits: limitsData,
            providerId: provider.id
        });

        await GameService.addGamesToAllEntities(master.find({ path: ":TLE1:" }) as ChildEntity,
            { codes: ["TABLE_GAME"] });

        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "TABLE_GAME",
            providerCode: "providerCode1",
            providerGameCode: "TABLE_GAME",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        const response = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);
        expect(response.settings).deep.equal({
            possibleLimits: limitsData["USD"],
            rtpConfigurator: undefined,
        });
    });

    it("Start brand game ignore isFreebetsSupported missing or true", async () => {
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME002",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        const response = await PlayService.startGame({
            startGameToken: startGameToken,
        }, US_IP);
        const tokenData = jwt.decode(response.gameToken) as any;
        expect(tokenData.freeBetsDisabled).to.equal(undefined);
    });

    it("Start game - game settings should contain data from features", async () => {
        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        const response: StartGameResponse = await PlayService.startGame({
            startGameToken: startGameToken,
        }, US_IP);
        expect(response.settings).deep.include({
            highestPrizeProbability: 99,
            rtpConfigurator: undefined,
            validateRequestsExtensionEnabled: true,
            zeroBetCheckEnabled: true
        });
    });

    it("Start game - check gamble flag in start game settings", async () => {
        const getEntitySettingsStub = sinon.stub(entitySettingsService, "getEntitySettings");
        getEntitySettingsStub.resolves({ gamble: false } as any);

        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME001",
            providerCode: "providerCode1",
            providerGameCode: "GAME001",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL
        });

        const response: StartGameResponse = await PlayService.startGame({
            startGameToken: startGameToken,
        }, US_IP);
        expect(response.settings.gamble).equal(false);
        expect(response.gameSettings.gamble).equal(true);
        getEntitySettingsStub.restore();
    });

    it("Start external game - response should contain finalization settings", async () => {

        const getEntitySettingsStub = sinon.stub(entitySettingsService, "getEntitySettings");
        getEntitySettingsStub.resolves({
            gamble: false,
            roundExpireAt: 100,
            finalizationSupport: "offlinePayments"
        } as any);

        const startGameToken = await TokenUtils.generateStartGameToken({
            brandId: brandEntity.id,
            gameCode: "GAME004_ext",
            providerCode: "providerCode1",
            providerGameCode: "GAME004_ext",
            playerCode: player.code,
            currency: player.currency,
            playmode: PlayMode.REAL,
            test: true
        });

        const response: StartGameResponse = await PlayService.startGame({
            startGameToken: startGameToken,
        }, US_IP);
        getEntitySettingsStub.restore();
        expect(response.settings).deep.equal({
            "brandFinalizationType": "offlinePayments",
            "gamble": false,
            "roundExpireAt": 100,
            "rtpConfigurator": undefined
        });
    });

    describe("Get new game url and token", () => {
        const gameId = "GAME002";
        const merchantType = "mrch";
        const merchantCode = "merchantCode";

        it("when req contains providerGameCode instead gameId", async () => {
            const startGameToken = await TokenUtils.generateStartGameToken(
                {
                    brandId: brandEntity.id,
                    gameCode: "GAME001",
                    providerCode: p2.code,
                    providerGameCode: "GAME001",
                    playerCode: player.code,
                    currency: player.currency,
                    playmode: PlayMode.REAL
                });

            const req: PlayerGameInfoRequest = {
                startGameToken,
                providerGameCode: gameId
            };
            const playerGameInfo = await getPlayerGameURLInfo(req);
            expect(playerGameInfo).to.have.property("url");
            expect(playerGameInfo).to.have.property("token");

            const newStartGameToken = await TokenUtils.verifyStartGameToken(playerGameInfo.token);
            expect(newStartGameToken.gameCode).to.equal(gameId);
            expect(newStartGameToken.playerCode).to.equal(player.code);
            expect(newStartGameToken.currency).to.equal(player.currency);

        });

        it("for brand's player to real game", async () => {
            const startGameToken = await TokenUtils.generateStartGameToken(
                {
                    brandId: brandEntity.id,
                    gameCode: "GAME001",
                    providerCode: "providerCode1",
                    providerGameCode: "GAME001",
                    playerCode: player.code,
                    currency: player.currency,
                    playmode: PlayMode.REAL
                });

            const req: PlayerGameInfoRequest = {
                startGameToken,
                gameId
            };
            const playerGameInfo = await getPlayerGameURLInfo(req);
            expect(playerGameInfo).to.have.property("url");
            expect(playerGameInfo).to.have.property("token");

            const newStartGameToken = await TokenUtils.verifyStartGameToken(playerGameInfo.token);
            expect(newStartGameToken.gameCode).to.equal(gameId);
            expect(newStartGameToken.playerCode).to.equal(player.code);
            expect(newStartGameToken.currency).to.equal(player.currency);

        });

        it("for brand's player to fun game", async () => {
            const startGameToken = await TokenUtils.generateStartGameToken(
                {
                    brandId: brandEntity.id,
                    gameCode: "GAME001",
                    providerCode: "providerCode1",
                    providerGameCode: "GAME001",
                    playerCode: player.code,
                    playmode: PlayMode.FUN,
                    currency: player.currency,
                });

            const req: PlayerGameInfoRequest = {
                startGameToken,
                gameId
            };
            const playerGameInfo = await getPlayerGameURLInfo(req);
            expect(playerGameInfo).to.have.property("url");
            expect(playerGameInfo).to.have.property("token");

            const newStartGameTokenData = await TokenUtils.verifyStartGameToken(playerGameInfo.token);
            expect(newStartGameTokenData.gameCode).to.equal(gameId);
            expect(newStartGameTokenData.playerCode).to.equal(player.code);
            expect(newStartGameTokenData.currency).to.equal(player.currency);
            expect(newStartGameTokenData.playmode).to.equal(PlayMode.FUN);
        });

        it("for brand's anonymous player to fun game with start game token as an object", async () => {
            const nonExistPlayerId = "nonExistPlayerId";
            const startGameToken = {
                brandId: 1,
                gameCode: "GAME001",
                playerCode: nonExistPlayerId,
                currency: player.currency,
            };

            const req: PlayerGameInfoRequest = {
                startGameToken,
                gameId
            };
            const playerGameInfo = await getPlayerGameURLInfo(req);
            expect(playerGameInfo).to.have.property("url");
            expect(playerGameInfo).to.have.property("token");

            const newStartGameTokenData = await TokenUtils.verifyStartGameToken(playerGameInfo.token);
            expect(newStartGameTokenData.gameCode).to.equal(gameId);
            expect(newStartGameTokenData.playerCode).to.equal(nonExistPlayerId);
            expect(newStartGameTokenData.currency).to.equal(player.currency);
            expect(newStartGameTokenData.playmode).to.equal(PlayMode.FUN);
        });

        it("for brand's anonymous player to fun game", async () => {
            const nonExistPlayerId = "nonExistPlayerId";
            const startGameToken = await TokenUtils.generateStartGameToken(
                {
                    brandId: brandEntity.id,
                    gameCode: "GAME001",
                    providerCode: "providerCode1",
                    providerGameCode: "GAME001",
                    playerCode: nonExistPlayerId,
                    playmode: PlayMode.FUN,
                    currency: player.currency,
                });

            const req: PlayerGameInfoRequest = {
                startGameToken,
                gameId
            };
            const playerGameInfo = await getPlayerGameURLInfo(req);
            expect(playerGameInfo).to.have.property("url");
            expect(playerGameInfo).to.have.property("token");

            const newStartGameTokenData = await TokenUtils.verifyStartGameToken(playerGameInfo.token);
            expect(newStartGameTokenData.gameCode).to.equal(gameId);
            expect(newStartGameTokenData.currency).to.equal(brandEntity.defaultCurrency);
            expect(newStartGameTokenData.playerCode).to.be.equal(nonExistPlayerId);
            expect(newStartGameTokenData.playmode).to.equal(PlayMode.FUN);
        });

        it("for brand's anonymous player to real game should be rejected", async () => {
            const nonExistPlayerId = "nonExistPlayerId";
            const startGameToken = await TokenUtils.generateStartGameToken(
                {
                    brandId: brandEntity.id,
                    gameCode: "GAME001",
                    providerCode: "providerCode1",
                    providerGameCode: "GAME001",
                    playerCode: nonExistPlayerId,
                    currency: player.currency,
                    playmode: PlayMode.REAL
                });

            const req: PlayerGameInfoRequest = {
                startGameToken,
                gameId
            };
            await getPlayerGameURLInfo(req).should.eventually.rejectedWith(Errors.PlayerNotFoundError);
        });

        it("for merchant's player to real game", async () => {
            await getMerchantCRUDService().remove(merchBrandEntity);
            await getMerchantCRUDService().create(merchBrandEntity, {
                type: merchantType,
                code: merchantCode,
                params: {
                    param1: "p1",
                    param2: "p2",
                },
            });

            const startGameToken = await TokenUtils.generateStartGameToken({
                brandId: merchBrandEntity.id,
                gameCode: "GAME001",
                providerCode: "providerCode1",
                providerGameCode: "GAME001",
                playerCode: player.code,
                merchantSessionId: "SOME_SESSION_ID",
                merchantType: merchantType,
                merchantCode: merchantCode,
                currency: "USD",
                country: "US",
                language: "EN",
                playmode: PlayMode.REAL
            } as StartGameTokenData);

            const req: PlayerGameInfoRequest = {
                startGameToken,
                gameId
            };
            const playerGameInfo = await getPlayerGameURLInfo(req);
            expect(playerGameInfo).to.have.property("url");
            expect(playerGameInfo).to.have.property("token");

            const parsedTokenData = await TokenUtils.verifyStartGameToken(playerGameInfo.token);
            const merchantTokenData = parsedTokenData as MerchantStartGameTokenData;

            expect(merchantTokenData.gameCode).to.equal(gameId);
            expect(merchantTokenData.playerCode).to.equal(player.code);
            expect(merchantTokenData.providerCode).to.equal(p2.code);
            expect(merchantTokenData.providerGameCode).to.equal(gameId);
            expect(merchantTokenData.merchantType).to.equal(merchantType);
            expect(merchantTokenData.merchantCode).to.equal(merchantCode);
        });

        it("For merchant's player to fun game", async () => {
            const startGameToken = await TokenUtils.generateStartGameToken({
                brandId: merchBrandEntity.id,
                gameCode: "GAME001",
                providerCode: "providerCode1",
                providerGameCode: "GAME001",
                playerCode: player.code,
                merchantSessionId: "SOME_SESSION_ID",
                merchantType: merchantType,
                merchantCode: merchantCode,
                currency: "USD",
                country: "US",
                language: "EN",
                playmode: PlayMode.FUN
            } as StartGameTokenData);

            const req: PlayerGameInfoRequest = {
                startGameToken,
                gameId
            };
            const playerGameInfo = await getPlayerGameURLInfo(req);
            expect(playerGameInfo).to.have.property("url");
            expect(playerGameInfo).to.have.property("token");

            const newStartGameTokenData = await TokenUtils.verifyStartGameToken(playerGameInfo.token);

            expect(newStartGameTokenData.gameCode).to.equal(gameId);
            expect(newStartGameTokenData.playerCode).to.equal(player.code);
            expect(newStartGameTokenData.providerCode).to.equal(p2.code);
            expect(newStartGameTokenData.providerGameCode).to.equal(gameId);
        });

        it("for master's anonymous player", async () => {
            const startGameToken = await TokenUtils.generateStartGameToken({
                brandId: master.id,
                gameCode: "GAME001",
                playerCode: player.code,
                playmode: PlayMode.FUN,
                currency: "USD",
            } as StartGameTokenData);

            const req: PlayerGameInfoRequest = {
                startGameToken,
                gameId
            };
            const playerGameInfo = await getPlayerGameURLInfo(req);
            expect(playerGameInfo).to.have.property("url");
            expect(playerGameInfo).to.have.property("token");

            const newStartGameTokenData = await TokenUtils.verifyStartGameToken(playerGameInfo.token);
            expect(newStartGameTokenData.gameCode).to.equal(gameId);
            expect(newStartGameTokenData.currency).to.equal("USD");
            expect(newStartGameTokenData.playerCode).to.equal(player.code);
            expect(newStartGameTokenData.playmode).to.equal(PlayMode.FUN);
        });

        it("for brand's player to real game with envId", async () => {
            DynamicDomainCache.reset();
            const startGameToken = await TokenUtils.generateStartGameToken(
                {
                    brandId: brandWithDomain.id,
                    gameCode: "GAME001",
                    providerCode: "providerCode1",
                    providerGameCode: "GAME001",
                    playerCode: player.code,
                    currency: player.currency,
                    envId: "some-env-id",
                    playmode: PlayMode.REAL
                });

            const req: PlayerGameInfoRequest = {
                startGameToken,
                gameId
            };

            const playerGameInfo = await getPlayerGameURLInfo(req);
            expect(playerGameInfo).to.have.property("url");
            expect(playerGameInfo).to.have.property("token");

            const newStartGameToken = await TokenUtils.verifyStartGameToken(playerGameInfo.token);
            expect(newStartGameToken.gameCode).to.equal(gameId);
            expect(newStartGameToken.playerCode).to.equal(player.code);
            expect(newStartGameToken.currency).to.equal(player.currency);
            expect(newStartGameToken.envId).to.equal(entityDomain.environment);

        });

        it("for brand's player to fun game with envId", async () => {
            DynamicDomainCache.reset();
            const startGameToken = await TokenUtils.generateStartGameToken(
                {
                    brandId: brandWithDomain.id,
                    gameCode: "GAME001",
                    providerCode: "providerCode1",
                    providerGameCode: "GAME001",
                    playerCode: player.code,
                    playmode: PlayMode.FUN,
                    currency: player.currency,
                    envId: "some-env-id"
                });

            const req: PlayerGameInfoRequest = {
                startGameToken,
                gameId
            };

            const playerGameInfo = await getPlayerGameURLInfo(req);
            expect(playerGameInfo).to.have.property("url");
            expect(playerGameInfo).to.have.property("token");

            const newStartGameToken = await TokenUtils.verifyStartGameToken(playerGameInfo.token);
            expect(newStartGameToken.gameCode).to.equal(gameId);
            expect(newStartGameToken.playerCode).to.equal(player.code);
            expect(newStartGameToken.currency).to.equal(player.currency);
            expect(newStartGameToken.envId).to.equal(entityDomain.environment);
            expect(newStartGameToken.playmode).to.equal(PlayMode.FUN);
        });

        it("for brand's player in bns playmode - error", async () => {
            const startGameToken = await TokenUtils.generateStartGameToken(
                {
                    brandId: brandEntity.id,
                    gameCode: "GAME001",
                    providerCode: "providerCode1",
                    providerGameCode: "GAME001",
                    playerCode: player.code,
                    currency: player.currency,
                    playmode: PlayMode.REAL
                });

            const req: PlayerGameInfoRequest = {
                startGameToken,
                gameId,
                playmode: PlayMode.BNS
            };
            await expect(getPlayerGameURLInfo(req)).to.be.rejectedWith(Errors.BonusCoinsNotAvailableError);
        });

        it("for brand's player in rt game in bns playmode - error", async () => {
            const provider = await createRandomGameProvider();
            const game = await registerGame(provider.code, "RT_GAME", "Game 1");
            game.features = { isGRCGame: true };
            await GameProviderService.update("RT_GAME", game);
            await GameService.addGamesToAllEntities(master.find({ path: ":TLE1:" }) as ChildEntity,
                { codes: ["RT_GAME"] });

            const startGameToken = await TokenUtils.generateStartGameToken(
                {
                    brandId: brandEntity.id,
                    gameCode: "RT_GAME",
                    providerCode: "providerCode1",
                    providerGameCode: "RT_GAME",
                    playerCode: player.code,
                    currency: player.currency,
                    playmode: PlayMode.BNS
                });

            const req: PlayerGameInfoRequest = {
                startGameToken,
                gameId: "RT_GAME",
                playmode: PlayMode.BNS
            };
            await expect(getPlayerGameURLInfo(req)).to.be.rejectedWith(Errors.BonusCoinsNotAvailableError);
        });

        it("for merchant's player in bns playmode - error", async () => {
            await getMerchantCRUDService().remove(merchBrandEntity);

            await getMerchantCRUDService().create(merchBrandEntity, {
                type: merchantType,
                code: merchantCode,
                params: {
                    param1: "p1",
                    param2: "p2",
                },
            });

            const startGameToken = await TokenUtils.generateStartGameToken({
                brandId: merchBrandEntity.id,
                gameCode: "GAME001",
                providerCode: "providerCode1",
                providerGameCode: "GAME001",
                playerCode: player.code,
                merchantSessionId: "SOME_SESSION_ID",
                merchantType: merchantType,
                merchantCode: merchantCode,
                currency: "USD",
                country: "US",
                language: "EN",
                playmode: PlayMode.REAL
            } as StartGameTokenData);

            const req: PlayerGameInfoRequest = {
                startGameToken,
                gameId,
                playmode: PlayMode.BNS
            };
            await expect(getPlayerGameURLInfo(req)).to.be.rejectedWith(Errors.BonusCoinsNotAvailableError);
        });

        it("for merchant's player in real playmode with BNS currency - error", async () => {
            await getMerchantCRUDService().remove(merchBrandEntity);

            await getMerchantCRUDService().create(merchBrandEntity, {
                type: merchantType,
                code: merchantCode,
                params: {
                    param1: "p1",
                    param2: "p2",
                },
            });

            const startGameToken = await TokenUtils.generateStartGameToken({
                brandId: merchBrandEntity.id,
                gameCode: "GAME001",
                providerCode: "providerCode1",
                providerGameCode: "GAME001",
                playerCode: player.code,
                merchantSessionId: "SOME_SESSION_ID",
                merchantType: merchantType,
                merchantCode: merchantCode,
                currency: "BNS",
                country: "US",
                language: "EN",
                playmode: PlayMode.REAL
            } as StartGameTokenData);

            const req: PlayerGameInfoRequest = {
                startGameToken,
                gameId,
                playmode: PlayMode.REAL
            };
            await expect(getPlayerGameURLInfo(req)).to.be.rejectedWith(Errors.CurrencyNotFoundError);
        });

        it("for merchant's player in rt game in bns playmode - error", async () => {
            const provider = await createRandomGameProvider();
            const game = await registerGame(provider.code, "MRCH_RT_GAME", "Game 1");
            game.features = { isGRCGame: true };
            await GameProviderService.update("MRCH_RT_GAME", game);
            await GameService.addGamesToAllEntities(merchBrandEntity,
                { codes: ["MRCH_RT_GAME"] });

            await getMerchantCRUDService().remove(merchBrandEntity);

            await getMerchantCRUDService().create(merchBrandEntity, {
                type: merchantType,
                code: merchantCode,
                params: {
                    param1: "p1",
                    param2: "p2",
                },
            });

            const startGameToken = await TokenUtils.generateStartGameToken({
                brandId: merchBrandEntity.id,
                gameCode: "MRCH_RT_GAME",
                providerCode: "providerCode1",
                providerGameCode: "MRCH_RT_GAME",
                playerCode: player.code,
                merchantSessionId: "SOME_SESSION_ID",
                merchantType: merchantType,
                merchantCode: merchantCode,
                currency: "USD",
                country: "US",
                language: "EN",
                playmode: PlayMode.REAL
            } as StartGameTokenData);

            const req: PlayerGameInfoRequest = {
                startGameToken,
                gameId: "MRCH_RT_GAME",
                playmode: PlayMode.BNS
            };
            await expect(getPlayerGameURLInfo(req)).to.be.rejectedWith(Errors.BonusCoinsNotAvailableError);
        });

        describe("Get new game url and token using expired startGameToken and valid game token", () => {

            let startGameToken;

            before(async () => {
                startGameToken = await swUtilsToken.generate(
                    {
                        brandId: brandEntity.id,
                        gameCode: "GAME001",
                        providerCode: "providerCode1",
                        providerGameCode: "GAME001",
                        playerCode: player.code,
                        currency: player.currency,
                        playmode: PlayMode.REAL
                    },
                    {
                        secret: config.startGameToken.secret,
                        algorithm: config.startGameToken.algorithm,
                        issuer: config.startGameToken.issuer,
                        expiresIn: 0, // expired token
                    }
                );
            });

            it("for brand's player", async () => {

                const gameToken = await TokenUtils.generateGameToken(
                    {
                        playerCode: player.code,
                        gameCode: "GAME001",
                        brandId: brandEntity.id,
                        currency: player.currency,
                        playmode: PlayMode.BNS,
                    }
                );

                const req: PlayerGameInfoRequest = {
                    startGameToken,
                    gameToken,
                    gameId
                };
                const playerGameInfo = await getPlayerGameURLInfo(req);
                expect(playerGameInfo).to.have.property("url");
                expect(playerGameInfo).to.have.property("token");

                const newStartGameToken = await TokenUtils.verifyStartGameToken(playerGameInfo.token);
                expect(newStartGameToken.gameCode).to.equal(gameId);
                expect(newStartGameToken.playerCode).to.equal(player.code);
                expect(newStartGameToken.currency).to.equal(player.currency);
            });

            it("for brand's player - playerCodes don't match", async () => {

                const gameToken = await TokenUtils.generateGameToken(
                    {
                        playerCode: "PL00100",
                        gameCode: "GAME001",
                        brandId: brandEntity.id,
                        currency: player.currency,
                        playmode: PlayMode.BNS,
                    }
                );

                const req: PlayerGameInfoRequest = {
                    startGameToken,
                    gameToken,
                    gameId
                };

                const promise = getPlayerGameURLInfo(req);
                await promise.should.eventually.be.rejected
                    .and.be.an.instanceOf(Errors.TokensVerifyError)
                    .and.have.property("message", "Start and Game Token verification error: " +
                        "wrong values (PL00001, PL00100) for playerCode key");
            });

            it("for brand's player - gameCodes don't match", async () => {

                const gameToken = await TokenUtils.generateGameToken(
                    {
                        playerCode: player.code,
                        gameCode: "GAME002",
                        brandId: brandEntity.id,
                        currency: player.currency,
                        playmode: PlayMode.BNS,
                    }
                );

                const req: PlayerGameInfoRequest = {
                    startGameToken,
                    gameToken,
                    gameId
                };

                const promise = getPlayerGameURLInfo(req);
                await promise.should.eventually.be.rejected
                    .and.be.an.instanceOf(Errors.TokensVerifyError)
                    .and.have.property("message", "Start and Game Token verification error: " +
                        "wrong values (GAME001, GAME002) for gameCode key");
            });

            it("for brand's player - brandIds don't match", async () => {

                const gameToken = await TokenUtils.generateGameToken(
                    {
                        playerCode: player.code,
                        gameCode: "GAME001",
                        brandId: 100,
                        currency: player.currency,
                        playmode: PlayMode.BNS,
                    }
                );

                const req: PlayerGameInfoRequest = {
                    startGameToken,
                    gameToken,
                    gameId
                };

                const promise = getPlayerGameURLInfo(req);
                await promise.should.eventually.be.rejected
                    .and.be.an.instanceOf(Errors.TokensVerifyError)
                    .and.have.property("message", "Start and Game Token verification error: " +
                        "wrong values (8, 100) for brandId key");
            });

            it("for brand's player - currencies don't match", async () => {

                const gameToken = await TokenUtils.generateGameToken(
                    {
                        playerCode: player.code,
                        gameCode: "GAME001",
                        brandId: brandEntity.id,
                        currency: "EUR",
                        playmode: PlayMode.BNS,
                    }
                );

                const req: PlayerGameInfoRequest = {
                    startGameToken,
                    gameToken,
                    gameId
                };

                const promise = getPlayerGameURLInfo(req);
                await promise.should.eventually.be.rejected
                    .and.be.an.instanceOf(Errors.TokensVerifyError)
                    .and.have.property("message", "Start and Game Token verification error: " +
                        "wrong values (USD, EUR) for currency key");
            });

        });
    });

    describe("Maintenance", () => {

        const maintenanceUrl = "http://maintenance.test";

        before(async () => {
            await EntityService.turnMaintenanceOn(brandEntity, {});
            await EntityService.turnMaintenanceOn(merchBrandEntity, {});
        });

        after(async () => {
            await EntityService.turnMaintenanceOff(brandEntity, {});
            await EntityService.turnMaintenanceOff(merchBrandEntity, {});
        });

        it("gets game url for brand", async () => {
            await new EntitySettingsService(brandEntity).patch({ maintenanceUrl });
            const startGameToken = await TokenUtils.generateStartGameToken(
                {
                    brandId: brandEntity.id,
                    gameCode: "GAME001",
                    providerCode: "providerCode1",
                    providerGameCode: "GAME001",
                    playerCode: player.code,
                    currency: player.currency,
                    playmode: PlayMode.REAL
                });

            const req: PlayerGameInfoRequest = { startGameToken, gameId: "GAME001" };
            const playerGameInfo = await getPlayerGameURLInfo(req);
            expect(playerGameInfo.url).to.be.equal(maintenanceUrl);
            expect(playerGameInfo).to.not.have.property("token");
        });

        it("gets game url for brand - maintenance url not defined", async () => {
            await new EntitySettingsService(brandEntity).patch({});
            const startGameToken = await TokenUtils.generateStartGameToken(
                {
                    brandId: brandEntity.id,
                    gameCode: "GAME001",
                    providerCode: "providerCode1",
                    providerGameCode: "GAME001",
                    playerCode: player.code,
                    currency: player.currency,
                    playmode: PlayMode.REAL
                });

            const req: PlayerGameInfoRequest = { startGameToken, gameId: "GAME001" };
            await expect(getPlayerGameURLInfo(req))
                .to.be.rejectedWith(Errors.MaintenanceUrlNotDefinedError);
        });

        it("gets game url for merchant", async () => {
            await new EntitySettingsService(merchBrandEntity).patch({ maintenanceUrl });
            const startGameToken = await TokenUtils.generateStartGameToken({
                brandId: merchBrandEntity.id,
                gameCode: "GAME001",
                providerCode: "providerCode1",
                providerGameCode: "GAME001",
                playerCode: player.code,
                merchantSessionId: "SOME_SESSION_ID",
                merchantType: "mrch",
                merchantCode: "merchantCode",
                currency: "USD",
                country: "US",
                language: "EN",
                playmode: PlayMode.REAL
            } as StartGameTokenData);

            const req: PlayerGameInfoRequest = { startGameToken, gameId: "GAME001" };
            const playerGameInfo = await getPlayerGameURLInfo(req);
            expect(playerGameInfo.url).to.be.equal(maintenanceUrl);
            expect(playerGameInfo).to.not.have.property("token");
        });

        it("gets game url for merchant - maintenance url not defined", async () => {
            await new EntitySettingsService(merchBrandEntity).patch({});
            const startGameToken = await TokenUtils.generateStartGameToken({
                brandId: merchBrandEntity.id,
                gameCode: "GAME001",
                providerCode: "providerCode1",
                providerGameCode: "GAME001",
                playerCode: player.code,
                merchantSessionId: "SOME_SESSION_ID",
                merchantType: "mrch",
                merchantCode: "merchantCode",
                currency: "USD",
                country: "US",
                language: "EN",
                playmode: PlayMode.REAL
            } as StartGameTokenData);

            const req: PlayerGameInfoRequest = { startGameToken, gameId: "GAME001" };
            await expect(getPlayerGameURLInfo(req))
                .to.be.rejectedWith(Errors.MaintenanceUrlNotDefinedError);
        });

        it("fails to start game", async () => {
            const startGameToken = await TokenUtils.generateStartGameToken({
                brandId: brandEntity.id,
                gameCode: "GAME001",
                providerCode: "providerCode1",
                providerGameCode: "GAME001",
                playerCode: player.code,
                currency: player.currency,
                playmode: PlayMode.REAL
            });

            await expect(PlayService.startGame({ startGameToken: startGameToken }, US_IP))
                .to.be.rejectedWith(Errors.EntityUnderMaintenanceError);
        });

        // todo aguzanov fix test after refactoring of provider
        it("fails to commit payment", async () => {
            const gameToken: GameTokenData = {
                gameCode: "GAME001",
                brandId: brandEntity.id,
                playerCode: "SOME_CUSTOMER",
                currency: "USD"
            };

            const transactionId = await WalletFacade.generateTransactionId();

            const token = await TokenUtils.generateGameToken(gameToken);
            await expect(PlayService.commitPaymentOperation({ gameToken: token, transactionId } as any))
                .to.be.rejectedWith(PlayerSessionErrors.PlayerSessionExpiredError);
        });

        it("fails to generate token with bad player code", async () => {
            await expect(TokenUtils.generateStartGameToken({
                brandId: brandEntity.id,
                gameCode: "GAME001",
                providerCode: "providerCode1",
                providerGameCode: "GAME001",
                playerCode: "player:",
                currency: player.currency,
                playmode: PlayMode.REAL
            })).to.be.rejectedWith(Errors.ValidationError);
        });
    });

    describe("Regulatory data check", () => {

        it("Adds operatorName and operatorLicenseId to start game data", async () => {
            await getMerchantCRUDService().remove(merchBrandEntity);
            reset();
            const testAdapterX: any = {
                getGameTokenInfo: (merchant, tokenData, currency, entityGame) => {
                    return {
                        gameTokenData: {
                            desc: "Custom merchant game token",
                            playerCode: "SOME_CUSTOMER:",
                            merchantSessionId: "SOME_SESSION_ID",
                            merchantType: "pop",
                            merchantCode: "merch",
                            currency: currency
                        }
                    };
                },
                getBalances: () => {
                    return { "USD": { "main": 100 } };
                }
            };
            await register("pop", testAdapterX as AnyMerchantAdapter);

            await getMerchantCRUDService().create(merchBrandEntity, {
                type: "pop",
                code: "merch",
                params: {
                    param1: "p1",
                    param2: "p2",
                    regulatorySettings: {
                        merchantRegulation: "italian",
                        operatorName: "italianCasino777",
                        operatorLicenseId: "itLicense345432"
                    }
                }
            });

            const startGameToken = await TokenUtils.generateStartGameToken({
                brandId: merchBrandEntity.id,
                gameCode: "GAME001",
                providerCode: "providerCode1",
                providerGameCode: "GAME001",
                playerCode: "SOME_CUSTOMER",
                merchantSessionId: "SOME_SESSION_ID",
                merchantType: "testY",
                merchantCode: "merch",
                currency: "USD",
                country: "US",
                language: "EN",
                playmode: PlayMode.REAL
            } as StartGameTokenData);

            const result = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);
            expect(result.brandSettings).to.exist;
            expect(result.brandSettings.operatorName).to.eq("italianCasino777");
            expect(result.brandSettings.operatorLicenseId).to.eq("itLicense345432");
        });

        it("Should return regulatoryLinks in the brandSettings on start game", async () => {
            const getEntitySettingsStub = sinon.stub(entitySettingsService, "getEntitySettings");
            const regulatoryLinks: RegulatoryLink[] = [
                {
                    urlType: "123",
                    url: "http://123.com"
                },
                {
                    urlType: "321",
                    url: "http://321.com"
                }
            ];
            getEntitySettingsStub.resolves({ regulatoryLinks } as any);

            await getMerchantCRUDService().remove(merchBrandEntity);
            reset();
            const testAdapterX: any = {
                getGameTokenInfo: (merchant, tokenData, currency, entityGame) => {
                    return {
                        gameTokenData: {
                            desc: "Custom merchant game token",
                            playerCode: "SOME_CUSTOMER:",
                            merchantSessionId: "SOME_SESSION_ID",
                            merchantType: "pop",
                            merchantCode: "merch",
                            currency: currency
                        }
                    };
                },
                getBalances: () => {
                    return { "USD": { "main": 100 } };
                }
            };
            await register("pop", testAdapterX as AnyMerchantAdapter);

            await getMerchantCRUDService().create(merchBrandEntity, {
                type: "pop",
                code: "merch",
                params: {
                    param1: "p1",
                    param2: "p2",
                }
            });

            const startGameToken = await TokenUtils.generateStartGameToken({
                brandId: merchBrandEntity.id,
                gameCode: "GAME001",
                providerCode: "providerCode1",
                providerGameCode: "GAME001",
                playerCode: "SOME_CUSTOMER",
                merchantSessionId: "SOME_SESSION_ID",
                merchantType: "testY",
                merchantCode: "merch",
                currency: "USD",
                country: "US",
                language: "EN",
                playmode: PlayMode.REAL
            } as StartGameTokenData);

            const result = await PlayService.startGame({ startGameToken: startGameToken }, US_IP);
            expect(result.brandSettings).to.exist;
            expect(result.brandSettings.regulatoryLinks).to.deep.equal(regulatoryLinks);

            getEntitySettingsStub.restore();
        });
    });

    it("Check balance without free bets", async () => {
        const gameTokenData: MerchantGameTokenData = {
            gameCode: "GAME001",
            brandId: merchBrandEntity.id,
            playerCode: "SOME_CUSTOMER",
            currency: "USD",
            merchantType: "test",
            merchantCode: "merch",
            isPromoInternal: false,
            freeBetsDisabled: true
        };
        const gameToken = await TokenUtils.generateGameToken(gameTokenData);
        const testAdapter: any = {
            getBalances: () => {
                return Promise.resolve(
                    { "USD": { "main": 5, freeBets: { "amount": 10 } } }
                );
            }
        };
        await register("test", testAdapter as AnyMerchantAdapter);

        await PlayerGameSessionService.create(
            gameTokenData.brandId,
            gameTokenData.playerCode,
            gameTokenData.gameCode, {}
        );

        const balance = await PlayService.getGameBalance(gameToken);

        expect(balance).deep.equal({
            "main": 5
        });
    });

    it("Player balances without free bets", async () => {
        const testAdapter: any = {
            getBalances: () => {
                return {
                    "USD": { "main": 100, freeBets: { "amount": 10 } },
                    "EUR": { "main": 200, freeBets: { "amount": 20 } }
                };
            },
        };

        await register("test", testAdapter as AnyMerchantAdapter);

        const gameTokenData: MerchantGameTokenData = {
            gameCode: "GAME001",
            brandId: merchBrandEntity.id,
            playerCode: "SOME_CUSTOMER",
            currency: "USD",
            merchantType: "test",
            merchantCode: "merch",
            isPromoInternal: false,
            freeBetsDisabled: true
        };

        const gameToken = await TokenUtils.generateGameToken(gameTokenData);

        await PlayerGameSessionService.create(
            gameTokenData.brandId,
            gameTokenData.playerCode,
            gameTokenData.gameCode, {}
        );

        const balances = await PlayService.getPlayerBalances(gameToken);
        expect(balances).deep.equal({ "USD": { "main": 100 }, "EUR": { "main": 200 } });
    });

    it("Create player with prefix", async () => {
        await new EntitySettingsService(brandEntityWithPlayerPrefix).patch({
            playerPrefix: "ABC"
        });
        const playerWithPrefix = await getPlayerService().create(brandEntityWithPlayerPrefix,
            {
                code: "CrocodileGena",
                password: "ACAB!Area51",
                firstName: "Crocodile",
                lastName: "Gena",
                email: "<EMAIL>",
                customData: ["The roof the roof the roof in on fire"],
                country: "US",
                currency: "USD",
                language: "en",
            });
        expect(playerWithPrefix.code).to.be.equal("ABC_CrocodileGena");
    });

    describe("Country", () => {
        let startGameTokenData: StartGameTokenData;
        beforeEach(async () => {
            startGameTokenData = {
                brandId: merchBrandEntity.id,
                gameCode: "GAME001",
                providerCode: "providerCode1",
                providerGameCode: "GAME001",
                playerCode: player.code,
                currency: "USD",
                playmode: PlayMode.REAL
            };
        });

        it("operatorCountry is empty && country is empty && IP is empty", async () => {
            const startGameToken = await TokenUtils.generateStartGameToken(startGameTokenData);

            const res = await PlayService.startGame({ startGameToken }, undefined);
            const response = res as DetailedStartGameResponse;
            const tokenData = jwt.decode(response.gameToken) as any;
            expect(tokenData.country).to.undefined;
            expect(response.player.country).to.undefined;
            expect(response.playedFromCountry).to.undefined;
            expect(response.operatorPlayerCountry).to.undefined;
        });

        it("operatorCountry is empty && country is empty && IP is valid", async () => {
            const startGameToken = await TokenUtils.generateStartGameToken(startGameTokenData);

            const res = await PlayService.startGame({ startGameToken }, US_IP);
            const response = res as DetailedStartGameResponse;
            const tokenData = jwt.decode(response.gameToken) as any;
            expect(tokenData.country).to.undefined;
            expect(response.player.country).to.equal("US");
            expect(response.playedFromCountry).to.equal("US");
            expect(response.operatorPlayerCountry).to.undefined;
        });

        it("operatorCountry is empty && country is empty && IP is whitelisted", async () => {
            const startGameToken = await TokenUtils.generateStartGameToken(startGameTokenData);

            const res = await PlayService.startGame({ startGameToken }, "***********");
            const response = res as DetailedStartGameResponse;
            const tokenData = jwt.decode(response.gameToken) as any;
            expect(tokenData.country).to.undefined;
            expect(response.player.country).to.equal("US");
            expect(response.playedFromCountry).to.undefined;
            expect(response.operatorPlayerCountry).to.undefined;
        });

        it("operatorCountry is valid && country is empty && IP is valid", async () => {
            const startGameToken = await TokenUtils.generateStartGameToken({
                ...startGameTokenData,
                operatorCountry: "US"
            });

            const res = await PlayService.startGame({ startGameToken }, US_IP);
            const response = res as DetailedStartGameResponse;
            const tokenData = jwt.decode(response.gameToken) as any;
            expect(tokenData.country).to.undefined;
            expect(response.player.country).to.undefined;
            expect(response.playedFromCountry).to.equal("US");
            expect(response.operatorPlayerCountry).to.undefined;
        });

        it("operatorCountry is valid && country is valid && IP is valid", async () => {
            const startGameToken = await TokenUtils.generateStartGameToken({
                ...startGameTokenData,
                country: "US",
                operatorCountry: "US"
            });

            const res = await PlayService.startGame({ startGameToken }, US_IP);
            const response = res as DetailedStartGameResponse;
            const tokenData = jwt.decode(response.gameToken) as any;
            expect(tokenData.country).to.undefined;
            expect(response.player.country).to.equal("US");
            expect(response.playedFromCountry).to.equal("US");
            expect(response.operatorPlayerCountry).to.equal("US");
        });
    });
});
