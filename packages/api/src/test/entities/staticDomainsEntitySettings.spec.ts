import { complexStructure, createComplexStructure, truncate } from "./helper";
import { suite, test, timeout } from "mocha-typescript";
import { FACTORY } from "../factories/common";
import EntitySettingsService from "../../skywind/services/settings";
import { validateEntitySettings } from "../../skywind/api/settings";
import { expect, use } from "chai";
import * as Errors from "../../skywind/errors";
import { BaseEntity } from "../../skywind/entities/entity";
import { findOne } from "../../skywind/services/entity";
import { EntitySettings } from "../../skywind/entities/settings";
import { getEntityDomainService } from "../../skywind/services/entityDomainService";
import { DOMAIN_TYPE } from "../../skywind/utils/common";
import { BULK_OPERATION_ACTION, ENTITY_BULK_OPERATION_TYPE, } from "../../skywind/entities/bulk";
import { EntityBulkService } from "../../skywind/services/bulk/entityBulkService";
import { default as getEntityFactory } from "../../skywind/services/entityFactory";

const chaiAsPromised = require("chai-as-promised");
use(chaiAsPromised);

const FactoryGirl = require("factory-girl");

const factory = FactoryGirl.factory;

@suite("Static domains entity restrictions", timeout(20000))
class StaticDomainsEntityRestrictionsSpec {

    private static readonly domains = [];
    private static master;
    private static childOfTle1Ent1;

    public static async before() {
        await truncate();
        StaticDomainsEntityRestrictionsSpec.master = await createComplexStructure();
        for (let i = 0; i < 3; i++) {
            StaticDomainsEntityRestrictionsSpec.domains.push(await factory.create(FACTORY.STATIC_DOMAIN));
        }
        const tle1ent1: BaseEntity = await findOne({ key: complexStructure.tle1ent1.key });
        StaticDomainsEntityRestrictionsSpec.childOfTle1Ent1 = await getEntityFactory(tle1ent1).createEntity({
            name: "my-child-entity1",
            description: "description",
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            jurisdictionCode: "COM"
        });
    }

    @test
    public async addExistingDomainsToMasterNoErrors() {
        const service = new EntitySettingsService(StaticDomainsEntityRestrictionsSpec.master);
        const currentSettings = await service.get();
        const errors = await validateEntitySettings(StaticDomainsEntityRestrictionsSpec.master, {
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[1].id],
            emailTemplates: currentSettings.emailTemplates
        });
        expect(errors).deep.equal([]);
    }

    @test
    public async addFakeDomainsToMaster() {
        const service = new EntitySettingsService(StaticDomainsEntityRestrictionsSpec.master);
        const currentSettings = await service.get();
        await expect(validateEntitySettings(StaticDomainsEntityRestrictionsSpec.master, {
            allowedStaticDomainsForChildId: [1000000, 10000001],
            emailTemplates: currentSettings.emailTemplates
        })).to.be.rejectedWith(Errors.ValidationError);
    }

    @test
    public async addExistingDomainsToChildWhenMasterDoesntHaveDomainsNoErrors() {
        const service = new EntitySettingsService(StaticDomainsEntityRestrictionsSpec.master);
        const currentSettings = await service.get();
        await service.update({
            emailTemplates: currentSettings.emailTemplates
        });
        const child: BaseEntity = await findOne({ key: complexStructure.tle1ent1.key });
        const errors = await validateEntitySettings(child, {
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[1].id]
        });
        expect(errors).deep.equal([]);
    }

    @test
    public async addFakeDomainsToChild() {
        const service = new EntitySettingsService(StaticDomainsEntityRestrictionsSpec.master);
        const currentSettings = await service.get();
        await service.update({
            emailTemplates: currentSettings.emailTemplates
        });
        const child: BaseEntity = await findOne({ key: complexStructure.tle1ent1.key });
        await expect(validateEntitySettings(child, {
            allowedStaticDomainsForChildId: [1000000, 10000001]
        })).to.be.rejectedWith(Errors.ValidationError);
    }

    @test
    public async addSubSetOfDomainsToChild() {
        const service = new EntitySettingsService(StaticDomainsEntityRestrictionsSpec.master);
        const currentSettings = await service.get();
        await service.update({
            emailTemplates: currentSettings.emailTemplates,
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[1].id]
        });
        const child: BaseEntity = await findOne({ key: complexStructure.tle1ent1.key });
        let errors = await validateEntitySettings(child, {
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[1].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[0].id]
        });
        expect(errors).deep.equal([]);
        errors = await validateEntitySettings(child, {
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id]
        });
        expect(errors).deep.equal([]);
    }

    @test
    public async tryToAddNewDomainToChild() {
        const service = new EntitySettingsService(StaticDomainsEntityRestrictionsSpec.master);
        const currentSettings = await service.get();
        await service.update({
            emailTemplates: currentSettings.emailTemplates,
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[1].id]
        });
        const child: BaseEntity = await findOne({ key: complexStructure.tle1ent1.key });
        await expect(validateEntitySettings(child, {
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[2].id]
        })).to.be.rejectedWith(Errors.ValidationError);
    }

    @test
    public async hierarchicalRestrictions() {
        const service = new EntitySettingsService(StaticDomainsEntityRestrictionsSpec.master);
        const currentSettings = await service.get();
        await service.update({
            emailTemplates: currentSettings.emailTemplates,
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[1].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[2].id]
        });
        const child1: BaseEntity = await findOne({ key: complexStructure.tle1.key });
        new EntitySettingsService(child1).update({
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[1].id]
        } as EntitySettings);
        const child2: BaseEntity = await findOne({ key: complexStructure.tle1ent1.key });
        await expect(validateEntitySettings(child2, {
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[2].id]
        })).to.be.rejectedWith(Errors.ValidationError);
    }

    @test
    public async singleSetOperationValidation() {
        const service = new EntitySettingsService(StaticDomainsEntityRestrictionsSpec.master);
        const currentSettings = await service.get();
        await service.update({
            emailTemplates: currentSettings.emailTemplates,
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[1].id]
        });
        const child: BaseEntity = await findOne({ key: complexStructure.tle1ent1.key });
        const domainService = getEntityDomainService(DOMAIN_TYPE.STATIC);
        const result = await domainService.set(child, StaticDomainsEntityRestrictionsSpec.domains[1].id);
        expect(result).to.not.equal(undefined);
        await expect(domainService.set(child, StaticDomainsEntityRestrictionsSpec.domains[2].id))
            .to
            .be
            .rejectedWith(Errors.ValidationError);
    }

    @test
    public async hierarchicalRestrictionsSetOperation() {
        const service = new EntitySettingsService(StaticDomainsEntityRestrictionsSpec.master);
        const currentSettings = await service.get();
        await service.update({
            emailTemplates: currentSettings.emailTemplates,
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[1].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[2].id]
        });
        const child1: BaseEntity = await findOne({ key: complexStructure.tle1.key });
        new EntitySettingsService(child1).update({
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[1].id]
        } as EntitySettings);
        const child2: BaseEntity = await findOne({ key: complexStructure.tle1ent1.key });
        const domainService = getEntityDomainService(DOMAIN_TYPE.STATIC);
        const result = await domainService.set(child2, StaticDomainsEntityRestrictionsSpec.domains[1].id);
        expect(result).to.not.equal(undefined);
        await expect(domainService.set(child2, StaticDomainsEntityRestrictionsSpec.domains[2].id))
            .to
            .be
            .rejectedWith(Errors.ValidationError);
    }

    @test
    public async setStaticBulkOperationSuccess() {
        const service = new EntitySettingsService(StaticDomainsEntityRestrictionsSpec.master);
        const currentSettings = await service.get();
        await service.update({
            emailTemplates: currentSettings.emailTemplates,
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[1].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[2].id]
        });
        const child1: BaseEntity = await findOne({ key: complexStructure.tle1.key });
        new EntitySettingsService(child1).update({
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[1].id]
        } as EntitySettings);
        const child2: BaseEntity = await findOne({ key: complexStructure.tle1ent1.key });

        const bulkService = new EntityBulkService();
        const operations = [
            {
                entityKey: StaticDomainsEntityRestrictionsSpec.master.key,
                action: BULK_OPERATION_ACTION.SET,
                item: {
                    type: ENTITY_BULK_OPERATION_TYPE.STATIC,
                    id: StaticDomainsEntityRestrictionsSpec.domains[0].id
                }
            },
            {
                entityKey: complexStructure.tle1.key,
                action: BULK_OPERATION_ACTION.SET,
                item: {
                    type: ENTITY_BULK_OPERATION_TYPE.STATIC,
                    id: StaticDomainsEntityRestrictionsSpec.domains[0].id
                }
            },
            {
                entityKey: complexStructure.tle1ent1.key,
                action: BULK_OPERATION_ACTION.SET,
                item: {
                    type: ENTITY_BULK_OPERATION_TYPE.STATIC,
                    id: StaticDomainsEntityRestrictionsSpec.domains[1].id
                }
            }];
        const result = await bulkService.process(StaticDomainsEntityRestrictionsSpec.master, operations);
        expect(result.length).to.equal(3);
    }

    @test
    public async setStaticBulkOperationValidationError() {
        const service = new EntitySettingsService(StaticDomainsEntityRestrictionsSpec.master);
        const currentSettings = await service.get();
        await service.update({
            emailTemplates: currentSettings.emailTemplates,
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[1].id]
        });
        const child1: BaseEntity = await findOne({ key: complexStructure.tle1.key });
        new EntitySettingsService(child1).update({
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[1].id]
        } as EntitySettings);

        const bulkService = new EntityBulkService();
        const operations = [
            {
                entityKey: StaticDomainsEntityRestrictionsSpec.master.key,
                action: BULK_OPERATION_ACTION.SET,
                item: {
                    type: ENTITY_BULK_OPERATION_TYPE.STATIC,
                    id: StaticDomainsEntityRestrictionsSpec.domains[0].id
                }
            },
            {
                entityKey: complexStructure.tle1.key,
                action: BULK_OPERATION_ACTION.SET,
                item: {
                    type: ENTITY_BULK_OPERATION_TYPE.STATIC,
                    id: StaticDomainsEntityRestrictionsSpec.domains[2].id
                }
            },
            {
                entityKey: complexStructure.tle1ent1.key,
                action: BULK_OPERATION_ACTION.SET,
                item: {
                    type: ENTITY_BULK_OPERATION_TYPE.STATIC,
                    id: StaticDomainsEntityRestrictionsSpec.domains[2].id
                }
            }];
        await expect(bulkService.process(StaticDomainsEntityRestrictionsSpec.master, operations))
            .to
            .be
            .rejectedWith(Errors.ValidationError);
    }

    @test
    public async attemptToRemoveDomainWhenItExistInChild() {
        const service = new EntitySettingsService(StaticDomainsEntityRestrictionsSpec.master);
        const currentSettings = await service.get();
        await service.update({
            emailTemplates: currentSettings.emailTemplates,
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[1].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[2].id]
        });
        const child1: BaseEntity = await findOne({ key: complexStructure.tle1.key });
        new EntitySettingsService(child1).update({
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[1].id]
        } as EntitySettings);

        await expect(validateEntitySettings(StaticDomainsEntityRestrictionsSpec.master, {
            emailTemplates: currentSettings.emailTemplates,
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[2].id]
        })).to.be.rejectedWith(Errors.ValidationError);
    }

    @test
    public async attemptToRemoveDomainWhenItExistInChildRecursive() {
        const service = new EntitySettingsService(StaticDomainsEntityRestrictionsSpec.master);
        const currentSettings = await service.get();
        const tle1: BaseEntity = await findOne({ key: complexStructure.tle1.key });
        new EntitySettingsService(tle1).update({
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[1].id]
        } as EntitySettings);

        new EntitySettingsService(StaticDomainsEntityRestrictionsSpec.childOfTle1Ent1).update({
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[0].id,
                                             StaticDomainsEntityRestrictionsSpec.domains[1].id]
        } as EntitySettings);

        await expect(validateEntitySettings(tle1, {
            emailTemplates: currentSettings.emailTemplates,
            allowedStaticDomainsForChildId: [StaticDomainsEntityRestrictionsSpec.domains[1].id]
        })).to.be.rejectedWith(Errors.ValidationError);
    }
}
