import { lazy } from "@skywind-group/sw-utils";
import { getDynamicDomainModel, getStaticDomainModel } from "../../skywind/models/domain";
import { FACTORY } from "./common";

const factory = require("factory-girl").factory;

interface DomainBuildOptions {
    domain?: string;
}

interface DynamicBuildOptions extends DomainBuildOptions {
    environment?: string;
}

export const defineDomainFactory = lazy(() => {
    factory.define(FACTORY.STATIC_DOMAIN, getStaticDomainModel(), (buildOptions: DomainBuildOptions) => {
        return {
            domain: buildOptions.domain || factory.chance("domain")
        };
    });

    return factory.define(FACTORY.DYNAMIC_DOMAIN, getDynamicDomainModel(), (buildOptions: DynamicBuildOptions) => {
        return {
            domain: buildOptions.domain || factory.chance("domain"),
            environment: buildOptions.environment || factory.chance("word")
        };
    });
});
