import { expect } from "chai";
import * as sinon from "sinon";
import { buildDynamicLiveManagerUrl } from "../../skywind/services/entityDomainService";
import * as entityDomainService from "../../skywind/services/entityDomainService";
import config from "../../skywind/config";

describe("buildDynamicLiveManagerUrl", async () => {
    let configBackup;
    let getEntityDomainServiceStub: sinon.SinonStub;

    before(async () => {
        getEntityDomainServiceStub = sinon.stub(entityDomainService, "getEntityDomainService");
    });

    beforeEach(async () => {
        configBackup = { ...config.liveManager };
        config.liveManager = { schema: "https", port: 443, path: "/live-manager", baseUrl: "" };
    });

    afterEach(async () => {
        config.liveManager = configBackup;
        sinon.reset();
    });

    after(async () => {
        getEntityDomainServiceStub.restore();
        sinon.restore();
    });

    it("should return the correct URL and path for HTTPS with default port", async () => {
        const dynamicEntityDomainServiceFake: any = {
            get() {
                return { domain: "example.com" };
            }
        };
        getEntityDomainServiceStub.returns(dynamicEntityDomainServiceFake);

        const entity: any = { id: "test-entity" };
        const result = await buildDynamicLiveManagerUrl(entity);

        expect(result).to.deep.equal({
            url: "https://example.com",
            path: "/live-manager",
            socketPath: "",
        });
    });

    it("should return the correct URL and path for HTTP with custom port", async () => {
        config.liveManager = { schema: "http", port: 3000, path: "/custom-path", baseUrl: "" };
        const dynamicEntityDomainServiceFake: any = {
            get() {
                return { domain: "example.org" };
            }
        };
        getEntityDomainServiceStub.returns(dynamicEntityDomainServiceFake);

        const entity: any = { id: "test-entity" };
        const result = await buildDynamicLiveManagerUrl(entity);

        expect(result).to.deep.equal({
            url: "http://example.org:3000",
            path: "/custom-path",
            socketPath: "",
        });
    });

    it("should include the socketVersionPathLobby in the path if provided", async () => {
        const dynamicEntityDomainServiceFake: any = {
            get() {
                return { domain: "example.net" };
            }
        };
        getEntityDomainServiceStub.returns(dynamicEntityDomainServiceFake);

        const entity: any = { id: "test-entity" };
        const result = await buildDynamicLiveManagerUrl(entity, "v4");

        expect(result).to.deep.equal({
            url: "https://example.net",
            path: "/live-manager",
            socketPath: "/socket-v4",
        });
    });

    it("should return undefined if domain is not found", async () => {
        const dynamicEntityDomainServiceFake: any = {
            get(): any {
                return undefined;
            }
        };
        getEntityDomainServiceStub.returns(dynamicEntityDomainServiceFake);

        const entity: any = { id: "unknown-entity" };
        const result = await buildDynamicLiveManagerUrl(entity);

        expect(result).to.be.undefined;
    });

    it("should strip default HTTPS port 443", async () => {
        config.liveManager.port = 443;
        const dynamicEntityDomainServiceFake: any = {
            get() {
                return { domain: "example.com" };
            }
        };
        getEntityDomainServiceStub.returns(dynamicEntityDomainServiceFake);

        const entity: any = { id: "test-entity" };
        const result = await buildDynamicLiveManagerUrl(entity);

        expect(result.url).to.equal("https://example.com");
    });

    it("should strip default HTTP port 80", async () => {
        config.liveManager = { schema: "http", port: 80, path: "/socket.io", baseUrl: "" };
        const dynamicEntityDomainServiceFake: any = {
            get() {
                return { domain: "example.com" };
            }
        };
        getEntityDomainServiceStub.returns(dynamicEntityDomainServiceFake);

        const entity: any = { id: "test-entity" };
        const result = await buildDynamicLiveManagerUrl(entity);

        expect(result.url).to.equal("http://example.com");
    });
});
