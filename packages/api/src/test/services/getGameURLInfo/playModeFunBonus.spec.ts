import { expect, should, use } from "chai";
import * as sinon from "sinon";
import { FACTORY } from "../../factories/common";
import { factory } from "factory-girl";
import { EntityGame, Game } from "../../../skywind/entities/game";
import { PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import * as entityDomainService from "../../../skywind/services/entityDomainService";
import * as entityJrsdService from "../../../skywind/services/entityJurisdiction";
import { EntityHelper } from "../../../skywind/services/gameUrl/entityHelper";
import { ClientPayload, getGameURLInfo } from "../../../skywind/services/gameUrl/getGameURLInfo";
import { EmailTemplate, EntitySettings } from "../../../skywind/entities/settings";
import * as countrySourceService from "../../../skywind/utils/countrySource";
import { CountrySource } from "../../../skywind/utils/countrySource";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { truncate } from "../../entities/helper";

import chaiAsPromise = require("chai-as-promised");
should();
use(chaiAsPromise);

describe("getGameURLInfo - PlayMode.FUN_BONUS", function () {
    const language = "en";
    const emailTemplate: EmailTemplate = {
        from: "",
        subject: "",
        html: ""
    };
    const entitySettings: EntitySettings = {
        emailTemplates: {
            passwordRecovery: emailTemplate,
            changeEmail: emailTemplate
        },
        urlParams: {
            sound_popup: "true"
        },
        gameSplashes: {
            sw_csgo: "sw_pt",
            sw_pubg: "sw_pg"
        }
    };
    const game = {
        id: 1,
        code: "GAME001",
        url: "http://gc.gaming.skywindgroup.com/mrmonkey/latest/index.html" +
            "?startGameToken={startGameToken}" +
            "&url={dynamicDomain}/casino/game2" +
            "&language={lang}",
        gameProvider: {
            code: "GamePro"
        },
        providerGameCode: "ProGame",
        status: "normal",
        isLiveGame(): boolean {
            return false;
        }
    } as Game;
    const entityGame = {
        id: 1,
        entityId: 1,
        gameId: 1,
        parentEntityGameId: 1,
        game,
        isLiveGame(): boolean {
            return false;
        }
    } as EntityGame;
    const dynamicDomain = {
        domain: "http://localhost:4000"
    };
    const dynamicEntityDomainServiceFake = {
        get() {
            return dynamicDomain;
        }
    };
    const staticEntityDomainServiceFake = {
        get() {
            return undefined;
        }
    };

    let getEntityDomainServiceStub: sinon.SinonStub;
    let getAvailableLanguageStub: sinon.SinonStub;
    let getEntityJurisdictionServiceStub: sinon.SinonStub;
    let getIpCountrySourceStub: sinon.SinonStub;

    before(async () => {
        await truncate();
        getEntityDomainServiceStub = sinon.stub(entityDomainService, "getEntityDomainService");
        getAvailableLanguageStub = sinon.stub(EntityHelper, "getAvailableLanguage");
        getEntityJurisdictionServiceStub = sinon.stub(entityJrsdService, "getEntityJurisdictionService");
        getIpCountrySourceStub = sinon.stub(countrySourceService, "getIpCountrySource");
    });

    after(async () => {
        getEntityDomainServiceStub.restore();
        getAvailableLanguageStub.restore();
        getEntityJurisdictionServiceStub.restore();
        getIpCountrySourceStub.restore();
    });

    describe("PlayMode.FUN_BONUS", () => {
        let currenciesValueStub: sinon.SinonStub;
        let brand: Parameters<typeof getGameURLInfo>[0]["brand"];
        let player: Parameters<typeof getGameURLInfo>[0]["player"];

        beforeEach(async () => {
            currenciesValueStub = sinon.stub(Currencies, "value");
            brand = await factory.create(FACTORY.BRAND);
            player = await factory.create(FACTORY.PLAYER, { currency: "FUNUSD" });

            getEntityDomainServiceStub.reset();
            getEntityJurisdictionServiceStub.reset();
            getIpCountrySourceStub.reset();
            getAvailableLanguageStub.reset();

            getEntityDomainServiceStub.onCall(0).returns(dynamicEntityDomainServiceFake);
            getEntityDomainServiceStub.onCall(1).returns(staticEntityDomainServiceFake);
            getEntityJurisdictionServiceStub.returns({ findAll: () => [] });
            getAvailableLanguageStub.returns(language);
            getIpCountrySourceStub.returns(Promise.resolve<CountrySource>({
                whitelisted: true,
                restricted: false,
                reason: "",
                source: ""
            }));
        });

        afterEach(() => {
            currenciesValueStub.restore();
        });

        it("should convert playMode to FUN_BONUS when currency has funBonus property", async () => {
            currenciesValueStub.withArgs("FUNUSD").returns({
                name: "Fun USD",
                iso: "FUNUSD",
                funBonus: true
            });

            const result = await getGameURLInfo({
                entityGame: {
                    ...entityGame,
                    game: {
                        ...entityGame.game,
                        url: "http://test.game.com/{playmode}"
                    }
                },
                brand,
                entitySettings,
                disableLauncher: true,
                merchant: null,
                player,
                isLobby: false,
                request: {
                    playmode: PlayMode.REAL,
                    ip: "127.0.0.1"
                } as Partial<ClientPayload> as ClientPayload
            });

            expect(result.url).to.include("fun_bonus");
        });

        it("should not convert playMode when currency does not have funBonus property", async () => {
            currenciesValueStub.withArgs("USD").returns({
                name: "US Dollar",
                iso: "USD"
            });

            const result = await getGameURLInfo({
                entityGame: {
                    ...entityGame,
                    game: {
                        ...entityGame.game,
                        url: "http://test.game.com/{playmode}"
                    }
                },
                brand,
                entitySettings,
                disableLauncher: true,
                merchant: null,
                player,
                isLobby: false,
                request: {
                    playmode: PlayMode.REAL,
                    ip: "127.0.0.1"
                } as Partial<ClientPayload> as ClientPayload
            });

            expect(result.url).to.include("real");
            expect(result.url).to.not.include("fun_bonus");
        });

        it("should handle FUN_BONUS playMode when explicitly set", async () => {
            currenciesValueStub.withArgs("FUNUSD").returns({
                name: "Fun USD",
                iso: "FUNUSD",
                funBonus: true
            });

            const result = await getGameURLInfo({
                entityGame: {
                    ...entityGame,
                    game: {
                        ...entityGame.game,
                        url: "http://test.game.com/{playmode}"
                    }
                },
                brand,
                entitySettings,
                disableLauncher: true,
                merchant: null,
                player,
                isLobby: false,
                request: {
                    playmode: PlayMode.FUN_BONUS,
                    ip: "127.0.0.1"
                } as Partial<ClientPayload> as ClientPayload
            });

            expect(result.url).to.include("fun_bonus");
        });

        it("should convert FUN playMode to FUN_BONUS when currency has funBonus=true", async () => {
            currenciesValueStub.withArgs("FUNUSD").returns({
                name: "Fun USD",
                iso: "FUNUSD",
                funBonus: true
            });

            const result = await getGameURLInfo({
                entityGame: {
                    ...entityGame,
                    game: {
                        ...entityGame.game,
                        url: "http://test.game.com/{playmode}"
                    }
                },
                brand,
                entitySettings,
                disableLauncher: true,
                merchant: null,
                player,
                isLobby: false,
                request: {
                    playmode: PlayMode.FUN,
                    ip: "127.0.0.1"
                } as Partial<ClientPayload> as ClientPayload
            });

            expect(result.url).to.include("fun_bonus");
        });

        it("should not convert to FUN_BONUS when currency has funBonus=false", async () => {
            currenciesValueStub.withArgs("USD").returns({
                name: "US Dollar",
                iso: "USD",
                funBonus: false
            });

            const player = await factory.create(FACTORY.PLAYER, { currency: "USD" });

            const result = await getGameURLInfo({
                entityGame: {
                    ...entityGame,
                    game: {
                        ...entityGame.game,
                        url: "http://test.game.com/{playmode}"
                    }
                },
                brand,
                entitySettings,
                disableLauncher: true,
                merchant: null,
                player: player,
                isLobby: false,
                request: {
                    playmode: PlayMode.REAL,
                    ip: "127.0.0.1"
                } as Partial<ClientPayload> as ClientPayload
            });

            expect(result.url).to.include("real");
            expect(result.url).to.not.include("fun_bonus");
        });

        it("should handle currency that returns null from Currencies.value", async () => {
            currenciesValueStub.withArgs("UNKNOWN").returns(null);

            const player = await factory.create(FACTORY.PLAYER, { currency: "UNKNOWN" });

            const result = await getGameURLInfo({
                entityGame: {
                    ...entityGame,
                    game: {
                        ...entityGame.game,
                        url: "http://test.game.com/{playmode}"
                    }
                },
                brand,
                entitySettings,
                disableLauncher: true,
                merchant: null,
                player,
                isLobby: false,
                request: {
                    playmode: PlayMode.REAL,
                    ip: "127.0.0.1"
                } as Partial<ClientPayload> as ClientPayload
            });

            expect(result.url).to.include("real");
            expect(result.url).to.not.include("fun_bonus");
        });
    });
});
