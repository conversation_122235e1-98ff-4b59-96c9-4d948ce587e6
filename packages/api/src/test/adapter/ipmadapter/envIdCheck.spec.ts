import { suite } from "mocha-typescript";
import { should, use } from "chai";
import IpmIntegrationAdapter from "./ipmIntegrationAdapter.spec";
import { BrandEntity } from "../../../skywind/entities/brand";
import { BaseEntity, ChildEntity } from "../../../skywind/entities/entity";
import { SinonStub, stub } from "sinon";
import { Merchant } from "../../../skywind/entities/merchant";
import { createComplexStructure, setDynamicDomain, truncate } from "../../entities/helper";
import EntityCountryService from "../../../skywind/services/entityCountry";
import EntityFinance from "../../../skywind/services/entityFinance";
import { getDomainService } from "../../../skywind/services/domain";
import getEntityFactory from "../../../skywind/services/entityFactory";
import * as request from "request";
import * as SettingService from "../../../skywind/services/settings";

should();
use(require("chai-as-promised"));

@suite
class EnvIdCheck extends IpmIntegrationAdapter {
    protected brand: BrandEntity;
    protected master: BaseEntity;

    protected validateTicket: SinonStub;
    protected setting: SinonStub;
    protected domain;

    protected testMerchant: Merchant = {} as Merchant;

    public async before() {
        await super.before();
        await truncate();

        this.master = await createComplexStructure();

        const tle1 = this.master.find({ name: "TLE1" }) as ChildEntity;
        await (new EntityCountryService(tle1)).add(["US", "CN"]);
        await (new EntityFinance(tle1)).credit("USD", 10000);

        const tle1ent1 = this.master.find({ name: "ENT1" }) as ChildEntity;
        await (new EntityCountryService(tle1ent1)).add(["US", "CN"]);
        await (new EntityFinance(tle1ent1)).credit("USD", 10000);

        this.domain = await getDomainService().create({
            domain: "gc.gaming.skywindgroup.com",
            environment: "gc"
        });

        this.brand = await getEntityFactory(this.master.find({ path: ":TLE1:ENT1:" })).createBrand({
            name: "BRAND1",
            description: "BRAND1 description",
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            jurisdictionCode: "COM"
        });
        this.brand.addCountry("CN");
        await this.brand.save();

        await setDynamicDomain(this.brand, this.domain);

        await (new EntityFinance(this.brand)).credit("USD", 10000);

        this.testMerchant.code = "somecode";
        this.testMerchant.type = "ipm";
        this.testMerchant.brandId = this.brand.id;
        this.testMerchant.params = {
            serverUrl: "http://game.com",
            password: "pwd",
        };

        this.validateTicket = stub(request, "post");
        this.setting = stub(SettingService, "getEntitySettings");
    }

    public async after() {
        await super.after();
        this.validateTicket.restore();
        this.setting.restore();
    }

    public afterEach() {
        this.validateTicket.resetBehavior();
        this.setting.resetBehavior();
    }
}
